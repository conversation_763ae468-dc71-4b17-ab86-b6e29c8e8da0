package osquery

import "github.com/fatih/structs"

// booleanQuery represents the "bool" query from OpenSearch
// https://opensearch.org/docs/latest/query-dsl/compound/bool/
type booleanQuery struct {
	filter  []Mappable // Logical and operator that is applied first to reduce your dataset before applying the queries.
	must    []Mappable // Logical and operator. The results must match all queries in this clause.
	mustnot []Mappable // Logical not operator. All matches are excluded from the results.
	should  []Mappable // Logical or operator. The results must match at least one of the queries.
}

// <PERSON><PERSON> creates a default top-level boolean query
func Bool() *booleanQuery {
	return &booleanQuery{}
}

// Filter appends given filters to the query's filter clause
func (q *booleanQuery) Filter(filter ...Mappable) *booleanQuery {
	q.filter = append(q.filter, filter...)
	return q
}

// Must appends given must clauses to the query's must clause
func (q *booleanQuery) Must(must ...Mappable) *booleanQuery {
	q.must = append(q.must, must...)
	return q
}

// MustNot appends given must not clauses to the query's must not clause
func (q *booleanQuery) MustNot(mustnot ...Mappable) *booleanQuery {
	q.mustnot = append(q.mustnot, mustnot...)
	return q
}

// Should appends given should clauses to the query's should clause
func (q *booleanQuery) Should(should ...Mappable) *booleanQuery {
	q.should = append(q.should, should...)
	return q
}

// Map transforms the boolean query into an equal map representation
func (q *booleanQuery) Map() map[string]interface{} {
	var s struct {
		Filter  []map[string]any `structs:"filter,omitempty"`
		Must    []map[string]any `structs:"must,omitempty"`
		MustNot []map[string]any `structs:"must_not,omitempty"`
		Should  []map[string]any `structs:"should,omitempty"`
	}

	if len(q.filter) > 0 {
		s.Filter = make([]map[string]any, len(q.filter))
		for k, v := range q.filter {
			s.Filter[k] = v.Map()
		}
	}

	if len(q.must) > 0 {
		s.Must = make([]map[string]any, len(q.must))
		for k, v := range q.must {
			s.Must[k] = v.Map()
		}
	}

	if len(q.mustnot) > 0 {
		s.MustNot = make([]map[string]any, len(q.mustnot))
		for k, v := range q.mustnot {
			s.MustNot[k] = v.Map()
		}
	}

	if len(q.should) > 0 {
		s.Should = make([]map[string]any, len(q.should))
		for k, v := range q.should {
			s.Should[k] = v.Map()
		}
	}

	return map[string]interface{}{
		"bool": structs.Map(s),
	}
}
