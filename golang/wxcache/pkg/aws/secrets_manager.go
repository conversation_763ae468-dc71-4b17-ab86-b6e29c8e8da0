package llifaws

import (
	"context"
	"encoding/base64"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
)

type SecretManager interface {
	GetSecret(secretName string) string
}

type AWSSecretManager struct {
	region string
	client *secretsmanager.Client
}

type AWSApiKeysSecretOutput struct {
	AmbeeAPIKey          string `json:"AmbeeAPIKey"`
	WeatherAPIKey        string `json:"WeatherAPIKey"`
	VisualCrossingAPIKey string `json:"VisualCrossingAPIKey"`
}

type AWSSentryKeysSecretOutput struct {
	SentryDsn string `json:"MY_LLIF_DSN"`
}

func NewAWSSecretsManager(ctx context.Context, region string) (*AWSSecretManager, error) {
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion(region))
	if err != nil {
		return nil, err
	}

	return &AWSSecretManager{
		client: secretsmanager.NewFromConfig(cfg),
		region: region,
	}, nil
}

func (awssm *AWSSecretManager) GetValue(ctx context.Context, name string) (string, error) {
	input := awssm.getSecretValueInput(name)

	result, err := awssm.client.GetSecretValue(ctx, input)
	if err != nil {
		return "", err
	}

	// Decrypts secret using the associated KMS CMK.
	// Depending on whether the secret is a string or binary, one of these fields will be populated.
	if result.SecretString != nil {
		return *result.SecretString, nil
	}
	// Decodes the binary response into a string
	return awssm.decodeSecretValueBase64(result.SecretBinary)
}

func (awssm *AWSSecretManager) getSecretValueInput(name string) *secretsmanager.GetSecretValueInput {
	return &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(name),
	}
}

func (awssm *AWSSecretManager) decodeSecretValueBase64(input []byte) (string, error) {
	decodedBinarySecretBytes := make([]byte, base64.StdEncoding.DecodedLen(len(input)))

	length, err := base64.StdEncoding.Decode(decodedBinarySecretBytes, input)
	if err != nil {
		return "", err
	}
	return string(decodedBinarySecretBytes[:length]), nil
}
