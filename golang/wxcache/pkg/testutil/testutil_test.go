package testutil_test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/pkg/testutil"
)

func TestGetISOTimeFromStringShouldPass(t *testing.T) {
	var (
		timeInput = "2023-04-17T20:59:04Z"
		output    = testutil.GetISOTimeFromString(timeInput)
	)

	expectedOutput, err := time.Parse("2006-01-02T15:04:05Z", timeInput)
	require.NoError(t, err)

	require.Equal(t, expectedOutput, output)
}

func TestEqualMapShouldPass(t *testing.T) {
	tests := []struct {
		inputA         map[string]any
		inputB         map[string]any
		expectedResult bool
	}{
		{
			inputA:         map[string]any{"a": "b"},
			inputB:         map[string]any{"a": "b"},
			expectedResult: true,
		},
		{
			inputA:         map[string]any{"a": 1},
			inputB:         map[string]any{"a": "b"},
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		if testutil.EqualMap(tt.inputA, tt.inputB) != tt.expectedResult {
			t.Fatalf("input maps did not match expected result: inputA=%#v, inputB=%#v, expectedResult=%#v", tt.inputA, tt.inputB, tt.expectedResult)
		}
	}
}
