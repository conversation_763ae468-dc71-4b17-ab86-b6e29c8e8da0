package visualcrossing

import (
	"context"
	"log/slog"
	"time"

	"llif.org/wxcache/internal/ckey"
	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/pkg/wxtypes"
)

func normaliseWeather(ctx context.Context, res VisualCrossingWeatherResponse) ([]wxtypes.WeatherV2, error) {
	var (
		result     = make([]wxtypes.WeatherV2, 0)
		isBackfill = ctx.Value(ckey.Runtime) == ckey.RuntimeMsgProc
	)

	slog.DebugContext(ctx, "normalising visual crossing response", "days", len(res.Days))

	for _, day := range res.Days {
		for _, hour := range day.Hours {
			// Go by default uses system's IANA data, which means it does not recognise every single time zone out of the box.
			// We circumvent this by creating a dummy zone with our desired UTC offset.
			offsetInSeconds := int(res.TzOffset) * 60 * 60
			loc := time.FixedZone("", offsetInSeconds)

			// Combine date and time strings
			// day.DateTime is in format "2006-01-02" and hour.DateTime is in format "00:00:00"
			dateTime := day.DateTime + " " + hour.DateTime

			timestamp, err := time.ParseInLocation("2006-01-02 15:04:05", dateTime, loc)
			if err != nil {
				slog.WarnContext(ctx, "error parsing timestamp", "dateTime", dateTime, "err", err)
				continue
			}
			timestamp = timestamp.Truncate(time.Second).UTC()

			var (
				cloudCover    = convertor.FloatPtrToInt(hour.CloudCover)
				uvIndex       = convertor.FloatPtrToInt(hour.UVIndex)
				windDirection = getWindDirection(convertor.FloatNilPtrToZero(hour.WindDirection))
			)

			nw := wxtypes.WeatherV2{
				Timestamp: timestamp,
				Temperature: wxtypes.WeatherTemperature{
					Temperature: hour.Temperature,
					FeelsLike:   hour.FeelsLike,
				},
				Wind: wxtypes.WeatherWind{
					Speed:     hour.WindSpeed,
					Gust:      hour.WindGust,
					Degree:    hour.WindDirection,
					Direction: &windDirection,
				},
				Humidity:      hour.Humidity,
				CloudCover:    &cloudCover,
				UV:            &uvIndex,
				Pressure:      hour.Pressure,
				Visiblity:     hour.Visibility,
				Precipitation: hour.Precip,
				Coordinates: wxtypes.Coordinates{
					Latitude:  res.Latitude,
					Longitude: res.Longitude,
				},
				Metadata: wxtypes.Metadata{
					Provider: "visualcrossing",
				},
				SystemProperties: wxtypes.SystemProperties{
					CreatedAt: time.Now().UTC(),
					Backfill:  isBackfill,
				},
			}

			if err := wxtypes.ValidateStruct(nw); err != nil {
				slog.WarnContext(ctx, "could not validate weather struct", "struct", nw, "provider", "visualcrossing", "err", err)
				continue
			}

			result = append(result, nw)
		}
	}
	return result, nil
}

// getWindDirection converts wind direction in degrees to a compass direction
func getWindDirection(degrees float64) string {
	directions := []string{"N", "NNE", "NE", "ENE", "E", "ESE", "SE", "SSE", "S", "SSW", "SW", "WSW", "W", "WNW", "NW", "NNW"}
	index := int((degrees+11.25)/22.5) % 16
	return directions[index]
}
