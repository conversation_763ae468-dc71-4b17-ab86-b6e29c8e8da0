package visualcrossing

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestVisualCrossingNormaliseWeatherShouldPass(t *testing.T) {
	var (
		ctx            = context.Background()
		input          = getVisualCrossingWeatherResponse()
		expectedOutput = getVisualCrossingWeatherNormalisedResponse()
	)

	output, err := normaliseWeather(ctx, input)
	require.NoError(t, err)

	for i := 0; i < len(output); i++ {
		expectedOutput[i].SystemProperties.CreatedAt = output[i].SystemProperties.CreatedAt
	}
	require.Equal(t, expectedOutput, output)
}
