package breakpoint

var (
	breakpoint_US_O3 = []Boundary{
		{Low: 0, High: 54},
		{Low: 55, High: 70},
		{Low: 71, High: 164},
		{Low: 165, High: 204},
		{Low: 205, High: 404},
		{Low: 405, High: 504},
		{Low: 505, High: 604},
	}
	breakpoint_US_PM25 = []Boundary{
		{Low: 0, High: 12},
		{Low: 12.1, High: 35.4},
		{Low: 35.5, High: 55.4},
		{Low: 55.5, High: 150.4},
		{Low: 150.5, High: 250.4},
		{Low: 250.5, High: 350.4},
		{Low: 350.5, High: 500.4},
	}
	breakpoint_US_PM10 = []Boundary{
		{Low: 0, High: 54},
		{Low: 55, High: 154},
		{Low: 155, High: 254},
		{Low: 255, High: 354},
		{Low: 355, High: 424},
		{Low: 425, High: 504},
		{Low: 505, High: 604},
	}
	breakpoint_US_CO = []Boundary{
		{Low: 0, High: 4.4},
		{Low: 4.5, High: 9.4},
		{Low: 9.5, High: 12.4},
		{Low: 12.5, High: 15.4},
		{Low: 15.5, High: 30.4},
		{Low: 30.5, High: 40.4},
		{Low: 40.5, High: 50.4},
	}
	breakpoint_US_SO2 = []Boundary{
		{Low: 0, High: 35},
		{Low: 36, High: 75},
		{Low: 75, High: 185},
		{Low: 186, High: 304},
		{Low: 305, High: 604},
		{Low: 605, High: 804},
		{Low: 805, High: 1004},
	}
	breakpoint_US_NO2 = []Boundary{
		{Low: 0, High: 53},
		{Low: 54, High: 100},
		{Low: 101, High: 360},
		{Low: 361, High: 649},
		{Low: 650, High: 1249},
		{Low: 1250, High: 1649},
		{Low: 1650, High: 2049},
	}

	breakpointIndexTable_US = []Boundary{
		{Low: 0, High: 50},
		{Low: 51, High: 100},
		{Low: 101, High: 150},
		{Low: 151, High: 200},
		{Low: 201, High: 300},
		{Low: 301, High: 400},
		{Low: 401, High: 500},
	}
)

func getBreakpoints_US(t Type) []Boundary {
	switch t {
	case O3:
		return breakpoint_US_O3
	case PM25:
		return breakpoint_US_PM25
	case PM10:
		return breakpoint_US_PM10
	case CO:
		return breakpoint_US_CO
	case SO2:
		return breakpoint_US_SO2
	case NO2:
		return breakpoint_US_NO2
	default:
		panic("getBreakpoints_US panic'd")
	}
}

func getIndexBreakpointTable_US() []Boundary {
	return breakpointIndexTable_US
}
