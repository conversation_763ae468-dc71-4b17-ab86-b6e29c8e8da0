package aqi_test

import (
	"testing"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/internal/convertor"
	"llif.org/wxcache/pkg/aqi/v2"
)

func TestCalculateAQIShouldPass(t *testing.T) {
	tests := []struct {
		pm10 float64
		pm25 float64
		no2  float64
		so2  float64
		co   float64
		o3   float64
	}{
		{
			pm10: 55,
			pm25: 35,
			no2:  100,
			so2:  305,
			co:   9.5,
			o3:   400,
		},
	}

	for _, tt := range tests {
		var (
			pm10 = convertor.NumberToPtr(tt.pm10)
			pm25 = convertor.NumberToPtr(tt.pm25)
			no2  = convertor.NumberToPtr(tt.no2)
			so2  = convertor.NumberToPtr(tt.so2)
			co   = convertor.NumberToPtr(tt.co)
			o3   = convertor.NumberToPtr(tt.o3)

			concentration = aqi.AQIConcentration{
				PM10: pm10,
				PM25: pm25,
				NO2:  no2,
				SO2:  so2,
				CO:   co,
				O3:   o3,
			}
		)

		aqi, err := aqi.CalculateAQI(concentration)
		require.NoError(t, err)
		require.NotNil(t, aqi)
		require.NotNil(t, aqi.EUIndex)
		require.NotNil(t, aqi.USIndex)
		require.NotNil(t, aqi.GBIndex)
	}
}
