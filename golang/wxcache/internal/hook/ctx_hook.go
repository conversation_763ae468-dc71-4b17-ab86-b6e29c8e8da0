package hook

import (
	"context"
	"log/slog"

	"llif.org/wxcache/internal/ckey"
)

var _ slog.Handler = CtxHandler{}

type CtxHandler struct {
	handler slog.Handler
}

func NewCtxHandler(handler slog.Handler) slog.Handler {
	return CtxHandler{
		handler: handler,
	}
}

func (h CtxHandler) Enabled(ctx context.Context, level slog.Level) bool {
	return h.handler.Enabled(ctx, level)
}

func (h CtxHandler) Handle(ctx context.Context, record slog.Record) error {
	for _, k := range ckey.AllKeys() {
		v := ctx.Value(k)
		if v == nil {
			continue
		}
		record.AddAttrs(slog.Any(k.String(), v))
	}
	return h.handler.Handle(ctx, record)
}

func (h CtxHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return CtxHandler{h.handler.WithAttrs(attrs)}
}

func (h CtxHandler) WithGroup(name string) slog.Handler {
	return h.handler.WithGroup(name)
}
