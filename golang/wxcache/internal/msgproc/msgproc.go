package msgproc

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	llifaws "llif.org/wxcache/pkg/aws"
)

type MsgprocOpts struct {
	// The SQS service to be used to query for new messages
	SQSSvc *llifaws.SQS

	// The name of the queue to poll
	Queue string

	// A mapping of a topic of the message to the internal handler to receive the message
	Handlers map[string][]MessageHandler

	// How long to wait for messages before trying again
	PollTime int

	// How many messages to collect before processing them
	BulkFetchCount int
}

// A blocking function which reads messages of a specified queue in an infinite loop.
//
// It receives an instance of the SQS client, along with the queue name, and invokes
// all of the registered handlers, which were added via the MessageProcessor.Register() function
func Consume(ctx context.Context, opts MsgprocOpts) {
	// We need a non-zero value for both bulk fetch and poll time
	if opts.BulkFetchCount == 0 {
		opts.BulkFetchCount = 10
	}
	if opts.PollTime == 0 {
		opts.PollTime = 20
	}

	// Make sure we log panics so it doesn't crash without us knowing why.
	// This only works for functions directly called by this function.
	// If a function within Consume creates a goroutine, which then panics, this recover will not handle it.
	// It's good enough for now - mama
	defer func() {
		if r := recover(); r != nil {
			slog.ErrorContext(ctx, "recovered from asynchronous messaging panic", "err", r)

			// Re-raise panic so the system doesn't go into an undefined state
			panic(r)
		}
	}()
	slog.InfoContext(ctx, "started message consume", "queue", opts.Queue, "handler_count", len(opts.Handlers))

	for {
		if ctx.Err() != nil {
			slog.ErrorContext(ctx, "context for backfill is done", "err", ctx.Err())
			break
		}

		m, err := receiveMessages(ctx, opts.SQSSvc, opts.Queue, opts.BulkFetchCount, opts.PollTime)
		if err != nil {
			slog.ErrorContext(ctx, "could not receive messages from message queue", "err", err)
			time.Sleep(5 * time.Second)
			continue
		}

		// Skip processing if there are no polled messages from the queue
		if m == nil {
			continue
		}

		slog.DebugContext(ctx, "received messages", "queue", opts.Queue, "m", m.Messages)

		msgs := processMessageBulk(ctx, m.Messages, opts.Handlers)
		if len(msgs) == 0 {
			// No valid message has been processed given the registered handlers
			continue
		}

		// Run handlers for all messages
		receipts := invokeHandlersBulk(ctx, msgs, opts.Handlers)
		if len(receipts) == 0 {
			slog.ErrorContext(ctx, "no messages were processed correctly")
			continue
		}

		slog.InfoContext(ctx, "invoked handlers on messages", "successful", len(receipts), "total", len(msgs))

		// Delete messages from the queue that were successfully handled
		deleteMessagesBulk(ctx, opts.SQSSvc, opts.Queue, receipts)
	}
}

// Takes in an input of raw SQS messages and returns a slice of pointers to the normalised structures of the messages' bodies.
//
// The "handlers" map parameter represents a mapping of the MessageHandlers to the topic name to which the handlers are subscribed to
func processMessageBulk(ctx context.Context, msgs []types.Message, handlers map[string][]MessageHandler) []*llifaws.SQSMessageBody {
	var result []*llifaws.SQSMessageBody = make([]*llifaws.SQSMessageBody, 0, len(msgs))

	for _, msg := range msgs {
		message, err := processMessage(msg, handlers)
		if err != nil {
			slog.ErrorContext(ctx, "background message processor failed to process a message from the queue", "err", err, "msg", msg)
			continue
		}
		result = append(result, message)
	}
	return result
}

// Handles a single SQS message and processes it into a pointer to a SQSMessageBody struct.
// It also asserts if the given handlers parameter has a registered handler for the topic to which the
// specific SQS message belongs to.
//
// The "handlers" map parameter represents a mapping of the MessageHandlers to the topic name to which the handlers are subscribed to
func processMessage(m types.Message, handlers map[string][]MessageHandler) (*llifaws.SQSMessageBody, error) {
	message := llifaws.SQSMessageBody{
		ReceiptHandle: m.ReceiptHandle,
	}

	err := json.Unmarshal([]byte(*m.Body), &message)
	if err != nil {
		return nil, fmt.Errorf("could not unmarshal message's body to SQSMessageBody struct, got err=%s", err)
	}

	messageAttr, err := getMessageAttributeValue(message.MessageAttributes)
	if err != nil {
		return nil, fmt.Errorf("could not get key from message attribute, got err=%s", err)
	}

	if _, ok := handlers[messageAttr]; !ok {
		return nil, fmt.Errorf("received a message type=%s, but no handlers are registered", messageAttr)
	}
	return &message, nil
}

// Connects to the given queue with the passed in SQS instance and polls for messages. If there are no messages in the queue, the function returns nil, nil.
func receiveMessages(ctx context.Context, sqsSvc *llifaws.SQS, queue string, msgCount int, waitTime int) (*sqs.ReceiveMessageOutput, error) {
	m, err := sqsSvc.Receive(ctx, queue, msgCount, waitTime)
	if err != nil {
		return nil, err
	}

	// Skip processing if there are no messages in the response
	if len(m.Messages) == 0 {
		return nil, nil
	}
	return m, nil
}

// Receives the SQS instance, the normalised messages, and the handlers mapping to process each message for each registered handler.
// The function returns a slice of pointers to strings, which holds all of the message receipts for messages which were successfully processed.
// Each message is processed in a separate goroutine.
func invokeHandlersBulk(ctx context.Context, msgs []*llifaws.SQSMessageBody, handlers map[string][]MessageHandler) []*string {
	var (
		wg sync.WaitGroup

		processedMessagesReceiptsLock sync.Mutex
		// processedMessagesReceipts holds a slice of messages ready for deletion from the queue.
		processedMessagesReceipts []*string = make([]*string, 0, len(msgs))
	)

	// For now assume all messages in the slice have the same attributes
	messageAttr, _ := getMessageAttributeValue(msgs[0].MessageAttributes)

	for _, m := range msgs {
		wg.Add(1)

		go func(wg *sync.WaitGroup, m *llifaws.SQSMessageBody, h []MessageHandler) {
			defer wg.Done()

			// Process the message first
			if errs := invokeHandlers(ctx, m, h); len(errs) > 0 {
				slog.ErrorContext(ctx, "background message handlers could not process a message", "message", m.Message, "err_count", len(errs), "errs", errs)
				return // Don't mark as processed if there were errors
			}

			// Only mark as processed if handling was successful
			processedMessagesReceiptsLock.Lock()
			processedMessagesReceipts = append(processedMessagesReceipts, m.ReceiptHandle)
			processedMessagesReceiptsLock.Unlock()

		}(&wg, m, handlers[messageAttr])
	}
	wg.Wait()
	return processedMessagesReceipts
}

// Takes in the normalsied message as the input along with all of the handlers which will be used
// to process the message. Each handler receives their own goroutine to run in. The function waits for all
// of the goroutines to finish.
// It returns an error only if ALL of the handlers return an error
func invokeHandlers(ctx context.Context, msg *llifaws.SQSMessageBody, handlers []MessageHandler) []error {
	var (
		wg sync.WaitGroup

		errorsLock sync.Mutex
		errors     []error = make([]error, 0, len(handlers))
	)

	for _, h := range handlers {
		wg.Add(1)

		// Run each handler in a separate goroutine
		// If there is an error, add it to an outer scope errors slice
		go func(wg *sync.WaitGroup, h MessageHandler) {
			defer wg.Done()

			err := h.Handle(ctx, []byte(msg.Message))
			if err != nil {
				slog.WarnContext(ctx, "background message handler returned an error", "err", err, "message", msg.Message)

				errorsLock.Lock()
				errors = append(errors, err)
				errorsLock.Unlock()
			}
		}(&wg, h)
	}
	wg.Wait()
	return errors
}

// A helper function which takes in a slice of message receipts to go through and bulk delete all of those messages from the queue
func deleteMessagesBulk(ctx context.Context, sqsSvc *llifaws.SQS, queue string, receipts []*string) {
	var deleteCount int

	for _, r := range receipts {
		if r == nil {
			continue
		}

		_, err := sqsSvc.Delete(ctx, queue, *r)
		if err != nil {
			slog.ErrorContext(ctx, "cannot delete a message from a queue", "queue", queue, "err", err)
			continue
		}
		deleteCount++
	}
	slog.DebugContext(ctx, "succesfully deleted queue messages", "queue", queue, "count", deleteCount)
}
