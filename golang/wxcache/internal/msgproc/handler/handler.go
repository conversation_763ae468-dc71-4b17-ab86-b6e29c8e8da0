package handler

import (
	"context"
	"encoding/json"
	"log/slog"
	"time"

	"llif.org/wxcache/internal/msgproc"
	"llif.org/wxcache/internal/service"
	"llif.org/wxcache/pkg/wxtypes"
)

type LocationLoadedMessage struct {
	UserUUID string    `json:"user_uuid" validate:"required"`
	TimeFrom time.Time `json:"time_from" validate:"required"`
	TimeTo   time.Time `json:"time_to"`
	Lat      float64   `json:"lat" validate:"required"`
	Long     float64   `json:"long" validate:"required"`
	Type     string    `json:"type"`
}

type AirQualityMessageHandler struct {
	*slog.Logger

	svc service.EnvironmentService
}

var _ msgproc.MessageHandler = (*AirQualityMessageHandler)(nil)

func NewAirQualityMessageHandler(l *slog.Logger, svc service.EnvironmentService) *AirQualityMessageHandler {
	return &AirQualityMessageHandler{
		Logger: l,
		svc:    svc,
	}
}

func (h *AirQualityMessageHandler) Handle(ctx context.Context, data []byte) error {
	loc := LocationLoadedMessage{}
	err := json.Unmarshal(data, &loc)
	if err != nil {
		return err
	}

	if err := wxtypes.ValidateStruct(&loc); err != nil {
		return err
	}

	// TimeTo is an optional value in the response. If TimeTo is set to a default value
	// which is 0001-01-01 00:00:00 +0000 UTC then we create a new TimeTo value
	// with the current time and the timezone from the TimeFrom variable
	if loc.TimeTo.IsZero() {
		loc.TimeTo = time.Now().In(loc.TimeFrom.Location())
	}

	st := []wxtypes.SpaceTime{
		{
			Lat:      loc.Lat,
			Long:     loc.Long,
			TimeFrom: loc.TimeFrom,
			TimeTo:   loc.TimeTo,
		},
	}

	aq, err := h.svc.GetAirQuality(ctx, st)
	if err != nil {
		// @REFACTOR: for now we ignore the error and not expose it back to the message processing
		// to stop the backfill from retrying the message on a returned error.
		//
		// We will have to distinguish between errors, which could be resolved by trying the request again -- e.g. unavailable API
		// and errors which are going to return the same result if tried again -- e.g. invalid input, no data in provider
		h.ErrorContext(ctx, "asynchronous handler could not process a message from the queue", "data_type", "air_quality", "err", err)
		return nil
	}

	h.InfoContext(ctx, "asynchronously processed air quality entries", "count", len(aq))
	return nil
}

type WeatherMessageHandler struct {
	*slog.Logger

	svc service.EnvironmentService
}

var _ msgproc.MessageHandler = (*WeatherMessageHandler)(nil)

func NewWeatherMessageHandler(l *slog.Logger, svc service.EnvironmentService) *WeatherMessageHandler {
	return &WeatherMessageHandler{
		Logger: l,
		svc:    svc,
	}
}

func (h *WeatherMessageHandler) Handle(ctx context.Context, data []byte) error {
	loc := LocationLoadedMessage{}
	err := json.Unmarshal(data, &loc)
	if err != nil {
		return err
	}

	if err := wxtypes.ValidateStruct(&loc); err != nil {
		return err
	}

	// TimeTo is an optional value in the response. If TimeTo is set to a default value
	// which is 0001-01-01 00:00:00 +0000 UTC then we create a new TimeTo value
	// with the current time and the timezone from the TimeFrom variable
	if loc.TimeTo.IsZero() {
		loc.TimeTo = time.Now().In(loc.TimeFrom.Location())
	}

	st := []wxtypes.SpaceTime{
		{
			Lat:      loc.Lat,
			Long:     loc.Long,
			TimeFrom: loc.TimeFrom,
			TimeTo:   loc.TimeTo,
		},
	}

	w, err := h.svc.GetWeather(ctx, st)
	if err != nil {
		// @REFACTOR: for now we ignore the error and not expose it back to the message processing
		// to stop the backfill from retrying the message on a returned error.
		//
		// We will have to distinguish between errors, which could be resolved by trying the request again -- e.g. unavailable API
		// and errors which are going to return the same result if tried again -- e.g. invalid input, no data in provider
		h.ErrorContext(ctx, "asynchronous handler could not process a message from the queue", "data_type", "weather", "err", err)
		return nil
	}

	h.InfoContext(ctx, "asynchronously processed weather entries", "count", len(w))
	return nil
}

type PollenMessageHandler struct {
	*slog.Logger

	svc service.EnvironmentService
}

var _ msgproc.MessageHandler = (*PollenMessageHandler)(nil)

func NewPollenMessageHandler(l *slog.Logger, svc service.EnvironmentService) *PollenMessageHandler {
	return &PollenMessageHandler{
		Logger: l,
		svc:    svc,
	}
}

func (h *PollenMessageHandler) Handle(ctx context.Context, data []byte) error {
	loc := LocationLoadedMessage{}
	err := json.Unmarshal(data, &loc)
	if err != nil {
		return err
	}

	if err := wxtypes.ValidateStruct(&loc); err != nil {
		return err
	}

	// TimeTo is an optional value in the response. If TimeTo is set to a default value
	// which is 0001-01-01 00:00:00 +0000 UTC then we create a new TimeTo value
	// with the current time and the timezone from the TimeFrom variable
	if loc.TimeTo.IsZero() {
		loc.TimeTo = time.Now().In(loc.TimeFrom.Location())
	}

	st := []wxtypes.SpaceTime{
		{
			Lat:      loc.Lat,
			Long:     loc.Long,
			TimeFrom: loc.TimeFrom,
			TimeTo:   loc.TimeTo,
		},
	}

	p, err := h.svc.GetPollen(ctx, st)
	if err != nil {
		// @REFACTOR: for now we ignore the error and not expose it back to the message processing
		// to stop the backfill from retrying the message on a returned error.
		//
		// We will have to distinguish between errors, which could be resolved by trying the request again -- e.g. unavailable API
		// and errors which are going to return the same result if tried again -- e.g. invalid input, no data in provider
		h.ErrorContext(ctx, "asynchronous handler could not process a message from the queue", "data_type", "pollen", "err", err)
		return nil
	}

	h.InfoContext(ctx, "asynchronously processed pollen entries", "count", len(p))
	return nil
}
