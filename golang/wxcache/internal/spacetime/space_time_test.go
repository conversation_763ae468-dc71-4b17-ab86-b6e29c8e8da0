package spacetime

import (
	"sort"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"llif.org/wxcache/pkg/testutil"
	"llif.org/wxcache/pkg/testutil/location"
	"llif.org/wxcache/pkg/wxtypes"
)

func TestBucketSpaceTimeWithDeltaProperlyBucketsDateTime(t *testing.T) {
	var (
		beginTime = testutil.GetISOTimeFromString("2023-04-16T20:31:04Z")
		endTime   = testutil.GetISOTimeFromString("2023-04-16T23:59:04Z")
	)

	tests := []struct {
		input  []wxtypes.SpaceTime
		output []wxtypes.SpaceTime
	}{
		{
			input: []wxtypes.SpaceTime{
				{Lat: 0, Long: 0, TimeFrom: beginTime, TimeTo: endTime},
			},
			output: []wxtypes.SpaceTime{
				{
					Lat:      0,
					Long:     0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-16T20:31:04Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-16T21:00:00Z"),
				},
				{
					Lat:      0,
					Long:     0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-16T21:00:00Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-16T22:00:00Z"),
				},
				{
					Lat:      0,
					Long:     0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-16T22:00:00Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-16T23:00:00Z"),
				},
				{
					Lat:      0,
					Long:     0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-16T23:00:00Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-16T23:59:04Z"),
				},
			},
		},
	}

	for _, tt := range tests {
		sps := BucketSpaceTimeWithDelta(tt.input, time.Hour)
		require.Equal(t, tt.output, sps)
	}
}

func TestBucketSpaceTimeWithDeltaHandlesOneInput(t *testing.T) {
	var (
		beginTime = testutil.GetISOTimeFromString("2023-04-16T20:31:04Z")
		endTime   = testutil.GetISOTimeFromString("2023-04-16T20:59:04Z")
	)

	tests := []struct {
		input  []wxtypes.SpaceTime
		output []wxtypes.SpaceTime
	}{
		{
			input: []wxtypes.SpaceTime{
				{Lat: 0, Long: 0, TimeFrom: beginTime, TimeTo: endTime},
			},
			output: []wxtypes.SpaceTime{
				{
					Lat:      0,
					Long:     0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-16T20:31:04Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-16T20:59:04Z"),
				},
			},
		},
	}

	for _, tt := range tests {
		sps := BucketSpaceTimeWithDelta(tt.input, time.Hour)
		require.Equal(t, tt.output, sps)
	}
}

func TestBucketSpaceTimeWithDeltaHandlesTimeDifferenceExactlyOneHour(t *testing.T) {
	var (
		beginTime = testutil.GetISOTimeFromString("2023-04-16T20:31:04Z")
		endTime   = testutil.GetISOTimeFromString("2023-04-16T21:31:04Z")
	)

	tests := []struct {
		input  []wxtypes.SpaceTime
		output []wxtypes.SpaceTime
	}{
		{
			input: []wxtypes.SpaceTime{
				{Lat: 0, Long: 0, TimeFrom: beginTime, TimeTo: endTime},
			},
			output: []wxtypes.SpaceTime{
				{
					Lat:      0,
					Long:     0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-16T20:31:04Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-16T21:31:04Z"),
				},
			},
		},
	}

	for _, tt := range tests {
		sps := BucketSpaceTimeWithDelta(tt.input, time.Hour)
		require.Equal(t, tt.output, sps)
	}
}

func TestBucketSpaceTimeWithDeltaHandlesLargerTimeDelta(t *testing.T) {
	var (
		beginTime = testutil.GetISOTimeFromString("2023-04-01T20:31:04Z")
		endTime   = testutil.GetISOTimeFromString("2023-04-17T20:59:04Z")
	)

	tests := []struct {
		input  []wxtypes.SpaceTime
		output []wxtypes.SpaceTime
	}{
		{
			input: []wxtypes.SpaceTime{
				{Lat: 0, Long: 0, TimeFrom: beginTime, TimeTo: endTime},
			},
			output: []wxtypes.SpaceTime{
				{
					Lat:      0,
					Long:     0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-01T20:31:04Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-11T20:00:00Z"),
				},
				{
					Lat:      0,
					Long:     0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-11T20:00:00Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-17T20:59:04Z"),
				},
			},
		},
	}

	for _, tt := range tests {
		sps := BucketSpaceTimeWithDelta(tt.input, 10*(24*time.Hour))
		require.Equal(t, tt.output, sps)
	}
}

func TestBucketSpaceTimeWithDeltaDoesNotPanicOnEmptyInputShouldPass(t *testing.T) {
	input := []wxtypes.SpaceTime{}
	output := BucketSpaceTimeWithDelta(input, time.Minute)
	require.Equal(t, input, output)
}

func TestBucketSpaceTimeIntoDailyBucketsTurnsHourlyBucketsToDaily(t *testing.T) {
	var (
		input = []wxtypes.SpaceTime{
			{
				TimeFrom: testutil.GetISOTimeFromString("2024-01-01T00:00:00Z"),
				TimeTo:   testutil.GetISOTimeFromString("2024-01-01T01:00:00Z"),
			},
			{
				TimeFrom: testutil.GetISOTimeFromString("2024-01-01T01:00:00Z"),
				TimeTo:   testutil.GetISOTimeFromString("2024-01-01T02:00:00Z"),
			},
			{
				TimeFrom: testutil.GetISOTimeFromString("2024-01-01T02:00:00Z"),
				TimeTo:   testutil.GetISOTimeFromString("2024-01-01T03:00:00Z"),
			},
			{
				TimeFrom: testutil.GetISOTimeFromString("2024-01-02T00:00:00Z"),
				TimeTo:   testutil.GetISOTimeFromString("2024-01-02T01:00:00Z"),
			},
			{
				TimeFrom: testutil.GetISOTimeFromString("2024-01-03T00:00:00Z"),
				TimeTo:   testutil.GetISOTimeFromString("2024-01-03T01:00:00Z"),
			},
			{
				TimeFrom: testutil.GetISOTimeFromString("2024-01-03T01:00:00Z"),
				TimeTo:   testutil.GetISOTimeFromString("2024-01-03T02:00:00Z"),
			},
		}
		expectedOutput = []wxtypes.SpaceTime{
			{
				TimeFrom: testutil.GetISOTimeFromString("2024-01-01T00:00:00Z"),
				TimeTo:   testutil.GetISOTimeFromString("2024-01-01T03:00:00Z"),
			},
			{
				TimeFrom: testutil.GetISOTimeFromString("2024-01-02T00:00:00Z"),
				TimeTo:   testutil.GetISOTimeFromString("2024-01-02T01:00:00Z"),
			},
			{
				TimeFrom: testutil.GetISOTimeFromString("2024-01-03T00:00:00Z"),
				TimeTo:   testutil.GetISOTimeFromString("2024-01-03T02:00:00Z"),
			},
		}
	)

	output := BucketSpaceTimeIntoDailyBuckets(input)
	require.Equal(t, expectedOutput, output)
}

func TestBucketSpaceTimeToEuropeShouldPass(t *testing.T) {
	inputSpaceTime := []wxtypes.SpaceTime{
		// Paris
		{
			Lat:      48.86,
			Long:     2.34,
			TimeFrom: testutil.GetISOTimeFromString("2023-04-17T20:59:04Z"),
			TimeTo:   testutil.GetISOTimeFromString("2023-04-17T20:59:04Z"),
		},
		// San Diego
		{
			Lat:      32.72,
			Long:     -117.17,
			TimeFrom: testutil.GetISOTimeFromString("2023-04-17T20:59:04Z"),
			TimeTo:   testutil.GetISOTimeFromString("2023-04-17T20:59:04Z"),
		},
		// Prague
		{
			Lat:      50.07,
			Long:     14.43,
			TimeFrom: testutil.GetISOTimeFromString("2023-04-17T20:59:04Z"),
			TimeTo:   testutil.GetISOTimeFromString("2023-04-17T20:59:04Z"),
		},
		// New York
		{
			Lat:      40.71,
			Long:     -74.00,
			TimeFrom: testutil.GetISOTimeFromString("2023-04-17T20:59:04Z"),
			TimeTo:   testutil.GetISOTimeFromString("2023-04-17T20:59:04Z"),
		},
	}

	// Assign values from the inputSpaceTime slice to their individual buckets
	expectedEuropeSpaceTime := []wxtypes.SpaceTime{inputSpaceTime[0], inputSpaceTime[2]}
	expectedWorldSpaceTime := []wxtypes.SpaceTime{inputSpaceTime[1], inputSpaceTime[3]}

	outputEurope, outputWorld := BucketSpaceTimeToEurope(inputSpaceTime)

	require.Equal(t, expectedEuropeSpaceTime, outputEurope)
	require.Equal(t, expectedWorldSpaceTime, outputWorld)
}

func TestSpreadToDailyTimeBucketsShouldPass(t *testing.T) {
	tests := []struct {
		startTime     time.Time
		endTime       time.Time
		expectedCount int
	}{
		{
			startTime:     time.Now().Add(-time.Hour * 24 * 6).Truncate(time.Second), // 7 days ago
			endTime:       time.Now().Truncate(time.Second),
			expectedCount: 7,
		},
		{
			startTime:     time.Now().Add(-time.Hour).Truncate(time.Second), // Same day, an hour ago
			endTime:       time.Now().Truncate(time.Second),
			expectedCount: 1,
		},
	}

	for _, tt := range tests {
		result := SpreadToDailyTimeBuckets(tt.startTime, tt.endTime)
		require.Equal(t, tt.expectedCount, len(result))

		var (
			firstBucket = result[0]
			lastBucket  = result[len(result)-1]
		)

		require.Equal(t, tt.startTime, firstBucket)

		// Only check for matching end time if the bucket spans over 1 day since for a single day we only return one time bucket
		if len(result) > 1 {
			require.Equal(t, tt.endTime, lastBucket)
		}
	}
}

// Helper function to compare two slices of SpaceTime regardless of order
func compareSpaceTimeSlices(t *testing.T, result, expected []wxtypes.SpaceTime) {
	if len(result) != len(expected) {
		t.Errorf("expected %d buckets, got %d", len(expected), len(result))
		return
	}

	// Sort both slices by a consistent key (lat, long, then time)
	sortSpaceTime := func(st []wxtypes.SpaceTime) {
		sort.Slice(st, func(i, j int) bool {
			if st[i].Lat != st[j].Lat {
				return st[i].Lat < st[j].Lat
			}
			if st[i].Long != st[j].Long {
				return st[i].Long < st[j].Long
			}
			return st[i].TimeFrom.Before(st[j].TimeFrom)
		})
	}

	sortSpaceTime(result)
	sortSpaceTime(expected)

	for i, exp := range expected {
		if result[i].Lat != exp.Lat {
			t.Errorf("bucket %d: expected lat %f, got %f", i, exp.Lat, result[i].Lat)
		}
		if result[i].Long != exp.Long {
			t.Errorf("bucket %d: expected long %f, got %f", i, exp.Long, result[i].Long)
		}
		if !result[i].TimeFrom.Equal(exp.TimeFrom) {
			t.Errorf("bucket %d: expected TimeFrom %v, got %v", i, exp.TimeFrom, result[i].TimeFrom)
		}
		if !result[i].TimeTo.Equal(exp.TimeTo) {
			t.Errorf("bucket %d: expected TimeTo %v, got %v", i, exp.TimeTo, result[i].TimeTo)
		}
	}
}

func TestBucketSpaceTimeByLocationAndDate(t *testing.T) {
	tests := []struct {
		name     string
		input    []wxtypes.SpaceTime
		expected []wxtypes.SpaceTime
	}{
		{
			name:     "empty input",
			input:    []wxtypes.SpaceTime{},
			expected: []wxtypes.SpaceTime{},
		},
		{
			name: "single entry",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
			},
			expected: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			name: "same location, same day, different times",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 14, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 16, 0, 0, 0, time.UTC),
				},
			},
			expected: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 16, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			name: "same location, different days",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 21, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 21, 12, 0, 0, 0, time.UTC),
				},
			},
			expected: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 21, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 21, 12, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			name: "different locations, same day",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.London.Lat,
					Long:     location.London.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
			},
			expected: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.London.Lat,
					Long:     location.London.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			name: "complex case with multiple locations and days",
			input: []wxtypes.SpaceTime{
				// New York, March 20
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 14, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 16, 0, 0, 0, time.UTC),
				},
				// London, March 20
				{
					Lat:      location.London.Lat,
					Long:     location.London.Long,
					TimeFrom: time.Date(2024, 3, 20, 9, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 11, 0, 0, 0, time.UTC),
				},
				// New York, March 21
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 21, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 21, 12, 0, 0, 0, time.UTC),
				},
			},
			expected: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 16, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 21, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 21, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.London.Lat,
					Long:     location.London.Long,
					TimeFrom: time.Date(2024, 3, 20, 9, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 11, 0, 0, 0, time.UTC),
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BucketSpaceTimeByLocationAndDate(tt.input)
			compareSpaceTimeSlices(t, result, tt.expected)
		})
	}
}

func TestBucketSpaceTimeByLocationAndMergeDates(t *testing.T) {
	tests := []struct {
		name     string
		input    []wxtypes.SpaceTime
		expected []wxtypes.SpaceTime
	}{
		{
			name:     "empty input",
			input:    []wxtypes.SpaceTime{},
			expected: []wxtypes.SpaceTime{},
		},
		{
			name: "single entry",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
			},
			expected: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			name: "same location, consecutive days - should merge",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 21, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 21, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 22, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 22, 12, 0, 0, 0, time.UTC),
				},
			},
			expected: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 22, 12, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			name: "same location, non-consecutive days - should not merge",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 22, 10, 0, 0, 0, time.UTC), // Gap of 1 day
					TimeTo:   time.Date(2024, 3, 22, 12, 0, 0, 0, time.UTC),
				},
			},
			expected: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 22, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 22, 12, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			name: "different locations, same consecutive days - should not merge",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.London.Lat,
					Long:     location.London.Long,
					TimeFrom: time.Date(2024, 3, 21, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 21, 12, 0, 0, 0, time.UTC),
				},
			},
			expected: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.London.Lat,
					Long:     location.London.Long,
					TimeFrom: time.Date(2024, 3, 21, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 21, 12, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			name: "complex case with mixed consecutive and non-consecutive dates",
			input: []wxtypes.SpaceTime{
				// New York - consecutive days (20, 21, 22)
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 20, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 21, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 21, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 22, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 22, 12, 0, 0, 0, time.UTC),
				},
				// London - single day
				{
					Lat:      location.London.Lat,
					Long:     location.London.Long,
					TimeFrom: time.Date(2024, 3, 21, 9, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 21, 11, 0, 0, 0, time.UTC),
				},
				// New York - non-consecutive day (25)
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 25, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 25, 12, 0, 0, 0, time.UTC),
				},
			},
			expected: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 22, 12, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.London.Lat,
					Long:     location.London.Long,
					TimeFrom: time.Date(2024, 3, 21, 9, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 21, 11, 0, 0, 0, time.UTC),
				},
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2024, 3, 25, 10, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2024, 3, 25, 12, 0, 0, 0, time.UTC),
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BucketSpaceTimeByLocationAndMergeDates(tt.input)
			compareSpaceTimeSlices(t, result, tt.expected)
		})
	}
}

// TestBucketSpaceTimeWithDeltaEdgeCases tests edge cases that could cause data loss
func TestBucketSpaceTimeWithDeltaEdgeCases(t *testing.T) {
	tests := []struct {
		name          string
		input         []wxtypes.SpaceTime
		delta         time.Duration
		expectedCount int
		description   string
	}{
		{
			name: "9 hour range should produce 9 buckets",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2025, 6, 24, 0, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2025, 6, 24, 9, 0, 0, 0, time.UTC), // Exactly 9 hours
				},
			},
			delta:         time.Hour,
			expectedCount: 9,
			description:   "9-hour span should create 9 hourly buckets (0-1, 1-2, ..., 8-9)",
		},
		{
			name: "8 hour 59 min range edge case",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2025, 6, 24, 0, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2025, 6, 24, 8, 59, 0, 0, time.UTC), // 8 hours 59 minutes
				},
			},
			delta:         time.Hour,
			expectedCount: 9,
			description:   "8h59m span should still create 9 buckets due to truncation logic",
		},
		{
			name: "non aligned start time 9 hours",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2025, 6, 24, 0, 15, 30, 0, time.UTC), // 00:15:30
					TimeTo:   time.Date(2025, 6, 24, 9, 15, 30, 0, time.UTC), // 09:15:30 (exactly 9 hours)
				},
			},
			delta:         time.Hour,
			expectedCount: 10, // Changed from 9 to 10 - non-aligned times create partial buckets at start and end
			description:   "Non-aligned 9-hour span should create 10 buckets (partial at start and end)",
		},
		{
			name: "cross midnight boundary",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2025, 6, 23, 20, 0, 0, 0, time.UTC), // 20:00
					TimeTo:   time.Date(2025, 6, 24, 5, 0, 0, 0, time.UTC),  // 05:00 next day (9 hours)
				},
			},
			delta:         time.Hour,
			expectedCount: 9,
			description:   "9-hour span crossing midnight should create 9 buckets",
		},
		{
			name: "partial hour at end",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2025, 6, 24, 0, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2025, 6, 24, 8, 30, 0, 0, time.UTC), // 8.5 hours
				},
			},
			delta:         time.Hour,
			expectedCount: 9,
			description:   "8.5-hour span should create 9 buckets (last bucket partial)",
		},
		{
			name: "48 hour range should produce 48 buckets",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2025, 6, 24, 0, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2025, 6, 26, 0, 0, 0, 0, time.UTC), // Exactly 48 hours (2 days)
				},
			},
			delta:         time.Hour,
			expectedCount: 48,
			description:   "48-hour span should create exactly 48 hourly buckets",
		},
		{
			name: "single minute range",
			input: []wxtypes.SpaceTime{
				{
					Lat:      location.NewYork.Lat,
					Long:     location.NewYork.Long,
					TimeFrom: time.Date(2025, 6, 24, 0, 0, 0, 0, time.UTC),
					TimeTo:   time.Date(2025, 6, 24, 0, 1, 0, 0, time.UTC), // 1 minute
				},
			},
			delta:         time.Hour,
			expectedCount: 1,
			description:   "1-minute span should create 1 bucket (within delta threshold)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BucketSpaceTimeWithDelta(tt.input, tt.delta)

			if len(result) != tt.expectedCount {
				t.Errorf("Test: %s\nDescription: %s\nExpected %d buckets, got %d buckets\nInput: %+v\nResult: %+v",
					tt.name, tt.description, tt.expectedCount, len(result), tt.input[0], result)
			}

			// Verify that the total time span is preserved
			if len(result) > 0 {
				totalInputDuration := tt.input[0].TimeTo.Sub(tt.input[0].TimeFrom)
				totalResultDuration := result[len(result)-1].TimeTo.Sub(result[0].TimeFrom)

				if totalInputDuration != totalResultDuration {
					t.Errorf("Test: %s\nTime span not preserved. Input duration: %v, Result duration: %v",
						tt.name, totalInputDuration, totalResultDuration)
				}
			}
		})
	}
}

func TestBucketSpaceTimeWithDeltaBoundaryConditions(t *testing.T) {
	t.Run("hour boundary alignment", func(t *testing.T) {
		input := []wxtypes.SpaceTime{
			{
				Lat:      -15.7975, // Brasilia coordinates from the failing test
				Long:     -47.8919,
				TimeFrom: time.Date(2025, 6, 24, 0, 0, 0, 0, time.FixedZone("BRT", -4*3600)), // -04:00 timezone
				TimeTo:   time.Date(2025, 6, 24, 8, 0, 0, 0, time.FixedZone("BRT", -4*3600)), // 8 hours later
			},
		}

		result := BucketSpaceTimeWithDelta(input, time.Hour)

		// Should create buckets: 0-1, 1-2, 2-3, 3-4, 4-5, 5-6, 6-7, 7-8 = 8 buckets
		// But if we want 9 buckets for a 9-hour request, the TimeTo should be 9:00
		expectedBuckets := 8

		if len(result) != expectedBuckets {
			t.Errorf("Expected %d buckets for 8-hour span, got %d buckets", expectedBuckets, len(result))
			for i, bucket := range result {
				t.Logf("Bucket %d: %v to %v", i, bucket.TimeFrom, bucket.TimeTo)
			}
		}
	})

	t.Run("timezone handling", func(t *testing.T) {
		// Test with different timezones to ensure bucketing works correctly
		timezones := []*time.Location{
			time.UTC,
			time.FixedZone("EST", -5*3600), // -05:00
			time.FixedZone("BRT", -4*3600), // -04:00 (like in the failing test)
			time.FixedZone("JST", 9*3600),  // +09:00
		}

		for _, tz := range timezones {
			t.Run(tz.String(), func(t *testing.T) {
				input := []wxtypes.SpaceTime{
					{
						Lat:      location.NewYork.Lat,
						Long:     location.NewYork.Long,
						TimeFrom: time.Date(2025, 6, 24, 0, 0, 0, 0, tz),
						TimeTo:   time.Date(2025, 6, 24, 9, 0, 0, 0, tz), // 9 hours
					},
				}

				result := BucketSpaceTimeWithDelta(input, time.Hour)

				if len(result) != 9 {
					t.Errorf("Timezone %s: Expected 9 buckets, got %d", tz.String(), len(result))
				}
			})
		}
	})
}

func TestValidateSpaceTimeOrder(t *testing.T) {
	tests := []struct {
		name        string
		input       []wxtypes.SpaceTime
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid time ranges",
			input: []wxtypes.SpaceTime{
				{
					Lat:      1.0,
					Long:     2.0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-16T10:00:00Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-16T11:00:00Z"),
				},
				{
					Lat:      3.0,
					Long:     4.0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-16T12:00:00Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-16T13:00:00Z"),
				},
			},
			expectError: false,
		},
		{
			name: "equal time ranges",
			input: []wxtypes.SpaceTime{
				{
					Lat:      1.0,
					Long:     2.0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-16T10:00:00Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-16T10:00:00Z"),
				},
			},
			expectError: false,
		},
		{
			name: "invalid time range - TimeFrom after TimeTo",
			input: []wxtypes.SpaceTime{
				{
					Lat:      1.0,
					Long:     2.0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-16T11:00:00Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-16T10:00:00Z"),
				},
			},
			expectError: true,
			errorMsg:    "invalid time range at index 0",
		},
		{
			name: "mixed valid and invalid time ranges",
			input: []wxtypes.SpaceTime{
				{
					Lat:      1.0,
					Long:     2.0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-16T10:00:00Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-16T11:00:00Z"),
				},
				{
					Lat:      3.0,
					Long:     4.0,
					TimeFrom: testutil.GetISOTimeFromString("2023-04-16T13:00:00Z"),
					TimeTo:   testutil.GetISOTimeFromString("2023-04-16T12:00:00Z"), // Invalid: TimeFrom after TimeTo
				},
			},
			expectError: true,
			errorMsg:    "invalid time range at index 1",
		},
		{
			name:        "empty slice",
			input:       []wxtypes.SpaceTime{},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateSpaceTimeOrder(tt.input)

			if tt.expectError {
				require.Error(t, err)
				require.Contains(t, err.Error(), tt.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
