from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.shared_v2 import DeprEventModel
from services.data_service.api.models.output.annotated_api_outputs import EventAPIOutput
from services.data_service.type_resolver import TypeResolver


class EventAPIOutputMapper:
    @classmethod
    def map(cls, document: Event | DeprEventModel) -> EventAPIOutput:
        output_schema = TypeResolver.get_event_api_output(type_id=document.type_id())
        return output_schema(**document.model_dump(by_alias=True) | {DocumentLabels.TYPE: document.type_id()})
