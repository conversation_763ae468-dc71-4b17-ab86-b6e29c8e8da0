import pytest

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.event_type import EventType
from services.base.domain.schemas.events.event import EventFields
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionFields
from services.base.domain.schemas.events.symptom import SymptomFields
from services.base.domain.schemas.query.aggregations import (
    AggregationMethod,
    DateHistogramAggregation,
    FieldAggregation,
)
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException
from services.data_service.api.models.request.aggregate.date_histogram_api_request_input import (
    DataHistogramAPIRequestInput,
)
from services.data_service.api.queries.event_query_api import EventTypedQueryAPI


class TestDateHistogramAPIRequestInput:

    @pytest.mark.parametrize(
        "queries, field_name",
        [
            ([], EventFields.NAME),
            ([EventTypedQueryAPI(types=[EventType.Symptom], query=None)], DocumentLabels.TIMESTAMP),
        ],
    )
    def test_validate_field_type_raises(self, queries, field_name):
        with pytest.raises(QueryValidationException):
            DataHistogramAPIRequestInput(
                queries=queries,
                aggregation=DateHistogramAggregation(
                    default_aggregation_method=AggregationMethod.SUM,
                    histogram_field_aggregations=[
                        FieldAggregation(field_name=field_name, aggregation_method=AggregationMethod.SUM)
                    ],
                    interval="1d",
                ),
            )

    @pytest.mark.parametrize(
        "queries, field_name",
        [
            ([EventTypedQueryAPI(types=[EventType.Symptom, EventType.Emotion], query=None)], SymptomFields.RATING),
            ([EventTypedQueryAPI(types=[EventType.Food, EventType.Drink], query=None)], NutritionFields.CALORIES),
        ],
    )
    def test_validate_field_type_passes(self, queries, field_name):
        DataHistogramAPIRequestInput(
            queries=queries,
            aggregation=DateHistogramAggregation(
                default_aggregation_method=AggregationMethod.SUM,
                histogram_field_aggregations=[
                    FieldAggregation(field_name=field_name, aggregation_method=AggregationMethod.SUM)
                ],
                interval="1d",
            ),
        )
