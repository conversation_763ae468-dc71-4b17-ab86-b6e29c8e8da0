from typing import Annotated

from pydantic import Field

from services.base.domain.constants.document_labels import DocumentLabels
from services.data_service.api.models.response.event.event_v3_api_output import EventGroupAPIOutput
from services.data_service.type_resolver import TypeResolver

GroupV3APIOutput = EventGroupAPIOutput  # TODO Remove when we have base class (used only in tests)
EventV3APIOutput = Annotated[TypeResolver.EVENT_API_OUTPUTS_V3_UNION, Field(discriminator=DocumentLabels.TYPE)]
EventAPIOutput = Annotated[TypeResolver.EVENT_API_OUTPUTS_UNION, Field(discriminator=DocumentLabels.TYPE)]
RecordAPIOutput = Annotated[TypeResolver.RECORD_API_OUTPUTS_UNION, Field(discriminator=DocumentLabels.TYPE)]
PlanAPIOutput = Annotated[TypeResolver.PLAN_API_OUTPUTS_UNION, Field(discriminator=DocumentLabels.TYPE)]
TemplateAPIOutput = Annotated[TypeResolver.TEMPLATE_API_OUTPUTS_UNION, Field(discriminator=DocumentLabels.TYPE)]

DocumentAPIOutput = Annotated[TypeResolver.DOCUMENT_API_OUTPUTS_UNION, Field(discriminator=DocumentLabels.TYPE)]
