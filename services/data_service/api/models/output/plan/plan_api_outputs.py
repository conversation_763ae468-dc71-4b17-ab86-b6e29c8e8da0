from typing import Literal
from uuid import UUID

from pydantic import Field, field_validator

from services.base.domain.annotated_types import (
    NonEmptyStr,
    SerializableAwareDatetime,
    UniqueSequenceStr,
)
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.custom_rrule import CustomRRule
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.priority import Priority
from services.base.domain.schemas.events.document_base import DocumentValueLimits
from services.base.domain.schemas.plan.goal import GoalCondition, GoalFields, GoalIdentifier
from services.base.domain.schemas.plan.plan import PlanFields, PlanIdentifier
from services.base.domain.schemas.plan.plan_base import (
    PlanBaseFields,
    PlanBaseMetadata,
    PlanBaseValueLimits,
    Streak,
)
from services.data_service.api.models.output.shared.identifiable_document_api_output import (
    IdentifiableDocumentAPIOutput,
)
from services.data_service.api.models.output.shared.system_properties_document_api_output import (
    SystemPropertiesDocumentAPIOutput,
)


class PlanAPIOutputBase(SystemPropertiesDocumentAPIOutput, IdentifiableDocumentAPIOutput):
    metadata: PlanBaseMetadata = Field(alias=PlanBaseFields.METADATA)
    name: NonEmptyStr = Field(alias=PlanBaseFields.NAME, min_length=1, max_length=PlanBaseValueLimits.MAX_NAME_LENGTH)
    next_scheduled_at: SerializableAwareDatetime = Field(alias=PlanBaseFields.NEXT_SCHEDULED_AT)
    streak: Streak = Field(alias=PlanBaseFields.STREAK)
    current_completed: int = Field(alias=PlanBaseFields.CURRENT_COMPLETED, ge=0)
    total_cycles: int = Field(alias=PlanBaseFields.TOTAL_CYCLES, ge=0)
    tags: UniqueSequenceStr = Field(alias=DocumentLabels.TAGS, max_length=DocumentValueLimits.MaxTagsCount)
    archived_at: SerializableAwareDatetime | None = Field(alias=PlanBaseFields.ARCHIVED_AT)
    recurrence: NonEmptyStr | None = Field(alias=PlanBaseFields.RECURRENCE, min_length=1)
    note: NonEmptyStr | None = Field(alias=PlanBaseFields.NOTE, min_length=1)
    max_completed: int | None = Field(alias=PlanBaseFields.MAX_COMPLETED, ge=0)

    @field_validator(PlanFields.RECURRENCE, mode="before")
    def validate_recurrence(cls, recurrence: CustomRRule | str | None) -> str | None:
        return str(recurrence) if recurrence else None


class PlanAPIOutput(PlanAPIOutputBase, PlanIdentifier):
    type: Literal[DataType.Plan] = Field(alias=DocumentLabels.TYPE)
    is_urgent: bool = Field(alias=PlanFields.IS_URGENT)
    is_confirmation_required: bool = Field(alias=PlanFields.IS_CONFIRMATION_REQUIRED)
    is_absolute_schedule: bool = Field(alias=PlanFields.IS_ABSOLUTE_SCHEDULE)
    priority: Priority = Field(alias=PlanFields.PRIORITY)
    prompt: NonEmptyStr = Field(alias=PlanFields.PROMPT, min_length=1)
    template_id: UUID = Field(alias=PlanFields.TEMPLATE_ID)
    total_completed_on_time: int = Field(alias=PlanFields.TOTAL_COMPLETED_ON_TIME, ge=0)
    first_completed_at: SerializableAwareDatetime | None = Field(alias=PlanFields.FIRST_COMPLETED_AT)

    @field_validator(PlanFields.RECURRENCE, mode="before")
    def validate_recurrence(cls, recurrence: CustomRRule | str | None) -> str | None:
        return str(recurrence) if recurrence else None


class GoalAPIOutput(PlanAPIOutputBase, GoalIdentifier):
    type: Literal[DataType.Goal] = Field(alias=DocumentLabels.TYPE)
    condition: GoalCondition = Field(alias=GoalFields.CONDITION)
    current_value: float = Field(alias=GoalFields.CURRENT_VALUE, ge=0)
