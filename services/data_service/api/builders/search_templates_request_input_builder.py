from typing import Self

from services.base.api.request_input.sort_request_input import SortRequestInput
from services.data_service.api.enums.template_type import TemplateType
from services.data_service.api.queries.template_query_api import TemplateQueryAPI, TemplateTypedQueryAPI


class SearchTemplatesRequestInputBuilder:
    def __init__(self):
        self._sort: SortRequestInput | None = None
        self._limit: int | None = None
        self._query: TemplateQueryAPI | None = None

    def with_limit(self, limit: int) -> Self:
        self._limit = limit
        return self

    def with_sort(self, sort: SortRequestInput) -> Self:
        self._sort = sort
        return self

    def with_query(self, query: TemplateQueryAPI) -> Self:
        self._query = query
        return self

    def build_body_as_dict(self) -> dict:
        sort_as_dict = {"sort": self._sort.model_dump(by_alias=True, mode="json")} if self._sort else {}
        if not self._query:
            self._query = TemplateQueryAPI(queries=[TemplateTypedQueryAPI(types=[t for t in TemplateType])])
        query_as_dict = {"queries": self._query.model_dump(by_alias=True, mode="json")["queries"]}

        return {**sort_as_dict, **query_as_dict}

    def build_params_as_dict(self) -> dict:
        return {"limit": self._limit} if self._limit else {}
