from typing import Self, Sequence

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.schemas.templates.template_payloads import TemplatePayloads
from services.base.tests.domain.builders.template.payload.template_payload_builder import TemplatePayloadBuilder
from services.base.type_resolver import TypeResolver
from services.data_service.api.models.request.template.insert_template_request_input import (
    InsertTemplateAPIRequestInput,
)
from services.data_service.application.use_cases.templates.models.insert_template_input_boundary import (
    InsertEventTemplateInputBoundaryItem,
)


class InsertEventTemplateRequestInputBuilder:
    def build(self) -> InsertTemplateAPIRequestInput:
        return InsertTemplateAPIRequestInput(documents=InsertEventTemplateRequestInputItemBuilder().build_n())

    def build_all(self) -> InsertTemplateAPIRequestInput:
        return InsertTemplateAPIRequestInput(documents=InsertEventTemplateRequestInputItemBuilder().build_all())


class InsertEventTemplateRequestInputItemBuilder:
    def __init__(self):
        self._name: str | None = None
        self._document: TemplatePayloads | None = None

    def build(self) -> InsertEventTemplateInputBoundaryItem:
        document = self._document or TemplatePayloadBuilder().build()
        return InsertEventTemplateInputBoundaryItem(
            name=self._name or PrimitiveTypesGenerator.generate_random_string(),
            document=document,
            tags=PrimitiveTypesGenerator.generate_random_tags(),
        )

    def build_n(self, n: int | None = None) -> Sequence[InsertEventTemplateInputBoundaryItem]:
        return [
            self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=10))
        ]

    def build_all(self, n: int | None = None) -> Sequence[InsertEventTemplateInputBoundaryItem]:
        out = []
        for payload_builder_type in TypeResolver.TEMPLATE_PAYLOAD_BUILDERS:
            out.extend(
                [
                    self.with_document(document=payload_builder_type().build()).build()
                    for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))
                ]
            )
        return out

    def with_name(self, name: str) -> Self:
        self._name = name
        return self

    def with_document(self, document: TemplatePayloads) -> Self:
        self._document = document
        return self
