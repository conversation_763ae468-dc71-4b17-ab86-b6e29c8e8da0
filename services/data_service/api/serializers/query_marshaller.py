from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.type_resolver import TypeResolver


class QueryMarshaller:

    @staticmethod
    def serialize(query: Query) -> dict:
        type_queries = [QueryMarshaller.serialize_type_query(type_query) for type_query in query.type_queries]
        return {"type_queries": type_queries}

    @staticmethod
    def deserialize(query: dict) -> Query:
        type_queries = [QueryMarshaller.deserialize_type_query(type_query) for type_query in query["type_queries"]]
        return Query(type_queries=type_queries)

    @staticmethod
    def deserialize_type_query(type_query_as_dict: dict) -> TypeQuery:
        domain_types = [TypeResolver.get_document(t) for t in type_query_as_dict["types"]]
        return TypeQuery(**type_query_as_dict, domain_types=domain_types)

    @staticmethod
    def serialize_type_query(type_query: TypeQuery) -> dict:
        return {
            "query": type_query.query.model_dump(mode="json") if type_query.query else None,
            "types": [domain_type.type_id() for domain_type in type_query.domain_types],
        }
