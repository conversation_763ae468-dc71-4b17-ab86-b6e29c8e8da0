import json
import os
from typing import Awaitable, Callable

import pytest
from pydantic import HttpUrl
from starlette import status

from services.base.domain.schemas.member_user.member_user import MemberUser
from services.data_service.api.constants import DataServicePrefixes, LookupEndpointRoutes
from services.data_service.api.tests.common_rpc_calls import _call_post_endpoint
from services.data_service.api.urls import LookupEndpointURLs
from services.data_service.application.use_cases.content.content_lookup_input_boundary import ContentLookupInputBoundary


class TestLookupEndpoints:
    @pytest.mark.integration
    @pytest.mark.parametrize(
        "input",
        [
            ContentLookupInputBoundary(url=HttpUrl("https://stackoverflow.com")),
            ContentLookupInputBoundary(url=HttpUrl("https://www.google.com/")),
            ContentLookupInputBoundary(url=HttpUrl("https://github.com/")),
            ContentLookupInputBoundary(url=HttpUrl("https://x.com/elonmusk/status/1519480761749016577")),
            ContentLookupInputBoundary(url=HttpUrl("https://pypi.org/project/metadata-parser/")),
        ],
    )
    async def test_content_lookup_endpoint_returns_200(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        input: ContentLookupInputBoundary,
    ):
        _, headers = await user_headers_factory()

        response = await _call_post_endpoint(
            request_url=LookupEndpointURLs.BASE,
            headers=headers,
            json=json.loads(input.model_dump_json(by_alias=True)),
            retry=False,
        )

        assert response
        assert response.status_code == status.HTTP_200_OK, f"got response: {response.json()}"

    @pytest.mark.parametrize(
        "input",
        [
            ContentLookupInputBoundary(url=HttpUrl("http://localhost:8888")),
            ContentLookupInputBoundary(url=HttpUrl("https://x.com/invalid/url")),
            ContentLookupInputBoundary(url=HttpUrl("https://twitter.com/just_username")),
            ContentLookupInputBoundary(url=HttpUrl("https://x.com/username/status/123456789")),
        ],
    )
    async def test_content_lookup_endpoint_returns_204(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        input: ContentLookupInputBoundary,
    ):
        _, headers = await user_headers_factory()

        response = await _call_post_endpoint(
            request_url=LookupEndpointURLs.BASE,
            headers=headers,
            json=json.loads(input.model_dump_json(by_alias=True)),
            retry=False,
        )

        assert response
        assert response.status_code == status.HTTP_204_NO_CONTENT

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "search_term",
        ["apple", "banana", "coca cola", "protein powder", "yellow thai curry"],
    )
    async def test_third_party_nutrition_lookup_endpoint_returns_200_with_single_result(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        search_term: str,
    ):
        _, headers = await user_headers_factory()

        endpoint_url = (
            DataServicePrefixes.V3
            + DataServicePrefixes.LOOKUP_PREFIX
            + LookupEndpointRoutes.NUTRITION_THIRD_PARTY_LOOKUP
        )

        response = await _call_post_endpoint(
            request_url=endpoint_url,
            headers=headers,
            query_params={"name": search_term},
            retry=False,
        )

        if response.status_code == status.HTTP_200_OK:
            result = response.json()
            assert len(result) >= 1, f"Expected at least 1 result, got {len(result)}"

            for item in result:
                assert item["type"] in ["food", "drink", "supplement"], f"Unexpected type: {item['type']}"
        else:
            pytest.fail(f"Unexpected status code: {response.status_code}, response: {response.json()}")

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "search_term",
        ["apple", "banana", "orange juice", "yellow thai curry"],
    )
    async def test_ai_nutrition_lookup_endpoint_returns_200_with_single_result(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        search_term: str,
    ):
        _, headers = await user_headers_factory()

        endpoint_url = (
            DataServicePrefixes.V3 + DataServicePrefixes.LOOKUP_PREFIX + LookupEndpointRoutes.NUTRITION_AI_LOOKUP
        )

        response = await _call_post_endpoint(
            request_url=endpoint_url,
            headers=headers,
            query_params={"name": search_term},
            retry=False,
        )

        if response.status_code == status.HTTP_200_OK:
            result = response.json()
            assert len(result) >= 1, f"Expected at least 1 result, got {len(result)}"

            for item in result:
                assert item["type"] in ["food", "drink", "supplement"], f"Unexpected type: {item['type']}"
        else:
            pytest.fail(f"Unexpected status code: {response.status_code}, response: {response}")

    @pytest.mark.integration
    @pytest.mark.parametrize(
        "image_filename,expected_content,expected_type",
        [
            ("pizza_food.jpg", "pizza", "food"),
            # @TODO: Make it work for non-food types ("water_drink.jpg", "water", "drink"),
        ],
    )
    async def test_image_ai_nutrition_lookup_endpoint_returns_200_with_expected_content(
        self,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
        image_filename: str,
        expected_content: str,
        expected_type: str,
    ):
        _, headers = await user_headers_factory()

        endpoint_url = (
            DataServicePrefixes.V3 + DataServicePrefixes.LOOKUP_PREFIX + LookupEndpointRoutes.NUTRITION_IMAGE_AI_LOOKUP
        )

        # Load the test image
        image_path = os.path.join(os.path.dirname(__file__), "images", image_filename)

        with open(image_path, "rb") as image_file:
            files = {"image_data": ("image.jpg", image_file, "image/jpeg")}
            response = await _call_post_endpoint(
                request_url=endpoint_url,
                headers=headers,
                files=files,
                retry=False,
            )

        if response.status_code == status.HTTP_200_OK:
            result = response.json()
            assert len(result) == 1, f"Expected 1 result, got {len(result)}"

            for item in result:
                assert item["type"] == expected_type, f"Expected type '{expected_type}', got '{item['type']}'"
                assert expected_content in item["name"], f"Expected '{expected_content}' in name '{item['name']}'"
        else:
            pytest.fail(f"Unexpected status code: {response.status_code}, response: {response.json()}")
