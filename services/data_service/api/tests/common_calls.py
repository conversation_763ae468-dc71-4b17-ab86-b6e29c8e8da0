from typing import List, Type
from uuid import UUID

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.models.filter_types import UserUUIDTermsFilter
from services.base.application.database.models.filters import Filters
from services.base.domain.schemas.shared_v2 import DeprEventModel


async def _call_event_delete(
    event_repo: DeprEventRepository,
    user_uuid: UUID,
    data_schemas: List[Type[DeprEventModel]],
    filters: Filters,
):
    [
        await event_repo.delete_by_query(data_schema=data_schema, user_uuid=user_uuid, filters=filters)
        for data_schema in data_schemas
    ]


async def _delete_user_documents(
    user_uuid: UUID, data_schema: Type[DeprEventModel], event_repo: DeprEventRepository
) -> None:
    filters = Filters()
    filters.must_filters.with_filters([UserUUIDTermsFilter(value=[str(user_uuid)])])
    await _call_event_delete(event_repo=event_repo, user_uuid=user_uuid, data_schemas=[data_schema], filters=filters)
