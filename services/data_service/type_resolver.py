from __future__ import annotations

from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.data_service.api.models.output.events.body_metric_api_outputs import (
    BloodGlucoseAPIOutput,
    BloodPressureAPIOutput,
    BodyMetricAPIOutput,
)
from services.data_service.api.models.output.events.content_api_outputs import (
    AudioAPIOutput,
    ContentAPIOutput,
    ImageAPIOutput,
    InteractiveAPIOutput,
    TextAPIOutput,
    VideoAPIOutput,
)
from services.data_service.api.models.output.events.exercise_api_outputs import (
    CardioAPIOutput,
    ExerciseAPIOutput,
    StrengthAPIOutput,
)
from services.data_service.api.models.output.events.feeling_api_outputs import EmotionAPIOutput, StressAPIOutput
from services.data_service.api.models.output.events.medication_api_output import MedicationAPIOutput
from services.data_service.api.models.output.events.nutrition_api_outputs import (
    DrinkAPIOutput,
    FoodAPIOutput,
    SupplementAPIOutput,
)
from services.data_service.api.models.output.events.sleep_v3_api_output import SleepV3APIOutput
from services.data_service.api.models.output.plan.plan_api_outputs import GoalAPIOutput, PlanAPIOutput
from services.data_service.api.models.output.records.sleep_record_api_output import SleepRecordAPIOutput
from services.data_service.api.models.output.template.template_api_outputs import (
    EventTemplateAPIOutput,
    GroupTemplateAPIOutput,
)
from services.data_service.api.models.response.contact.contact_api_output import ContactAPIOutput
from services.data_service.api.models.response.event.event_v3_api_output import (
    ActivityAPIOutput,
    EventGroupAPIOutput,
    NoteAPIOutput,
    PersonAPIOutput,
    SymptomAPIOutput,
)
from services.data_service.api.models.response.use_case.use_case_api_output import UseCaseAPIOutput
from services.data_service.application.builders.event.insert.insert_activity_input_builder import (
    InsertActivityInputBuilder,
)
from services.data_service.application.builders.event.insert.insert_blood_glucose_input_builder import (
    InsertBloodGlucoseInputBuilder,
)
from services.data_service.application.builders.event.insert.insert_blood_pressure_input_builder import (
    InsertBloodPressureInputBuilder,
)
from services.data_service.application.builders.event.insert.insert_body_metric_input_builder import (
    InsertBodyMetricInputBuilder,
)
from services.data_service.application.builders.event.insert.insert_content_collection_input_builder import (
    InsertAudioInputBuilder,
    InsertContentInputBuilder,
    InsertImageInputBuilder,
    InsertInteractiveInputBuilder,
    InsertTextInputBuilder,
    InsertVideoInputBuilder,
)
from services.data_service.application.builders.event.insert.insert_emotion_input_builder import (
    InsertEmotionInputBuilder,
)
from services.data_service.application.builders.event.insert.insert_event_group_input_builder import (
    InsertEventGroupInputBuilder,
)
from services.data_service.application.builders.event.insert.insert_exercise_collection_input_builders import (
    InsertCardioInputBuilder,
    InsertExerciseInputBuilder,
    InsertStrengthInputBuilder,
)
from services.data_service.application.builders.event.insert.insert_medication_input_builder import (
    InsertMedicationInputBuilder,
)
from services.data_service.application.builders.event.insert.insert_note_input_builder import (
    InsertNoteInputBuilder,
)
from services.data_service.application.builders.event.insert.insert_nutrition_collection_input_builders import (
    InsertDrinkInputBuilder,
    InsertFoodInputBuilder,
    InsertSupplementInputBuilder,
)
from services.data_service.application.builders.event.insert.insert_person_input_builder import InsertPersonInputBuilder
from services.data_service.application.builders.event.insert.insert_sleep_v3_input_builder import (
    InsertSleepV3InputBuilder,
)
from services.data_service.application.builders.event.insert.insert_stress_input_builder import InsertStressInputBuilder
from services.data_service.application.builders.event.insert.insert_symptom_input_builder import (
    InsertSymptomInputBuilder,
)
from services.data_service.application.builders.event.update.update_activity_input_builder import (
    UpdateActivityInputBuilder,
)
from services.data_service.application.builders.event.update.update_blood_glucose_input_builder import (
    UpdateBloodGlucoseInputBuilder,
)
from services.data_service.application.builders.event.update.update_blood_pressure_input_builder import (
    UpdateBloodPressureInputBuilder,
)
from services.data_service.application.builders.event.update.update_body_metric_input_builder import (
    UpdateBodyMetricInputBuilder,
)
from services.data_service.application.builders.event.update.update_content_collection_input_builder import (
    UpdateAudioInputBuilder,
    UpdateContentInputBuilder,
    UpdateImageInputBuilder,
    UpdateInteractiveInputBuilder,
    UpdateTextInputBuilder,
    UpdateVideoInputBuilder,
)
from services.data_service.application.builders.event.update.update_emotion_input_builder import (
    UpdateEmotionInputBuilder,
)
from services.data_service.application.builders.event.update.update_event_group_input_builder import (
    UpdateEventGroupInputBuilder,
)
from services.data_service.application.builders.event.update.update_exercise_collection_input_builders import (
    UpdateCardioInputBuilder,
    UpdateExerciseInputBuilder,
    UpdateStrengthInputBuilder,
)
from services.data_service.application.builders.event.update.update_medication_input_builder import (
    UpdateMedicationInputBuilder,
)
from services.data_service.application.builders.event.update.update_note_input_builder import UpdateNoteInputBuilder
from services.data_service.application.builders.event.update.update_nutrition_collection_input_builders import (
    UpdateDrinkInputBuilder,
    UpdateFoodInputBuilder,
    UpdateSupplementInputBuilder,
)
from services.data_service.application.builders.event.update.update_person_input_builder import UpdatePersonInputBuilder
from services.data_service.application.builders.event.update.update_sleep_v3_input_builder import (
    UpdateSleepV3InputBuilder,
)
from services.data_service.application.builders.event.update.update_stress_input_builder import UpdateStressInputBuilder
from services.data_service.application.builders.event.update.update_symptom_input_builder import (
    UpdateSymptomInputBuilder,
)
from services.data_service.application.builders.record.insert_sleep_record_builder import InsertSleepRecordInputBuilder
from services.data_service.application.builders.record.update_sleep_record_builder import (
    UpdateSleepRecordInputBuilder,
)
from services.data_service.application.use_cases.events.models.body_metric.insert_body_metric_inputs import (
    InsertBloodGlucoseInput,
    InsertBloodPressureInput,
    InsertBodyMetricInput,
)
from services.data_service.application.use_cases.events.models.body_metric.update_body_metric_inputs import (
    UpdateBloodGlucoseInput,
    UpdateBloodPressureInput,
    UpdateBodyMetricInput,
)
from services.data_service.application.use_cases.events.models.content.insert_content_inputs import (
    InsertAudioInput,
    InsertContentInput,
    InsertImageInput,
    InsertInteractiveInput,
    InsertTextInput,
    InsertVideoInput,
)
from services.data_service.application.use_cases.events.models.content.update_content_inputs import (
    UpdateAudioInput,
    UpdateContentInput,
    UpdateImageInput,
    UpdateInteractiveInput,
    UpdateTextInput,
    UpdateVideoInput,
)
from services.data_service.application.use_cases.events.models.exercise.insert_exercise_inputs import (
    InsertCardioInput,
    InsertExerciseInput,
    InsertStrengthInput,
)
from services.data_service.application.use_cases.events.models.exercise.update_exercise_inputs import (
    UpdateCardioInput,
    UpdateExerciseInput,
    UpdateStrengthInput,
)
from services.data_service.application.use_cases.events.models.feeling.insert_feeling_inputs import (
    InsertEmotionInput,
    InsertStressInput,
)
from services.data_service.application.use_cases.events.models.feeling.update_feeling_inputs import (
    UpdateEmotionInput,
    UpdateStressInput,
)
from services.data_service.application.use_cases.events.models.insert_activity_input import InsertActivityInput
from services.data_service.application.use_cases.events.models.insert_event_group_input import InsertEventGroupInput
from services.data_service.application.use_cases.events.models.insert_medication_input import InsertMedicationInput
from services.data_service.application.use_cases.events.models.insert_note_input import InsertNoteInput
from services.data_service.application.use_cases.events.models.insert_person_input import InsertPersonInput
from services.data_service.application.use_cases.events.models.insert_sleep_v3_input import InsertSleepV3Input
from services.data_service.application.use_cases.events.models.insert_symptom_input import InsertSymptomInput
from services.data_service.application.use_cases.events.models.nutrition.insert_nutrition_inputs import (
    InsertDrinkInput,
    InsertFoodInput,
    InsertSupplementInput,
)
from services.data_service.application.use_cases.events.models.nutrition.update_nutrition_inputs import (
    UpdateDrinkInput,
    UpdateFoodInput,
    UpdateSupplementInput,
)
from services.data_service.application.use_cases.events.models.update_activity_input import UpdateActivityInput
from services.data_service.application.use_cases.events.models.update_event_group_input import UpdateEventGroupInput
from services.data_service.application.use_cases.events.models.update_medication_input import UpdateMedicationInput
from services.data_service.application.use_cases.events.models.update_note_input import UpdateNoteInput
from services.data_service.application.use_cases.events.models.update_person_input import UpdatePersonInput
from services.data_service.application.use_cases.events.models.update_sleep_v3_input import UpdateSleepV3Input
from services.data_service.application.use_cases.events.models.update_symptom_input import UpdateSymptomInput
from services.data_service.application.use_cases.records.models.insert_sleep_record_input import InsertSleepRecordInput
from services.data_service.application.use_cases.records.models.update_sleep_record_input import UpdateSleepRecordInput
from services.data_service.v1.api.models.output.heart_rate_api_output import HeartRateAPIOutput
from services.data_service.v1.api.models.output.location_api_output import LocationAPIOutput
from services.data_service.v1.api.models.output.resting_heart_rate_api_output import RestingHeartRateAPIOutput
from services.data_service.v1.api.models.output.sleep_api_output import SleepAPIOutput
from services.data_service.v1.api.models.output.steps_api_output import StepsAPIOutput


class TypeResolver:
    INSERT_EVENT_INPUTS_UNION = (
        # Body Metric Collection
        InsertBloodGlucoseInput
        | InsertBloodPressureInput
        | InsertBodyMetricInput
        # Content Collection
        | InsertAudioInput
        | InsertContentInput
        | InsertImageInput
        | InsertInteractiveInput
        | InsertTextInput
        | InsertVideoInput
        # Exercise Collection
        | InsertCardioInput
        | InsertExerciseInput
        | InsertStrengthInput
        # Feeling Collection
        | InsertEmotionInput
        | InsertStressInput
        # Nutrition Collection
        | InsertDrinkInput
        | InsertFoodInput
        | InsertSupplementInput
        # Other Events
        | InsertActivityInput
        | InsertSleepV3Input
        | InsertNoteInput
        | InsertSymptomInput
        | InsertMedicationInput
        | InsertPersonInput
        # Groups
        | InsertEventGroupInput
    )
    EVENT_API_OUTPUTS_V2_UNION = (
        HeartRateAPIOutput | RestingHeartRateAPIOutput | SleepAPIOutput | StepsAPIOutput | LocationAPIOutput
    )

    EVENT_API_OUTPUTS_V3_UNION = (
        # Body Metrics
        BloodGlucoseAPIOutput
        | BloodPressureAPIOutput
        | BodyMetricAPIOutput
        # Content
        | AudioAPIOutput
        | ContentAPIOutput
        | ImageAPIOutput
        | InteractiveAPIOutput
        | TextAPIOutput
        | VideoAPIOutput
        # Exercise
        | ExerciseAPIOutput
        | CardioAPIOutput
        | StrengthAPIOutput
        # Feeling
        | EmotionAPIOutput
        | StressAPIOutput
        # Nutrition
        | DrinkAPIOutput
        | FoodAPIOutput
        | SupplementAPIOutput
        # Other Events
        | ActivityAPIOutput
        | SleepV3APIOutput
        | EventGroupAPIOutput
        | NoteAPIOutput
        | SymptomAPIOutput
        | MedicationAPIOutput
        | PersonAPIOutput
    )

    EVENT_API_OUTPUTS_UNION = EVENT_API_OUTPUTS_V2_UNION | EVENT_API_OUTPUTS_V3_UNION
    RECORD_API_OUTPUTS_UNION = SleepRecordAPIOutput
    TEMPLATE_API_OUTPUTS_UNION = EventTemplateAPIOutput | GroupTemplateAPIOutput
    PLAN_API_OUTPUTS_UNION = PlanAPIOutput | GoalAPIOutput

    DOCUMENT_API_OUTPUTS_UNION = (
        EVENT_API_OUTPUTS_UNION
        | RECORD_API_OUTPUTS_UNION
        | TEMPLATE_API_OUTPUTS_UNION
        | ContactAPIOutput
        | PLAN_API_OUTPUTS_UNION
        | UseCaseAPIOutput
    )

    UPDATE_EVENT_INPUTS_UNION = (
        # Body Metric Collection
        UpdateBloodGlucoseInput
        | UpdateBloodPressureInput
        | UpdateBodyMetricInput
        # Content Collection
        | UpdateAudioInput
        | UpdateContentInput
        | UpdateImageInput
        | UpdateInteractiveInput
        | UpdateTextInput
        | UpdateVideoInput
        # Exercise Collection
        | UpdateCardioInput
        | UpdateExerciseInput
        | UpdateStrengthInput
        # Feeling Collection
        | UpdateEmotionInput
        | UpdateStressInput
        # Nutrition Collection
        | UpdateDrinkInput
        | UpdateFoodInput
        | UpdateSupplementInput
        # Other Events
        | UpdateActivityInput
        | UpdateSleepV3Input
        | UpdateEventGroupInput
        | UpdateNoteInput
        | UpdateSymptomInput
        | UpdateMedicationInput
        | UpdatePersonInput
    )

    INSERT_GROUP_INPUTS_UNION = InsertEventGroupInput
    UPDATE_GROUP_INPUTS_UNION = UpdateEventGroupInput

    INSERT_EVENT_INPUT_BUILDERS_UNION = (
        # Body Metric Collection
        InsertBloodGlucoseInputBuilder
        | InsertBloodPressureInputBuilder
        | InsertBodyMetricInputBuilder
        # Content Collection
        | InsertAudioInputBuilder
        | InsertContentInputBuilder
        | InsertImageInputBuilder
        | InsertInteractiveInputBuilder
        | InsertTextInputBuilder
        | InsertVideoInputBuilder
        # Exercise Collection
        | InsertCardioInputBuilder
        | InsertExerciseInputBuilder
        | InsertStrengthInputBuilder
        # Feeling Collection
        | InsertEmotionInputBuilder
        | InsertStressInputBuilder
        # Nutrition Collection
        | InsertDrinkInputBuilder
        | InsertFoodInputBuilder
        | InsertSupplementInputBuilder
        # Other Events
        | InsertActivityInputBuilder
        | InsertSleepV3InputBuilder
        | InsertEventGroupInputBuilder
        | InsertNoteInputBuilder
        | InsertSymptomInputBuilder
        | InsertMedicationInputBuilder
        | InsertPersonInputBuilder
    )

    UPDATE_EVENT_INPUT_BUILDERS_UNION = (
        # Body Metric Collection
        UpdateBloodGlucoseInputBuilder
        | UpdateBloodPressureInputBuilder
        | UpdateBodyMetricInputBuilder
        # Content Collection
        | UpdateAudioInputBuilder
        | UpdateContentInputBuilder
        | UpdateImageInputBuilder
        | UpdateInteractiveInputBuilder
        | UpdateTextInputBuilder
        | UpdateVideoInputBuilder
        # Exercise Collection
        | UpdateCardioInputBuilder
        | UpdateExerciseInputBuilder
        | UpdateStrengthInputBuilder
        # Feeling Collection
        | UpdateEmotionInputBuilder
        | UpdateStressInputBuilder
        # Nutrition Collection
        | UpdateDrinkInputBuilder
        | UpdateFoodInputBuilder
        | UpdateSupplementInputBuilder
        # Other Events
        | UpdateActivityInputBuilder
        | UpdateSleepV3InputBuilder
        | UpdateNoteInputBuilder
        | UpdateSymptomInputBuilder
        | UpdateEventGroupInputBuilder
        | UpdateMedicationInputBuilder
        | UpdatePersonInputBuilder
    )

    INSERT_GROUP_INPUT_BUILDERS_UNION = InsertEventGroupInputBuilder

    INSERT_RECORD_INPUTS_UNION = InsertSleepRecordInput

    INSERT_RECORD_INPUT_BUILDERS_UNION = InsertSleepRecordInputBuilder
    UPDATE_RECORD_INPUT_BUILDERS_UNION = UpdateSleepRecordInputBuilder

    UPDATE_RECORD_INPUTS_UNION = UpdateSleepRecordInput

    @staticmethod
    def get_insert_event_input(type_id: str) -> type[INSERT_EVENT_INPUTS_UNION]:
        for insert_event in TypeResolver.INSERT_EVENT_INPUTS_UNION.__args__:
            if insert_event.type_id() == type_id:
                return insert_event

        raise ShouldNotReachHereException(f"Insert event input not found for type id: {type_id}")

    @staticmethod
    def get_insert_event_input_builder(type_id: str) -> type[INSERT_EVENT_INPUT_BUILDERS_UNION]:
        for builder in TypeResolver.INSERT_EVENT_INPUT_BUILDERS_UNION.__args__:
            if builder.type_id() == type_id:
                return builder

        raise ShouldNotReachHereException(f"Insert event builder not found for type id: {type_id}")

    @staticmethod
    def get_update_event_input(type_id: str) -> type[UPDATE_EVENT_INPUT_BUILDERS_UNION]:
        for update_event in TypeResolver.UPDATE_EVENT_INPUTS_UNION.__args__:
            if update_event.type_id() == type_id:
                return update_event

        raise ShouldNotReachHereException(f"Update event builder not found for type id: {type_id}")

    @staticmethod
    def get_update_event_input_builder(type_id: str) -> type[UPDATE_EVENT_INPUT_BUILDERS_UNION]:
        for builder in TypeResolver.UPDATE_EVENT_INPUT_BUILDERS_UNION.__args__:
            if builder.type_id() == type_id:
                return builder

        raise ShouldNotReachHereException(f"Update event builder not found for type id: {type_id}")

    @staticmethod
    def get_event_api_output(type_id: str) -> type[EVENT_API_OUTPUTS_UNION]:
        for event_api_output in TypeResolver.EVENT_API_OUTPUTS_UNION.__args__:
            if event_api_output.type_id() == type_id:
                return event_api_output

        raise ShouldNotReachHereException(f"Event api output not found for type id: {type_id}")

    @staticmethod
    def get_document_api_output(type_id: str) -> type[DOCUMENT_API_OUTPUTS_UNION]:
        for document_api_output in TypeResolver.DOCUMENT_API_OUTPUTS_UNION.__args__:
            if document_api_output.type_id() == type_id:
                return document_api_output

        raise ShouldNotReachHereException(f"document api output not found for type id: {type_id}")

    @staticmethod
    def get_record_api_output(type_id: str) -> type[RECORD_API_OUTPUTS_UNION]:
        for record_api_output in (TypeResolver.RECORD_API_OUTPUTS_UNION,):
            if record_api_output.type_id() == type_id:
                return record_api_output

        raise ShouldNotReachHereException(f"Record api output not found for type id: {type_id}")

    @staticmethod
    def get_event_api_output_v3(type_id: str) -> type[EVENT_API_OUTPUTS_V3_UNION]:
        for event_api_output in TypeResolver.EVENT_API_OUTPUTS_V3_UNION.__args__:
            if event_api_output.type_id() == type_id:
                return event_api_output

        raise ShouldNotReachHereException(f"Event api output v3 not found for type id: {type_id}")

    @staticmethod
    def get_insert_record_input(type_id: str) -> type[INSERT_RECORD_INPUTS_UNION]:
        for insert_record in (TypeResolver.INSERT_RECORD_INPUTS_UNION,):
            if insert_record.type_id() == type_id:
                return insert_record

        raise ShouldNotReachHereException(f"Insert record input not found for type id: {type_id}")

    @staticmethod
    def get_update_record_input(type_id: str) -> type[UPDATE_RECORD_INPUTS_UNION]:
        for update_record in (TypeResolver.UPDATE_RECORD_INPUTS_UNION,):
            if update_record.type_id() == type_id:
                return update_record

        raise ShouldNotReachHereException(f"Update record input not found for type id: {type_id}")

    @classmethod
    def get_update_record_input_builder(cls, type_id: str) -> type[UPDATE_RECORD_INPUT_BUILDERS_UNION]:
        for builder in (cls.UPDATE_RECORD_INPUT_BUILDERS_UNION,):
            if builder.type_id() == type_id:
                return builder
        raise ValueError(f"No update record input builder found for type_id: {type_id}")

    @classmethod
    def get_insert_record_input_builder(cls, type_id):
        for builder in (cls.INSERT_RECORD_INPUT_BUILDERS_UNION,):
            if builder.type_id() == type_id:
                return builder
        raise ValueError(f"No insert record input builder found for type_id: {type_id}")
