import math
import random
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, AsyncGenerator, Awaitable, Callable, Literal, Sequence

import pytest

from services.base.application.boundaries.time_input import TimeInput
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_type import EventType
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.air_quality import AirQualityFields
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.symptom import SymptomFields
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.query.aggregations import SimpleAggregationMethod
from services.base.domain.schemas.shared import CoordinatesModel
from services.base.tests.domain.builders.location_builder import <PERSON><PERSON>uilder
from services.base.tests.domain.builders.symptom_builder import Symptom<PERSON>uilder
from services.data_service.api.queries.event_query_api import EventTypedQueryAPI
from services.data_service.api.tests.common_calls import _delete_user_documents
from services.data_service.application.use_cases.event_correlation.event_correlation_use_case import (
    CorrelationVariableInput,
    EnvironmentTypedQuery,
    EventCorrelationUseCase,
)


class TestEventCorrelationUseCase:
    @pytest.fixture
    async def user_with_symptoms(
        self,
        event_repo: EventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[tuple[Sequence[Event], MemberUser], Any]:
        user, headers = await user_headers_factory()
        start = PrimitiveTypesGenerator.generate_random_aware_datetime()
        end = start + timedelta(days=300)

        symptoms = []
        for _ in range(PrimitiveTypesGenerator.generate_random_int(min_value=500, max_value=1000)):
            if random.choice([True, False]):
                continue
            ts = PrimitiveTypesGenerator.generate_random_aware_datetime(gte=start, lte=end)
            et = PrimitiveTypesGenerator.generate_random_aware_datetime(
                allow_none=True,
                gte=ts,
                lte=ts + PrimitiveTypesGenerator.generate_random_timedelta(max_timedelta=timedelta(hours=1)),
            )
            symptoms.append(
                SymptomBuilder()
                .with_owner_id(owner_id=user.user_uuid)
                .with_name(name="headache")
                .with_timestamp(timestamp=ts)
                .with_end_time(end_time=et)
                .build()
            )

        inserted_symptoms = await event_repo.insert(events=symptoms, force_strong_consistency=True)

        yield inserted_symptoms, user

        await event_repo.delete_by_id(ids=[e.id for e in [*inserted_symptoms]])

    @pytest.fixture
    async def user_with_location(
        self,
        depr_event_repository: DeprEventRepository,
        user_headers_factory: Callable[[], Awaitable[tuple[MemberUser, dict]]],
    ) -> AsyncGenerator[tuple[Sequence[Location], MemberUser], Any]:
        user, headers = await user_headers_factory()
        locations = [
            LocationBuilder()
            .with_coordinates(CoordinatesModel(lat=49.5938, lon=17.2509))
            .with_user_uuid(user_uuid=user.user_uuid)
            .build()
            for _ in range(20)
        ]
        inserted_locations = await depr_event_repository.insert(models=locations, force_strong_consistency=True)
        yield inserted_locations, user

        # Teardown
        await _delete_user_documents(user_uuid=user.user_uuid, data_schema=Location, event_repo=depr_event_repository)

    async def test_event_correlation_use_case_get_docs_get_event_data_no_field_name(
        self, event_correlation_use_case, user_with_symptoms
    ):
        symptoms, user = user_with_symptoms
        time_gte = min([symptom.timestamp for symptom in symptoms])
        time_lte = max([symptom.timestamp for symptom in symptoms])

        docs, count = await event_correlation_use_case._get_docs(
            variable=CorrelationVariableInput(
                field_name=None,
                aggregation_method=None,
                query=EventTypedQueryAPI(query=None, types=[EventType.Symptom]),
            ),
            owner_id=user.user_uuid,
            time_input=TimeInput(time_gte=time_gte, time_lte=time_lte, interval="1h"),
            doc_limit=10000,
        )

        assert count == len(symptoms)

    async def test_event_correlation_use_case_get_docs_get_event_data_field_name(
        self, event_correlation_use_case, user_with_symptoms
    ):
        symptoms, user = user_with_symptoms
        time_gte = min([symptom.timestamp for symptom in symptoms])
        time_lte = max([symptom.timestamp for symptom in symptoms])

        docs, count = await event_correlation_use_case._get_docs(
            variable=CorrelationVariableInput(
                field_name=SymptomFields.RATING,
                aggregation_method=None,
                query=EventTypedQueryAPI(query=None, types=[EventType.Symptom]),
            ),
            owner_id=user.user_uuid,
            time_input=TimeInput(time_gte=time_gte, time_lte=time_lte, interval="1h"),
            doc_limit=10000,
        )

        symptoms_with_rating = [symptom for symptom in symptoms if symptom.rating is not None]
        assert count == len(symptoms_with_rating)

        sorted_symptoms = sorted(symptoms_with_rating, key=lambda symptom: symptom.timestamp)
        for value, symptom in zip([doc.value for doc in docs.values], sorted_symptoms):
            assert value == symptom.rating

    async def test_event_correlation_use_case_get_docs_get_event_data_aggregation(
        self, event_correlation_use_case, user_with_symptoms
    ):
        symptoms, user = user_with_symptoms
        time_gte = min([symptom.timestamp for symptom in symptoms if symptom.rating is not None])
        time_lte = max([symptom.timestamp for symptom in symptoms if symptom.rating is not None])

        docs, count = await event_correlation_use_case._get_docs(
            variable=CorrelationVariableInput(
                field_name=SymptomFields.RATING,
                aggregation_method=SimpleAggregationMethod.AVG,
                query=EventTypedQueryAPI(query=None, types=[EventType.Symptom]),
            ),
            owner_id=user.user_uuid,
            time_input=TimeInput(time_gte=time_gte, time_lte=time_lte, interval="1d"),
            doc_limit=10000,
        )

        days_delta = time_lte.date() - time_gte.date()
        days = math.ceil(days_delta.days) + 1
        assert count == days

    async def test_event_correlation_use_case_get_docs_environment_data(
        self, event_correlation_use_case, user_with_location
    ):
        locations, user = user_with_location
        time_gte = min([symptom.timestamp for symptom in locations])
        time_lte = max([symptom.timestamp for symptom in locations])

        docs, count = await event_correlation_use_case._get_docs(
            variable=CorrelationVariableInput(
                field_name=f"{AirQualityFields.POLLUTANTS}.{AirQualityFields.PM10}",
                aggregation_method=SimpleAggregationMethod.AVG,
                query=EnvironmentTypedQuery(domain_type=DataType.AirQuality),
            ),
            owner_id=user.user_uuid,
            time_input=TimeInput(time_gte=time_gte, time_lte=time_lte, interval="1h"),
            doc_limit=10000,
        )

        assert count == len(locations)
        assert len(docs.values) == len(locations)

    @pytest.mark.parametrize(
        "dependent_type, independent_type, expected_visualization",
        [
            ("continuous", "continuous", "scatter_plot"),
            ("continuous", "discrete", "box_plot"),
            ("continuous", "occurrence", "box_plot"),
            ("discrete", "continuous", "box_plot"),
            ("occurrence", "continuous", "box_plot"),
            ("discrete", "discrete", "heat_map"),
            ("discrete", "occurrence", "heat_map"),
            ("occurrence", "discrete", "heat_map"),
            ("occurrence", "occurrence", "heat_map"),
        ],
    )
    def test_determine_visualization_type(
        self,
        dependent_type: str,
        independent_type: str,
        expected_visualization: Literal["scatter_plot", "box_plot", "heat_map"],
    ):
        # Arrange
        use_case = EventCorrelationUseCase

        # Act
        result = use_case._determine_visualization_type(
            dependent_type=dependent_type, independent_type=independent_type
        )

        # Assert
        assert result == expected_visualization
