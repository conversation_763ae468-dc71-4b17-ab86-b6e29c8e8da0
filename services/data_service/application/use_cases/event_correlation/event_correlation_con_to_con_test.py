import pytest
from pandas import DataFrame

from services.data_service.application.use_cases.event_correlation.event_correlation_schemas import (
    AnalysisCertainty,
    AnalysisRelationshipLabel,
)
from services.data_service.application.use_cases.event_correlation.event_correlation_service import CorrelationService


def test_calculate_con_to_con_perfect_positive_correlation():
    # Perfect positive correlation (strong, significant)
    data = DataFrame({"dependent": [1, 2, 3, 4, 5], "independent": [1, 2, 3, 4, 5]})
    expected = {
        "coef": 1.0,
        "p": 0.0,
        "certainty": AnalysisCertainty.STRONG_EVIDENCE,
        "relationship": AnalysisRelationshipLabel.STRONG_RELATIONSHIP,
    }

    result = CorrelationService.calculate_con_to_con(data)

    assert round(result.correlation_coefficient, 2) == pytest.approx(expected["coef"], abs=0.1)
    assert result.p_value == pytest.approx(expected["p"], abs=0.1)
    assert result.certainty == expected["certainty"]
    assert result.relationship == expected["relationship"]


def test_calculate_con_to_con_perfect_negative_correlation():
    # Perfect negative correlation (strong, significant)
    data = DataFrame({"dependent": [5, 4, 3, 2, 1], "independent": [1, 2, 3, 4, 5]})
    expected = {
        "coef": -1.0,
        "p": 0.0,
        "certainty": AnalysisCertainty.STRONG_EVIDENCE,
        "relationship": AnalysisRelationshipLabel.STRONG_RELATIONSHIP,
    }

    result = CorrelationService.calculate_con_to_con(data)

    assert round(result.correlation_coefficient, 2) == pytest.approx(expected["coef"], abs=0.1)
    assert result.p_value == pytest.approx(expected["p"], abs=0.1)
    assert result.certainty == expected["certainty"]
    assert result.relationship == expected["relationship"]


def test_calculate_con_to_con_strong_correlation():
    # Strong correlation (significant)
    data = DataFrame({"dependent": [1, 2, 3], "independent": [0.9, 2.1, 3.2]})
    expected = {
        "coef": 0.998,
        "p": 0.039,
        "certainty": AnalysisCertainty.MODERATE_EVIDENCE,
        "relationship": AnalysisRelationshipLabel.STRONG_RELATIONSHIP,
    }

    result = CorrelationService.calculate_con_to_con(data)

    assert round(result.correlation_coefficient, 2) == pytest.approx(expected["coef"], abs=0.1)
    assert result.p_value == pytest.approx(expected["p"], abs=0.1)
    assert result.certainty == expected["certainty"]
    assert result.relationship == expected["relationship"]


def test_calculate_con_to_con_no_correlation():
    # No correlation (insignificant)
    data = DataFrame({"dependent": [10, 20, 10, 20], "independent": [1, 1, 2, 2]})
    expected = {
        "coef": 0.0,
        "p": 1.0,
        "certainty": AnalysisCertainty.INSUFFICIENT_EVIDENCE,
        "relationship": AnalysisRelationshipLabel.NO_RELATIONSHIP,
    }

    result = CorrelationService.calculate_con_to_con(data)

    assert round(result.correlation_coefficient, 2) == pytest.approx(expected["coef"], abs=0.1)
    assert result.p_value == pytest.approx(expected["p"], abs=0.1)
    assert result.certainty == expected["certainty"]
    assert result.relationship == expected["relationship"]
