from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import UniqueSequenceUUID
from services.base.domain.schemas.shared import BaseDataModel


class ReorderEventGroupInputBoundary(BaseDataModel):
    id: UUID = Field(description="The UUID of the event group to reorder")
    child_ids: UniqueSequenceUUID = Field(description="Ordered list of child event IDs representing the desired order")
