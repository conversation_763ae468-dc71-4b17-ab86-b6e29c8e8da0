from abc import ABC, abstractmethod
from typing import Sequence
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, UniqueSequenceStr
from services.base.domain.schemas.events.document_base import AssetReference, DocumentValueLimits, EventMetadata
from services.base.domain.schemas.events.event import Event, EventFields, EventPlanExtension, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.events.models.shared import (
    EventInputAssetsDocument,
    EventInputTimeInterval,
)


class EventInsertionContext(BaseDataModel):
    owner_id: UUID
    submission_id: UUID
    asset_references: Sequence[AssetReference]
    group_id: UUID | None = None
    metadata: EventMetadata


class InsertEventInput(EventInputTimeInterval, EventInputAssetsDocument, TypeIdentifier, ABC):
    name: NonEmptyStr = Field(alias=EventFields.NAME, max_length=EventValueLimits.MAX_NAME_LENGTH, min_length=1)
    tags: UniqueSequenceStr = Field(
        alias=EventFields.TAGS, max_length=DocumentValueLimits.MaxTagsCount, default_factory=list
    )
    template_id: UUID | None = Field(alias=EventFields.TEMPLATE_ID, default=None)
    plan_extension: EventPlanExtension | None = Field(alias=EventFields.PLAN_EXTENSION, default=None)
    group_id: UUID | None = Field(
        alias=EventFields.GROUP_ID, default=None, description="Adds event to existing event group"
    )

    @abstractmethod
    def to_domain(self, ctx: EventInsertionContext) -> Event:
        pass
