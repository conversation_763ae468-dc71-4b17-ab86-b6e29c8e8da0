from enum import StrEnum

from pydantic_ai import Agent
from pydantic_ai.models import Model

from services.base.application.use_case_base import UseCaseBase
from services.base.domain.enums.event_type import EventType
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNot<PERSON><PERSON><PERSON><PERSON>Exception
from services.base.domain.schemas.shared import BaseDataModel
from services.data_service.application.use_cases.ai.prompts.pick_type_prompt import generate_pick_type_prompt
from services.data_service.application.use_cases.ai.prompts.prompt_schemas import PromptSchema
from services.data_service.application.use_cases.events.models.insert_event_group_input import InsertEventGroupInput
from services.data_service.type_resolver import TypeResolver


class ModelOutput(BaseDataModel):
    type: EventType


class ClassifyEventTypeUseCase(UseCaseBase):
    def __init__(self, model: Model):
        self._schemas = [
            self._generate_prompt_schema(schema)
            for schema in (set(TypeResolver.INSERT_EVENT_INPUTS_UNION.__args__) - {InsertEventGroupInput})
        ]
        self._type_prompt = generate_pick_type_prompt(schemas=self._schemas)
        self._agent = Agent(model=model, instructions=self._type_prompt)

    async def execute(self, query: str) -> PromptSchema:
        result = await self._agent.run(user_prompt=query)
        event_type = EventType(result.output.strip().lower())
        return [schema for schema in self._schemas if schema.event_type.type_id() == event_type][0]

    @staticmethod
    def _generate_prompt_schema(event_type: type[TypeResolver.INSERT_EVENT_INPUTS_UNION]) -> PromptSchema:
        category_field = event_type.model_fields.get("category")
        if not category_field:
            raise ShouldNotReachHereException(f"Category field not found in event type {category_field}")
        enum_type = category_field.annotation
        if not enum_type:
            raise ShouldNotReachHereException(f"Category field not annotated {enum_type}")
        if not issubclass(enum_type, StrEnum):
            raise ShouldNotReachHereException(f"Category field is not str enum {enum_type}")
        return PromptSchema(
            event_type=event_type,
            category_enum=enum_type,
        )
