from datetime import datetime, timezone
from typing import Sequence
from uuid import UUID
from zoneinfo import ZoneInfo

from pydantic import BaseModel, ValidationError
from pydantic_ai import Agent
from pydantic_ai.models import Model

from services.base.application.retry import retry
from services.base.application.use_case_base import UseCaseBase
from services.base.application.utils.member_user.member_user_settings import get_user_timezone
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.schemas.query.leaf_query import MatchType, PatternQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.data_service.application.use_cases.ai.classify_event_type_use_case import ClassifyEventTypeUseCase
from services.data_service.application.use_cases.ai.prompts.prompt_schemas import PromptSchema
from services.data_service.application.use_cases.ai.prompts.suggest_event_prompt import (
    UserTemplate,
    generate_suggest_event_prompt,
)
from services.data_service.application.use_cases.events.insert_event_inputs import InsertEventInputs
from services.data_service.application.use_cases.templates.models.search_templates_input_boundary import (
    SearchTemplatesInputBoundary,
)
from services.data_service.application.use_cases.templates.search_template_use_case import SearchTemplatesUseCase


class SuggestEventUseCaseInputBoundary(BaseModel):
    query: NonEmptyStr


class SuggestEventUseCase(UseCaseBase):
    def __init__(
        self,
        model: Model,
        search_templates_use_case: SearchTemplatesUseCase,
        member_user_settings_repo: MemberUserSettingsRepository,
        classify_event_type_uc: ClassifyEventTypeUseCase,
    ):
        self._model = model
        self._search_templates_use_case = search_templates_use_case
        self._member_user_settings_repo = member_user_settings_repo
        self._classify_event_type_uc = classify_event_type_uc

    @retry(exceptions=(ValidationError,), max_times=1, delay=0.0)
    async def execute(self, user_uuid: UUID, input_boundary: SuggestEventUseCaseInputBoundary) -> InsertEventInputs:
        prompt_schema = await self._classify_event_type_uc.execute(query=input_boundary.query)

        templates = await self._get_user_templates(query=input_boundary.query, user_uuid=user_uuid)
        serialized_templates = self._serialize_templates(templates) if templates else None

        user_timezone = await get_user_timezone(
            uuid=user_uuid,
            member_user_settings_repo=self._member_user_settings_repo,
        )

        return await self._generate_event(
            query=input_boundary.query,
            schema=prompt_schema,
            templates=serialized_templates,
            user_timezone=user_timezone,
        )

    def _serialize_templates(self, templates: Sequence[EventTemplate]) -> list[dict]:
        return [
            UserTemplate(
                template_id=t.id,
                template_name=t.document_name,
                document=t.document.model_dump(by_alias=True),
            ).model_dump(by_alias=True)
            for t in templates
        ]

    async def _generate_event(
        self,
        query: str,
        schema: PromptSchema,
        user_timezone: ZoneInfo,
        templates: list[dict] | None,
    ) -> InsertEventInputs:
        current_timestamp = datetime.now(timezone.utc).isoformat()
        instructions = generate_suggest_event_prompt(
            schema=schema,
            templates=templates,
            current_timestamp=current_timestamp,
        )
        agent = Agent(model=self._model, instructions=instructions, output_type=schema.event_type)
        response = await agent.run(user_prompt=query, output_type=schema.event_type)
        event = response.output

        # We replace the timezones to match the user's timezone while keeping the logic of the LLM timestamp in UTC
        event.timestamp = event.timestamp.replace(tzinfo=user_timezone)
        if event.end_time:
            event.end_time = event.end_time.replace(tzinfo=user_timezone)

        return response.output

    async def _get_user_templates(self, query: str, user_uuid: UUID) -> Sequence[EventTemplate]:
        result = await self._search_templates_use_case.execute_async(
            input_boundary=SearchTemplatesInputBoundary(
                owner_id=user_uuid,
                query=Query(
                    type_queries=[
                        TypeQuery(
                            domain_types=[EventTemplate],
                            query=PatternQuery(
                                field_names=["document_name", "document", "tags"],
                                pattern=query,
                                match_type=MatchType.FUZZY,
                            ),
                        )
                    ]
                ),
            )
        )
        # we are explicitly ignoring group templates for now
        templates: list[EventTemplate] = []
        for template in result:
            if isinstance(template, EventTemplate):
                templates.append(template)
        return templates
