from datetime import datetime, timezone
from typing import Sequence
from uuid import UUID

from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.templates.template import Template, TemplateFields
from services.data_service.application.use_cases.templates.template_validator import TemplateValidator


class ArchiveTemplateUseCase:
    def __init__(
        self,
        template_repo: TemplateRepository,
        template_validator: TemplateValidator,
    ):
        self._template_repo = template_repo
        self._template_validator = template_validator

    async def execute_async(self, owner_id: UUID, template_ids: Sequence[UUID]) -> Sequence[Template]:
        async def collect_templates(ids: Sequence[UUID], owner_id: UUID) -> list[Template]:
            existing_templates = await self._template_validator.fetch_and_validate_templates(
                template_ids=ids, owner_id=owner_id
            )

            archive_timestamp = datetime.now(timezone.utc)
            return [
                type(template).map(model=template, fields={TemplateFields.ARCHIVED_AT: archive_timestamp})
                for template in existing_templates
            ]

        templates_to_archive = await collect_templates(template_ids, owner_id)

        return await self._template_repo.update(templates=templates_to_archive)
