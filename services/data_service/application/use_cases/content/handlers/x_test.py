import pytest
from pydantic import HttpUrl

from services.data_service.application.use_cases.content.handlers.x import XHandler


class TestXHandler:
    @pytest.mark.parametrize(
        ("url", "expected"),
        [
            # Valid URLs
            ("https://x.com/username/status/123456789", True),
            ("https://x.com/username/status/123456789?s=20", True),
            ("https://x.com/user_name/status/123456789", True),
            ("https://x.com/user.name/status/123456789", True),
            ("https://x.com/user123/status/123456789", True),
            # Invalid URLs
            ("https://x.com/username", False),  # Missing status
            ("https://x.com/username/status", False),  # Missing ID
            ("https://x.com/username/status/abc", False),  # Non-numeric ID
            ("https://x.com", False),  # Missing path
            ("https://example.com/username/status/123456789", False),  # Wrong domain
        ],
    )
    def test_is_valid_x_url(self, url: str, expected: bool):
        assert XHandler._is_valid_x_url(HttpUrl(url)) == expected

    def test_extract_post_text(self):
        html = """<blockquote class="twitter-tweet"><p lang="en" dir="ltr">Just released our new open source project!<br><br>• Improved performance<br><br>• Better documentation<br><br>• New features added<br><br>Check it out and let me know what you think! 🚀</p>&mdash; Sarah Chen (@sarahcodes) <a href="https://twitter.com/sarahcodes/status/1882406209187409976?ref_src=twsrc%5Etfw">January 23, 2025</a></blockquote>\n<script async src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>\n"""

        text = XHandler._extract_post_text(html)

        expected_text = "Just released our new open source project! • Improved performance • Better documentation • New features added Check it out and let me know what you think! 🚀"
        assert text == expected_text
