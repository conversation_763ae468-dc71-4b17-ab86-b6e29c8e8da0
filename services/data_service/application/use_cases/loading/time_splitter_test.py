from datetime import datetime, timedelta, timezone
from typing import List

import pytest

from services.base.application.builders.input_time_interval_builder import InputTimeIntervalBuilder
from services.base.application.input_validators.shared import InputTimeIntervalModel
from services.base.domain.constants.time_constants import SECONDS_IN_HOUR
from services.data_service.application.use_cases.loading.time_splitter import TimeSplitter


@pytest.mark.parametrize(
    "input,interval",
    (
        (
            [
                InputTimeIntervalBuilder()
                .with_timestamp(timestamp=datetime.now(timezone.utc).replace(microsecond=0) + timedelta(seconds=i))
                .with_end_time(end_time=datetime.now(timezone.utc).replace(microsecond=0) + timedelta(seconds=i + 1))
                .build()
                for i in range(0, 1)
            ],
            timedelta(seconds=1),
        ),
        (
            [
                InputTimeIntervalBuilder()
                .with_timestamp(timestamp=datetime.now(timezone.utc).replace(microsecond=0) + timedelta(seconds=i))
                .with_end_time(end_time=datetime.now(timezone.utc).replace(microsecond=0) + timedelta(seconds=i + 1))
                .build()
                for i in range(0, 10)
            ],
            timedelta(seconds=2),
        ),
        (
            [
                InputTimeIntervalBuilder()
                .with_timestamp(timestamp=datetime.now(timezone.utc).replace(microsecond=0) + timedelta(seconds=i))
                .with_end_time(end_time=datetime.now(timezone.utc).replace(microsecond=0) + timedelta(seconds=i + 1))
                .build()
                for i in range(0, 100)
            ],
            timedelta(seconds=10),
        ),
        (
            InputTimeIntervalBuilder().build_series(max_time_delta=timedelta(seconds=SECONDS_IN_HOUR)),
            timedelta(seconds=SECONDS_IN_HOUR),
        ),
        (
            InputTimeIntervalBuilder().build_series(max_time_delta=timedelta(seconds=775)),
            timedelta(seconds=775),
        ),
        (
            [],
            timedelta(hours=1),
        ),
    ),
)
def test_time_splitter_validate_split_should_pass(input: List[InputTimeIntervalModel], interval: timedelta):
    split_buckets = TimeSplitter.split(input_data=input, load_aggregation_interval=interval)
    for bucket in split_buckets:
        assert bucket.time_bucket.end_time - bucket.time_bucket.timestamp <= interval
        for item in bucket.data:
            assert bucket.time_bucket.timestamp <= item.timestamp <= bucket.time_bucket.end_time
            assert bucket.time_bucket.timestamp <= item.end_time <= bucket.time_bucket.end_time


def test_time_splitter_with_empty_input():
    split_buckets = list(TimeSplitter.split(input_data=[], load_aggregation_interval=timedelta(hours=1)))
    assert split_buckets == []


# test case where the time_difference is equal to load_aggregation_interval on the first iteration
def test_time_splitter_first_iter_time_diff_eq_load_agg():
    interval = timedelta(hours=1)
    now = datetime.now(timezone.utc).replace(microsecond=0)

    input_data = [
        InputTimeIntervalBuilder().with_timestamp(timestamp=now).with_end_time(end_time=now + interval).build(),
        InputTimeIntervalBuilder()
        .with_timestamp(timestamp=now + interval)
        .with_end_time(end_time=now + interval * 2)
        .build(),
    ]
    split_buckets = list(TimeSplitter.split(input_data=input_data, load_aggregation_interval=interval))
    assert len(split_buckets) == 2  # Ensure that two buckets are created correctly
    assert split_buckets[0].time_bucket.timestamp == input_data[0].timestamp
    assert split_buckets[1].time_bucket.timestamp == input_data[1].timestamp
