from typing import List

from pydantic import field_validator
from pydantic.fields import Field

from services.data_service.application.use_cases.loading.metadata_input import MetadataInputBoundary
from services.data_service.application.use_cases.loading.resting_heart_rate.models.load_resting_heart_rate_input import (
    LoadRestingHeartRateInput,
)
from services.data_service.application.validation.loading_input_validators import validate_overlap_in_sorted_entries


class LoadRestingHeartRateInputBoundary(MetadataInputBoundary):
    documents: List[LoadRestingHeartRateInput] = Field(min_length=1)

    @field_validator("documents")
    @classmethod
    def input_data_overlap_validator(
        cls, documents: List[LoadRestingHeartRateInput]
    ) -> List[LoadRestingHeartRateInput]:
        documents.sort(key=lambda entry: entry.timestamp)
        validate_overlap_in_sorted_entries(documents=documents)
        return documents
