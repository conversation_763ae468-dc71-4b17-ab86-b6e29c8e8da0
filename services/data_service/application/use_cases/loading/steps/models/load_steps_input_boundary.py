from typing import List

from pydantic import field_validator
from pydantic.fields import Field

from services.data_service.application.use_cases.loading.metadata_input import MetadataInputBoundary
from services.data_service.application.use_cases.loading.steps.models.load_steps_input import LoadStepsInput
from services.data_service.application.validation.loading_input_validators import validate_overlap_in_sorted_entries


class LoadStepsInputBoundary(MetadataInputBoundary):
    documents: List[LoadStepsInput] = Field(min_length=1)

    @field_validator("documents")
    @classmethod
    def input_data_overlap_validator(cls, documents: List[LoadStepsInput]) -> List[LoadStepsInput]:
        documents.sort(key=lambda entry: entry.timestamp)
        validate_overlap_in_sorted_entries(documents=documents)
        return documents
