import random
from datetime import datetime, timedelta, timezone

from dateutil.rrule import DAILY, HOURLY

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.custom_rrule import CustomRRule
from services.base.domain.schemas.plan.plan_base import Streak
from services.data_service.application.use_cases.plans.complete_plan_helpers import CompletePlanHelpers


class TestCompletePlanHelpers:

    def test_plan_is_too_early_passes(self):
        # Arrange
        started_at = PrimitiveTypesGenerator.generate_random_aware_datetime()
        recurrence = PrimitiveTypesGenerator.generate_random_rrule(started_at=started_at)
        assert recurrence

        frequency: timedelta = recurrence[1] - recurrence[0]
        offset = PrimitiveTypesGenerator.generate_random_timedelta(
            min_timedelta=timedelta(seconds=1), max_timedelta=timedelta(seconds=int(frequency.total_seconds()))
        )
        # do two ticks
        next_scheduled_at = recurrence.after(recurrence.after(dt=started_at))
        completed_at = recurrence.before(dt=next_scheduled_at) - offset

        # Act
        plan_is = CompletePlanHelpers.is_plan(
            next_scheduled_at=next_scheduled_at,
            completed_at=completed_at,
            recurrence=recurrence,
        )
        # Assert
        assert plan_is == "too_early"

    def test_plan_is_too_early_empty_before_passes(self):
        # Arrange
        started_at = PrimitiveTypesGenerator.generate_random_aware_datetime()
        recurrence = CustomRRule(dtstart=started_at, freq=DAILY)

        frequency: timedelta = recurrence[1] - recurrence[0]
        offset = PrimitiveTypesGenerator.generate_random_timedelta(
            min_timedelta=timedelta(seconds=1), max_timedelta=timedelta(seconds=int(frequency.total_seconds()))
        )

        completed_at = started_at - timedelta(days=1) - offset

        # Act
        plan_is = CompletePlanHelpers.is_plan(
            next_scheduled_at=started_at,
            completed_at=completed_at,
            recurrence=recurrence,
        )
        # Assert
        assert plan_is == "too_early"

    def test_plan_is_too_late_passes(self):
        # Arrange
        started_at = PrimitiveTypesGenerator.generate_random_aware_datetime()
        recurrence = PrimitiveTypesGenerator.generate_random_rrule(started_at=started_at)
        assert recurrence

        frequency: timedelta = recurrence[1] - recurrence[0]

        offset = PrimitiveTypesGenerator.generate_random_timedelta(
            min_timedelta=timedelta(seconds=1), max_timedelta=timedelta(seconds=int(frequency.total_seconds()))
        )
        completed_at = recurrence.after(dt=started_at) + offset

        # Act
        plan_is = CompletePlanHelpers.is_plan(
            next_scheduled_at=started_at,
            completed_at=completed_at,
            recurrence=recurrence,
        )
        # Assert
        assert plan_is == "too_late"

    def test_plan_is_on_time_passes(self):
        # Arrange
        started_at = PrimitiveTypesGenerator.generate_random_aware_datetime()
        recurrence = PrimitiveTypesGenerator.generate_random_rrule(started_at=started_at)
        assert recurrence

        frequency: timedelta = recurrence[1] - recurrence[0]

        offset = PrimitiveTypesGenerator.generate_random_timedelta(
            min_timedelta=timedelta(seconds=1), max_timedelta=timedelta(seconds=int(frequency.total_seconds()))
        )
        completed_at = started_at + random.choice((-1, 1)) * offset
        # Act
        plan_is = CompletePlanHelpers.is_plan(
            next_scheduled_at=started_at,
            completed_at=completed_at,
            recurrence=recurrence,
        )
        # Assert
        assert plan_is == "on_time"

    def test_calculate_next_schedule_calculate_is_plan_and_next_schedule_passes(self):
        # Arrange
        original_due = datetime(2025, 4, 1, 23, tzinfo=timezone.utc)
        completed_at = datetime(2025, 4, 2, 23, tzinfo=timezone.utc)
        recurrence = CustomRRule(dtstart=original_due, freq=DAILY)

        plan_is = CompletePlanHelpers.is_plan(
            next_scheduled_at=original_due,
            completed_at=completed_at,
            recurrence=recurrence,
        )
        # Act
        next_tick, rec = CompletePlanHelpers.calculate_next_schedule_and_recurrence(
            next_scheduled_at=original_due,
            completed_at=completed_at,
            recurrence=recurrence,
            is_plan_schedule_absolute=True,
            plan_is=plan_is,
        )
        # Assert
        assert next_tick == datetime(2025, 4, 3, 23, tzinfo=timezone.utc)

    def test_calculate_next_schedule_relative_only_freq_passes(self):
        # Arrange
        started_at = PrimitiveTypesGenerator.generate_random_aware_datetime()
        recurrence = PrimitiveTypesGenerator.generate_random_rrule(started_at=started_at)
        assert recurrence

        frequency: timedelta = recurrence[1] - recurrence[0]
        offset = PrimitiveTypesGenerator.generate_random_timedelta(
            min_timedelta=timedelta(seconds=1), max_timedelta=timedelta(seconds=int(frequency.total_seconds()))
        )
        completed_at = started_at + random.choice((-1, 1)) * offset

        expected_rec = recurrence.replace(dtstart=completed_at)
        expected_dt = expected_rec.after(completed_at)

        # Act
        next_tick, rec = CompletePlanHelpers.calculate_next_schedule_and_recurrence(
            next_scheduled_at=PrimitiveTypesGenerator.generate_random_aware_datetime(),  # Not important
            completed_at=completed_at,
            recurrence=recurrence,
            is_plan_schedule_absolute=False,
            plan_is=random.choice(("on_time", "too_early", "too_late")),  # does not matter
        )

        # Assert
        assert next_tick == expected_dt, str(rec)
        assert str(rec) == str(expected_rec)

    def test_calculate_next_schedule_absolute_only_freq_on_time_passes(self):
        # Arrange
        started_at = PrimitiveTypesGenerator.generate_random_aware_datetime()
        recurrence = PrimitiveTypesGenerator.generate_random_rrule(started_at=started_at)
        assert recurrence
        next_scheduled_at = recurrence.after(dt=started_at)

        frequency: timedelta = recurrence[1] - recurrence[0]
        offset = PrimitiveTypesGenerator.generate_random_timedelta(
            min_timedelta=timedelta(seconds=1), max_timedelta=timedelta(seconds=int(frequency.total_seconds()))
        )
        completed_at = next_scheduled_at + random.choice((-1, 1)) * offset

        expected_dt = recurrence.after(dt=next_scheduled_at)

        # Act
        next_tick, rec = CompletePlanHelpers.calculate_next_schedule_and_recurrence(
            next_scheduled_at=next_scheduled_at,
            completed_at=completed_at,
            recurrence=recurrence,
            is_plan_schedule_absolute=True,
            plan_is="on_time",
        )

        # Assert
        assert next_tick == expected_dt
        assert str(rec) == str(recurrence)

    def test_calculate_next_schedule_only_freq_absolute_late_passes(self):
        # Arrange
        started_at = PrimitiveTypesGenerator.generate_random_aware_datetime()
        recurrence = PrimitiveTypesGenerator.generate_random_rrule(started_at=started_at)
        assert recurrence
        next_scheduled_at = recurrence.after(dt=started_at)

        frequency: timedelta = recurrence[1] - recurrence[0]
        ticks = PrimitiveTypesGenerator.generate_random_int(min_value=2, max_value=10)
        offset = PrimitiveTypesGenerator.generate_random_timedelta(
            min_timedelta=timedelta(seconds=1), max_timedelta=timedelta(seconds=int(frequency.total_seconds()))
        )

        for i in range(ticks):
            next_scheduled_at = recurrence.after(next_scheduled_at)
        completed_at = next_scheduled_at + random.choice((-1, 1)) * offset
        expected_next_scheduled = recurrence.after(completed_at)

        # Act
        next_tick, rec = CompletePlanHelpers.calculate_next_schedule_and_recurrence(
            next_scheduled_at=next_scheduled_at,
            completed_at=completed_at,
            recurrence=recurrence,
            is_plan_schedule_absolute=True,
            plan_is="too_late",
        )

        # Assert
        assert next_tick == expected_next_scheduled
        assert str(rec) == str(recurrence)

    def test_calculate_next_schedule_absolute_too_early_passes(self):
        # Arrange
        started_at = PrimitiveTypesGenerator.generate_random_aware_datetime()
        recurrence = PrimitiveTypesGenerator.generate_random_rrule(started_at=started_at)
        assert recurrence

        next_scheduled_at = recurrence.after(dt=started_at)

        frequency: timedelta = recurrence[1] - recurrence[0]
        offset = PrimitiveTypesGenerator.generate_random_timedelta(
            min_timedelta=timedelta(seconds=1), max_timedelta=timedelta(seconds=int(frequency.total_seconds()))
        )
        completed_at = started_at - offset

        expected_next_scheduled = next_scheduled_at

        # Act
        next_tick, rec = CompletePlanHelpers.calculate_next_schedule_and_recurrence(
            next_scheduled_at=next_scheduled_at,
            completed_at=completed_at,
            recurrence=recurrence,
            is_plan_schedule_absolute=True,
            plan_is="too_early",
        )

        # Assert
        assert next_tick == expected_next_scheduled
        assert str(rec) == str(recurrence)

    def _generate_streak_input(self, within_interval=True, too_early=False):
        """
        Generates dynamic input for the test case.
        `within_interval` determines if the task completion should be within the frequency interval.
        `too_early` determines if the task completion is too early but within the acceptable range.
        """
        streak = Streak(
            streak=PrimitiveTypesGenerator.generate_random_int(0, 10),
            longest_streak=PrimitiveTypesGenerator.generate_random_int(0, 20),
        )
        next_scheduled_at = datetime.now()
        recurrence = CustomRRule(
            dtstart=next_scheduled_at, freq=HOURLY, interval=PrimitiveTypesGenerator.generate_random_int(1, 3)
        )
        frequency = recurrence[1] - recurrence[0]

        if within_interval:
            completed_at = next_scheduled_at + timedelta(
                minutes=PrimitiveTypesGenerator.generate_random_int(
                    min_value=0, max_value=int(frequency.total_seconds() // 60 - 1)
                )
            )
        elif too_early:
            completed_at = next_scheduled_at - timedelta(
                minutes=PrimitiveTypesGenerator.generate_random_int(min_value=int(frequency.total_seconds() // 60))
            )
        else:
            completed_at = (
                next_scheduled_at + frequency + timedelta(minutes=PrimitiveTypesGenerator.generate_random_int(1, 30))
            )

        return streak, next_scheduled_at, completed_at, recurrence

    def test_calculate_streak_counters_within_interval_passes(self):
        # Arrange
        streak = Streak(
            streak=PrimitiveTypesGenerator.generate_random_int(0, 10),
            longest_streak=PrimitiveTypesGenerator.generate_random_int(0, 20),
        )

        # Act
        updated_streak = CompletePlanHelpers.calculate_streak_counters(streak=streak, plan_is="on_time")

        # Assert
        assert updated_streak.streak == streak.streak + 1
        assert updated_streak.longest_streak == max(streak.longest_streak, streak.streak + 1)

    def test_calculate_streak_counters_too_late_passes(self):
        # Arrange
        streak = Streak(
            streak=PrimitiveTypesGenerator.generate_random_int(2, 10),
            longest_streak=PrimitiveTypesGenerator.generate_random_int(0, 20),
        )

        # Act
        updated_streak = CompletePlanHelpers.calculate_streak_counters(streak=streak, plan_is="too_late")

        # Assert
        assert updated_streak.streak == 1
        assert updated_streak.longest_streak == streak.longest_streak

    def test_calculate_streak_counters_completed_too_early_passes(self):
        # Arrange
        streak = Streak(
            streak=PrimitiveTypesGenerator.generate_random_int(0, 10),
            longest_streak=PrimitiveTypesGenerator.generate_random_int(0, 20),
        )

        # Act
        updated_streak = CompletePlanHelpers.calculate_streak_counters(streak=streak, plan_is="too_early")

        # Assert
        assert updated_streak.streak == streak.streak
        assert updated_streak.longest_streak == max(streak.longest_streak, streak.streak)
