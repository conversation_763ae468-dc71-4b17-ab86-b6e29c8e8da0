from typing import Any, AsyncGenerator, Awaitable, Callable

import pytest

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.schemas.events.nutrition.food import Food
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionFields
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.plan.goal import Goal
from services.base.domain.schemas.plan.plan import Plan
from services.base.domain.schemas.query.aggregations import SimpleAggregationMethod
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.tests.domain.builders.activity_builder import ActivityBuilder
from services.base.tests.domain.builders.goal_builder import GoalBuilder, GoalConditionBuilder
from services.base.tests.domain.builders.nutrition.food_builder import FoodBuilder
from services.data_service.application.use_cases.plans.calculate_goal_progress_and_completion_use_case import (
    CalculateGoalProgressAndCompletionUseCase,
)


class TestCalculateGoalProgressAndCompletionUseCase:
    @pytest.fixture
    async def user_with_nutrition_goal(
        self, plan_repo: PlanRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> AsyncGenerator[tuple[Plan, MemberUser], Any]:
        user: MemberUser = await user_factory()
        condition = (
            GoalConditionBuilder()
            .with_field_name(NutritionFields.CALORIES)
            .with_gte(PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=1000))
            .with_lte(PrimitiveTypesGenerator.generate_random_float(min_value=1000, max_value=2000))
            .with_agg_method(SimpleAggregationMethod.SUM)
            .with_query(TypeQuery(domain_types=[Food], query=None))
            .build()
        )
        started_at = PrimitiveTypesGenerator.generate_random_aware_datetime()
        goal = (
            GoalBuilder()
            .with_owner_id(user.user_uuid)
            .with_condition(condition)
            .with_next_scheduled_at(PrimitiveTypesGenerator.generate_random_aware_datetime(gte=started_at))
            .with_archived_at(False)
            .with_recurrence(PrimitiveTypesGenerator.generate_random_rrule(started_at=started_at))
            .with_current_value(0)
            .build()
        )
        inserted_goals = await plan_repo.insert(plans=[goal], force_strong_consistency=True)

        yield inserted_goals[0], user

        # Teardown
        await plan_repo.delete_by_id(ids=[g.id for g in inserted_goals])

    async def test_calculate_goal_nothing_to_recalculate_goal_unchanged_passes(
        self,
        user_with_nutrition_goal: tuple[Goal, MemberUser],
        event_repo: EventRepository,
        calculate_goal_progress_and_completion_uc: CalculateGoalProgressAndCompletionUseCase,
    ):
        # Arrange
        nutrition_goal, user = user_with_nutrition_goal
        activity = (
            ActivityBuilder()
            .with_timestamp(
                PrimitiveTypesGenerator.generate_random_aware_datetime(
                    lte=nutrition_goal.next_scheduled_at,
                    gte=nutrition_goal.recurrence.before(dt=nutrition_goal.next_scheduled_at),
                )
            )
            .with_owner_id(user.user_uuid)
            .build()
        )
        await event_repo.insert(events=[activity], force_strong_consistency=True)

        try:
            # Act
            recalculated_goal = (
                await calculate_goal_progress_and_completion_uc.execute(
                    goals=[nutrition_goal], should_hard_complete=False
                )
            )[0]
            assert recalculated_goal.current_value == nutrition_goal.current_value
            assert recalculated_goal.current_completed == nutrition_goal.current_completed

        finally:
            # Teardown
            await event_repo.delete_by_id(ids=[activity.id])

    async def test_calculate_goal_food_inserted_recalculates_goal_passes(
        self,
        user_with_nutrition_goal: tuple[Goal, MemberUser],
        event_repo: EventRepository,
        calculate_goal_progress_and_completion_uc: CalculateGoalProgressAndCompletionUseCase,
    ):
        # Arrange
        nutrition_goal, user = user_with_nutrition_goal
        assert nutrition_goal.condition.lte
        assert nutrition_goal.recurrence

        food = (
            FoodBuilder()
            .with_calories(
                PrimitiveTypesGenerator.generate_random_float(max_value=nutrition_goal.condition.gte or 1000)
            )
            .with_timestamp(
                PrimitiveTypesGenerator.generate_random_aware_datetime(
                    lte=nutrition_goal.next_scheduled_at,
                    gte=nutrition_goal.recurrence.before(dt=nutrition_goal.next_scheduled_at),
                )
            )
            .with_owner_id(user.user_uuid)
            .build()
        )
        food = (await event_repo.insert(events=[food], force_strong_consistency=True))[0]
        assert isinstance(food, Food)
        assert food.calories

        try:
            # Act
            recalculated_goal = (
                await calculate_goal_progress_and_completion_uc.execute(
                    goals=[nutrition_goal], should_hard_complete=False
                )
            )[0]
            assert recalculated_goal.current_value == nutrition_goal.current_value + food.calories
            assert not recalculated_goal.is_achieved

            food.calories = int(nutrition_goal.condition.gte) + 1
            await event_repo.update(events=[food], force_strong_consistency=True)
            recalculated_goal = (
                await calculate_goal_progress_and_completion_uc.execute(
                    goals=[recalculated_goal], should_hard_complete=False
                )
            )[0]
            assert recalculated_goal.is_achieved
        finally:
            # Teardown
            await event_repo.delete_by_id(ids=[food.id])

    async def test_calculate_goal_hard_complete_passes(
        self,
        user_with_nutrition_goal: tuple[Goal, MemberUser],
        event_repo: EventRepository,
        plan_repo: PlanRepository,
        calculate_goal_progress_and_completion_uc: CalculateGoalProgressAndCompletionUseCase,
    ):
        # Arrange
        nutrition_goal, user = user_with_nutrition_goal
        assert nutrition_goal.recurrence
        nutrition_goal.current_completed = 0
        nutrition_goal.max_completed = 2

        food = (
            FoodBuilder()
            .with_timestamp(
                PrimitiveTypesGenerator.generate_random_aware_datetime(
                    lte=nutrition_goal.next_scheduled_at,
                    gte=nutrition_goal.recurrence.before(dt=nutrition_goal.next_scheduled_at),
                )
            )
            .with_owner_id(user.user_uuid)
            .build()
        )
        food = (await event_repo.insert(events=[food], force_strong_consistency=True))[0]
        assert isinstance(food, Food)
        await plan_repo.update(plans=[nutrition_goal])

        try:
            # Act
            recalculated_goal = (
                await calculate_goal_progress_and_completion_uc.execute(
                    goals=[nutrition_goal], should_hard_complete=True
                )
            )[0]
            assert recalculated_goal.current_completed == nutrition_goal.current_completed + 1
            assert recalculated_goal.next_scheduled_at == nutrition_goal.recurrence.after(
                dt=nutrition_goal.next_scheduled_at
            )
            assert not recalculated_goal.archived_at

            again_recalculated_goal = (
                await calculate_goal_progress_and_completion_uc.execute(
                    goals=[recalculated_goal], should_hard_complete=True
                )
            )[0]
            assert again_recalculated_goal.current_completed == recalculated_goal.current_completed + 1
            assert again_recalculated_goal.next_scheduled_at == recalculated_goal.next_scheduled_at
            assert again_recalculated_goal.archived_at

        finally:
            # Teardown
            await event_repo.delete_by_id(ids=[food.id])
