import json
from typing import Sequence
from uuid import UUID

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts
from services.base.application.notifications.push_notification_models import PushNotificationMessage
from services.base.application.notifications.push_notification_service import PushNotificationService
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.repository.member_user_device_repository import MemberUserDeviceRepository
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.plan.goal import Goal, GoalConditionFields, GoalFields
from services.base.domain.schemas.query.boolean_query import AndQuery, NotQuery
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import ExistsQuery, RangeQuery, ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.data_service.api.serializers.query_marshaller import QueryMarshaller
from services.data_service.application.use_cases.plans.calculate_goal_progress_and_completion_use_case import (
    CalculateGoalProgressAndCompletionUseCase,
)


class CheckGoalsToRecalculateUseCase:
    def __init__(
        self,
        plan_repo: PlanRepository,
        device_repo: MemberUserDeviceRepository,
        search_service: DocumentSearchService,
        calculate_goal_progress_uc: CalculateGoalProgressAndCompletionUseCase,
        push_notification_service: PushNotificationService,
    ):
        self._plan_repo = plan_repo
        self._search_service = search_service
        self._calculate_goal_progress_uc = calculate_goal_progress_uc
        self._push_notification_service = push_notification_service
        self._device_repo = device_repo

    async def handle(self, owner_id: UUID, mutated_document: Event) -> Sequence[Goal]:
        tp = mutated_document.type_id()
        existing_goals = await self._fetch_and_validate_goals(owner_id=owner_id, tp=tp)  # pyright: ignore
        if not existing_goals:
            return []
        device_tokens = [device.device_token for device in await self._device_repo.get_by_uuid(user_uuid=owner_id)]
        recalculated_goals = await self._fetch_goals_to_recalculate(
            owner_id=owner_id, existing_goals=existing_goals, mutated_document=mutated_document
        )
        messages = [
            self._get_goal_progress_message(rg, og, device_tokens)
            for rg, og in zip(
                sorted(recalculated_goals, key=lambda g: g.id), sorted(existing_goals, key=lambda g: g.id)
            )
        ]
        self._push_notification_service.publish(messages=[m for m in messages if m is not None])
        return recalculated_goals

    async def _fetch_goals_to_recalculate(
        self, owner_id: UUID, existing_goals: Sequence[Goal], mutated_document: Event
    ):
        goals_to_recalculate = []
        for goal in existing_goals:
            lte = goal.next_scheduled_at
            if goal.recurrence:
                gte = goal.recurrence.before(dt=lte)
            else:
                gte = goal.system_properties.created_at
            if not lte <= mutated_document.timestamp <= gte:
                continue
            q = QueryMarshaller.deserialize_type_query(json.loads(goal.condition.query))
            q.query = (
                BooleanQueryBuilder()
                .add_queries(
                    queries=[
                        q.query,
                        ExistsQuery(field_name=goal.condition.field_name),
                        RangeQuery(field_name=DocumentLabels.TIMESTAMP, gte=gte, lte=lte),
                        CommonLeafQueries.owner_id_value_query(user_uuid=owner_id),
                        ValuesQuery(field_name=DocumentLabels.ID, values=[str(mutated_document.id)]),
                    ]
                )
                .build_and_query()
            )
            matching_docs = (
                await self._search_service.search_documents_by_query(
                    query=Query(type_queries=[q]), size=1, sorts=CommonSorts.created_at_and_internal_id()
                )
            ).documents
            if not matching_docs:
                continue
            if mutated_document.id == matching_docs[0].id:
                goals_to_recalculate.append(goal)

        return await self._calculate_goal_progress_uc.execute(goals=goals_to_recalculate, should_hard_complete=False)

    async def _fetch_and_validate_goals(self, owner_id: UUID, tp: DataType) -> Sequence[Goal]:
        q = SingleDocumentTypeQuery[Goal](
            query=AndQuery(
                queries=[
                    CommonLeafQueries.owner_id_value_query(user_uuid=owner_id),
                    NotQuery(queries=[ExistsQuery(field_name=DocumentLabels.ARCHIVED_AT)]),
                    ValuesQuery(
                        field_name=GoalFields.CONDITION + "." + GoalConditionFields.QUERY + "." + "domain_type",
                        values=[tp],
                    ),
                ]
            ),
            domain_type=Goal,
        )
        existing_goals = await self._search_service.search_documents_by_single_query(
            query=q, size=10000, sorts=CommonSorts.created_at_and_internal_id()
        )
        for goal in existing_goals.documents:
            if goal.rbac.owner_id != owner_id:
                raise Exception("You don't have permission to access this document")
        return existing_goals.documents

    def _get_goal_progress_message(
        self, updated_goal: Goal, original_goal: Goal, device_tokens: Sequence[str]
    ) -> PushNotificationMessage | None:
        if updated_goal.is_achieved:
            return PushNotificationMessage(
                title=f"Goal {updated_goal.name} Achieved",
                body="Congrats! You achieved your goal.",
                device_tokens=device_tokens,
            )
        if updated_goal.condition.lte:
            if updated_goal.current_value >= updated_goal.condition.lte / 2:
                if original_goal.current_value < updated_goal.condition.lte / 2:
                    return PushNotificationMessage(
                        title="You are halfway there! Keep it up.",
                        body=f"Goal {updated_goal.name} is 50% Achieved",
                        device_tokens=device_tokens,
                    )
        return None
