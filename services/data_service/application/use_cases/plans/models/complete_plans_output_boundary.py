from typing import Sequence

from pydantic import Field

from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.plan.plan import Plan
from services.base.domain.schemas.shared import BaseDataModel


class CompletePlansOutputBoundaryItem(BaseDataModel):
    plan: Plan
    events: Sequence[Event] = Field(min_length=1)


class CompletePlansOutputBoundary(BaseDataModel):
    documents: Sequence[CompletePlansOutputBoundaryItem] = Field(min_length=1)
