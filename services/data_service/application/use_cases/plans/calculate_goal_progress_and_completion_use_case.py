import json
import logging
from typing import Sequence

from services.base.application.database.aggregation_service import AggregationService
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.schemas.plan.goal import Goal, GoalFields
from services.base.domain.schemas.query.aggregations import AggregationMethod, FieldAggregation
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.leaf_query import ExistsQuery, RangeQuery
from services.base.domain.schemas.query.query import Query
from services.data_service.api.serializers.query_marshaller import QueryMarshaller
from services.data_service.application.use_cases.plans.complete_plan_helpers import CompletePlanHelpers


class CalculateGoalProgressAndCompletionUseCase:
    def __init__(self, plan_repo: PlanRepository, agg_service: AggregationService):
        self._plan_repo = plan_repo
        self._agg_service = agg_service

    async def execute(self, goals: Sequence[Goal], should_hard_complete: bool) -> Sequence[Goal]:
        goals_to_update = []
        returned_goals = []
        for goal in goals:
            lte = goal.next_scheduled_at
            if goal.recurrence:
                gte = goal.recurrence.before(dt=lte)
            else:
                gte = goal.system_properties.created_at
            q = QueryMarshaller.deserialize_type_query(json.loads(goal.condition.query))
            q.query = (
                BooleanQueryBuilder()
                .add_queries(
                    queries=[
                        q.query,
                        ExistsQuery(field_name=goal.condition.field_name),
                        RangeQuery(field_name=DocumentLabels.TIMESTAMP, gte=gte, lte=lte),
                    ]
                )
                .build_and_query()
            )
            aggregated = (
                await self._agg_service.aggregate_by_query(
                    query=Query(type_queries=[q]),
                    aggregations=[
                        FieldAggregation(
                            field_name=goal.condition.field_name,
                            aggregation_method=AggregationMethod(goal.condition.agg_method),
                        )
                    ],
                )
            )[0]
            if aggregated.value is None:
                logging.error(f"Failed to aggregate data for goal {goal.id}")
                aggregated.value = 0
            if should_hard_complete:
                completed_goal = CompletePlanHelpers.complete_goal(goal=goal)
                goals_to_update.append(completed_goal)
                returned_goals.append(completed_goal)
            elif aggregated.value != goal.current_value:
                completed_goal = Goal.map(model=goal, fields={GoalFields.CURRENT_VALUE: aggregated.value})
                goals_to_update.append(completed_goal)
                returned_goals.append(completed_goal)
            else:
                returned_goals.append(goal)

        if goals_to_update:
            persisted = await self._plan_repo.update(plans=goals_to_update)
        return returned_goals
