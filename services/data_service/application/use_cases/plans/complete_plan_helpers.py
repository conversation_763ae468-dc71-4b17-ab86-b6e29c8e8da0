from datetime import datetime, timezone
from typing import Literal

from services.base.domain.custom_rrule import CustomRRule
from services.base.domain.enums.data_types import DataType
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.rrule_helpers import RRuleHelpers
from services.base.domain.schemas.plan.goal import Goal
from services.base.domain.schemas.plan.plan import Plan
from services.base.domain.schemas.plan.plan_base import Streak
from services.data_service.application.use_cases.plans.models.complete_plans_input_boundary import CompletePlanInput


class CompletePlanHelpers:
    @staticmethod
    def is_plan(
        completed_at: datetime,
        next_scheduled_at: datetime,
        recurrence: CustomRRule,
    ) -> Literal["too_early", "too_late", "on_time"]:
        # Validate previous tick, or calculate it if not available
        previous_tick = recurrence.before(dt=next_scheduled_at)
        if not previous_tick:
            period = RRuleHelpers.get_relativedelta_from_rrule(rule=recurrence)
            previous_tick = next_scheduled_at - period

        if completed_at < previous_tick:
            return "too_early"
        elif completed_at >= recurrence.after(dt=next_scheduled_at):
            return "too_late"
        else:
            return "on_time"

    @staticmethod
    def calculate_streak_counters(streak: Streak, plan_is: Literal["too_early", "too_late", "on_time"]) -> Streak:
        new_streak = streak.streak

        # Completed too late: reset streak
        if plan_is == "too_early":
            pass
        # Completed too early: streak does not change
        elif plan_is == "too_late":
            new_streak = 1
        # Default -> increment streak
        else:
            new_streak += 1

        new_longest_streak = max(streak.longest_streak, new_streak)
        return Streak(streak=new_streak, longest_streak=new_longest_streak)

    @staticmethod
    def calculate_next_schedule_and_recurrence(
        completed_at: datetime,
        next_scheduled_at: datetime,
        recurrence: CustomRRule,
        is_plan_schedule_absolute: bool,
        plan_is: Literal["too_early", "too_late", "on_time"],
    ) -> tuple[datetime | None, CustomRRule]:
        if is_plan_schedule_absolute:
            # Completed too early -> just keep current schedule
            if plan_is == "too_early":
                return next_scheduled_at, recurrence
            # Completed too late -> skip ticks of the plans to current time
            elif plan_is == "too_late":
                return recurrence.after(dt=completed_at), recurrence
            # Default -> schedule next tick
            else:
                return recurrence.after(dt=next_scheduled_at), recurrence
        else:
            rec = recurrence.replace(dtstart=completed_at)
            next_schedule_from = completed_at
            return rec.after(dt=next_schedule_from), rec

    @staticmethod
    def should_archive(max_completed: int | None, current_completed: int) -> bool:
        if not max_completed:
            return False
        return True if (current_completed >= max_completed) else False

    @classmethod
    def complete_goal(cls, goal: Goal):
        current_completed = goal.current_completed + 1
        current_value = 0
        now = datetime.now(timezone.utc)
        archived_at = goal.archived_at
        next_scheduled_at = goal.next_scheduled_at

        if goal.recurrence:
            next_scheduled_at = goal.recurrence.after(dt=goal.next_scheduled_at)

        if cls.should_archive(goal.max_completed, current_completed):
            archived_at = now
            next_scheduled_at = goal.next_scheduled_at

        # Calculate streak
        streak = CompletePlanHelpers.calculate_streak_counters(
            streak=goal.streak,
            plan_is="on_time" if goal.is_achieved else "too_late",
        )

        return Goal(
            type=DataType.Goal,
            id=goal.id,
            rbac=goal.rbac,
            metadata=goal.metadata,
            name=goal.name,
            recurrence=goal.recurrence,
            next_scheduled_at=next_scheduled_at,
            note=goal.note,
            streak=streak,
            archived_at=archived_at,
            current_completed=current_completed,
            max_completed=goal.max_completed,
            tags=goal.tags,
            condition=goal.condition,
            current_value=current_value,
        )

    @classmethod
    def get_updated_plan_fields(
        cls, complete_input: CompletePlanInput, existing_plan: Plan
    ) -> tuple[datetime, CustomRRule, datetime | None, int, int, Streak]:
        if not existing_plan.recurrence:
            raise ShouldNotReachHereException(f"plan {existing_plan.id} recurrence not set")
        current_completed = existing_plan.current_completed + 1
        plan_is = CompletePlanHelpers.is_plan(
            completed_at=complete_input.completed_at,
            next_scheduled_at=existing_plan.next_scheduled_at,
            recurrence=existing_plan.recurrence,
        )
        total_completed_on_time = existing_plan.total_completed_on_time
        if plan_is == "on_time":
            total_completed_on_time += 1

        next_scheduled_at, recurrence = CompletePlanHelpers.calculate_next_schedule_and_recurrence(
            completed_at=complete_input.completed_at,
            is_plan_schedule_absolute=existing_plan.is_absolute_schedule,
            next_scheduled_at=existing_plan.next_scheduled_at,
            recurrence=existing_plan.recurrence,
            plan_is=plan_is,
        )

        archive = not next_scheduled_at or CompletePlanHelpers.should_archive(
            max_completed=existing_plan.max_completed,
            current_completed=current_completed,
        )

        archived_at = complete_input.completed_at if archive else None

        if not next_scheduled_at:
            if not archived_at:
                raise ShouldNotReachHereException(
                    f"plan {existing_plan.id} at the end of recurrence {existing_plan.recurrence}"
                )
            # If archived at, we can just keep current next schedule
            next_scheduled_at = existing_plan.next_scheduled_at

        updated_streak = CompletePlanHelpers.calculate_streak_counters(streak=existing_plan.streak, plan_is=plan_is)

        return next_scheduled_at, recurrence, archived_at, current_completed, total_completed_on_time, updated_streak

    @classmethod
    def complete_plan(cls, complete_input: CompletePlanInput, existing_plan: Plan):
        if existing_plan.recurrence:
            next_scheduled_at, recurrence, archived_at, current_completed, total_completed_on_time, updated_streak = (
                cls.get_updated_plan_fields(existing_plan=existing_plan, complete_input=complete_input)
            )
        else:
            next_scheduled_at = existing_plan.next_scheduled_at
            recurrence = None
            archived_at = complete_input.completed_at
            current_completed = existing_plan.current_completed + 1
            updated_streak = existing_plan.streak
            total_completed_on_time = existing_plan.total_completed_on_time + 1

        return Plan(
            # calculated
            archived_at=archived_at,
            current_completed=current_completed,
            next_scheduled_at=next_scheduled_at,
            streak=updated_streak,
            recurrence=recurrence,
            # from input
            type=DataType.Plan,
            id=existing_plan.id,
            rbac=existing_plan.rbac,
            metadata=existing_plan.metadata,
            name=existing_plan.name,
            template_id=existing_plan.template_id,
            is_urgent=existing_plan.is_urgent,
            is_confirmation_required=existing_plan.is_confirmation_required,
            note=existing_plan.note,
            is_absolute_schedule=existing_plan.is_absolute_schedule,
            priority=existing_plan.priority,
            prompt=existing_plan.prompt,
            first_completed_at=existing_plan.first_completed_at or complete_input.completed_at,
            max_completed=existing_plan.max_completed,
            tags=existing_plan.tags,
            total_completed_on_time=total_completed_on_time,
        )
