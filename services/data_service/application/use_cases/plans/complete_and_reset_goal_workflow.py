from asyncio import Task, TaskGroup
from datetime import datetime, timedelta, timezone
from typing import Sequence
from uuid import UUID

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts
from services.base.application.notifications.push_notification_models import PushNotificationMessage
from services.base.application.notifications.push_notification_service import PushNotificationService
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.member_user_device_repository import MemberUserDeviceRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.schemas.plan.goal import Goal
from services.base.domain.schemas.plan.plan_base import PlanBaseFields
from services.base.domain.schemas.query.boolean_query import AndQuery, NotQuery
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import ExistsQuery, RangeQuery
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor
from services.data_service.application.use_cases.plans.calculate_goal_progress_and_completion_use_case import (
    CalculateGoalProgressAndCompletionUseCase,
)


class CompleteAndResetGoalsWorkflow:
    def __init__(
        self,
        search_service: DocumentSearchService,
        calculate_goal_progress_uc: CalculateGoalProgressAndCompletionUseCase,
        push_notification_service: PushNotificationService,
        member_user_device_repository: MemberUserDeviceRepository,
        member_user_repository: MemberUserRepository,
    ):
        self._search_service = search_service
        self._calculate_goal_progress_uc = calculate_goal_progress_uc
        self._push_notification_service = push_notification_service
        self._device_repo = member_user_device_repository
        self._user_repo = member_user_repository
        workflow_counter = TelemetryInstrumentor.get_counter(name=__name__)
        self._counter = workflow_counter

    async def run(self, active_considered_period: timedelta):
        self._counter.add(1)
        now = datetime.now(timezone.utc)

        async for users in self._user_repo.yield_active_users(delta=active_considered_period):
            push_notifications: list[PushNotificationMessage] = []

            async with TaskGroup() as group:
                tasks: Sequence[Task] = [
                    group.create_task(self._handle_user(user_uuid=user.user_uuid, now=now)) for user in users
                ]

            for task in tasks:
                if result := task.result():
                    push_notifications.extend(result)

            if push_notifications:
                self._push_notification_service.publish(messages=push_notifications)

    async def _handle_user(self, user_uuid: UUID, now: datetime) -> Sequence[PushNotificationMessage]:
        device_tokens = [device.device_token for device in await self._device_repo.get_by_uuid(user_uuid=user_uuid)]
        messages = []
        q = SingleDocumentTypeQuery[Goal](
            query=AndQuery(
                queries=[
                    NotQuery(queries=[ExistsQuery(field_name=DocumentLabels.ARCHIVED_AT)]),
                    RangeQuery(field_name=PlanBaseFields.NEXT_SCHEDULED_AT, lte=now),
                    CommonLeafQueries.owner_id_value_query(user_uuid=user_uuid),
                ]
            ),
            domain_type=Goal,
        )
        continuation_token = None
        while True:
            results = await self._search_service.search_documents_by_single_query(
                query=q,
                size=100,
                sorts=CommonSorts.created_at_and_internal_id(),
                continuation_token=continuation_token,
            )
            recalculated_goals = await self._calculate_goal_progress_uc.execute(
                goals=results.documents, should_hard_complete=True
            )
            if device_tokens:
                messages.extend([self._get_goal_completion_message(goal, device_tokens) for goal in recalculated_goals])
            continuation_token = results.continuation_token
            if not continuation_token:
                break

        return messages

    def _get_goal_completion_message(self, goal: Goal, device_tokens: Sequence[str]) -> PushNotificationMessage:
        if goal.is_achieved:
            add = "and it has been reset for next window. Good luck!"
            if goal.archived_at:
                add = "and it has been archived. Great work!"
            return PushNotificationMessage(
                title=f"Goal {goal.name} Achieved",
                body="Congrats! You achieved your goal " + add,
                device_tokens=device_tokens,
            )
        else:
            add = "Your Goal has been reset for next window. Good luck!"
            if goal.archived_at:
                add = "Your goal has been archived."
            return PushNotificationMessage(
                title=f"Goal {goal.name} Failed",
                body="You made good progress. " + add,
                device_tokens=device_tokens,
            )
