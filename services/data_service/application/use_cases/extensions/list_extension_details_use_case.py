from typing import Sequence

from pydantic import Field

from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.extensions.extension_detail import ExtensionDetail
from services.base.domain.schemas.shared import BaseDataModel


class ListExtensionDetailsOutputBoundary(BaseDataModel):
    results: Sequence[ExtensionDetail] = Field(...)


class ListExtensionDetailsUseCase:
    def __init__(self, extension_repository: ExtensionDetailRepository):
        self._extension_repository = extension_repository

    async def execute_async(self) -> ListExtensionDetailsOutputBoundary:
        extension_details = await self._extension_repository.get(wrapper=ReadFromDatabaseWrapper(search_keys={}))
        return ListExtensionDetailsOutputBoundary(results=extension_details)
