from typing import List, Optional, Sequence
from uuid import UUID

from pydantic import Field

from services.base.application.boundaries.metadata_parameters_input_boundary import MetadataParametersInputBoundary
from services.base.application.boundaries.output_models import EventOutputModel
from services.base.application.boundaries.time_input import TimeRangeInput
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.sleep_stages import SleepStage
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.shared import BaseDataModel, TimeIntervalModel
from services.data_service.application.use_cases.helpers.do_list_query_helper import DoListQueryHelper


class ListSleepUseCaseOutputDetail(TimeIntervalModel):
    stage: SleepStage = Field()


class ListSleepUseCaseOutputSummary(BaseDataModel):
    efficiency: Optional[float] = Field(default=None)
    is_main_sleep: Optional[bool] = Field(default=None)
    events_count: Optional[int] = Field(default=None)
    fall_asleep_seconds: Optional[int] = Field(default=None)
    after_wakeup_seconds: Optional[int] = Field(default=None)
    awake_seconds: Optional[int] = Field(default=None)
    asleep_seconds: Optional[int] = Field(default=None)
    in_bed_seconds: Optional[int] = Field(default=None)
    deep_seconds: Optional[int] = Field(default=None)
    light_seconds: Optional[int] = Field(default=None)
    rem_seconds: Optional[int] = Field(default=None)
    restless_seconds: Optional[int] = Field(default=None)


class ListSleepUseCaseOutput(TimeIntervalModel):
    sleep_detail: Optional[List[ListSleepUseCaseOutputDetail]] = Field(default=None)
    sleep_summary: ListSleepUseCaseOutputSummary = Field()


class ListSleepUseCaseOutputItem(TimeIntervalModel, EventOutputModel):
    sleep_events: Sequence[ListSleepUseCaseOutput] = Field()


class ListSleepUseCaseOutputBoundary(BaseDataModel):
    results: Sequence[ListSleepUseCaseOutputItem]
    re_fetch_time_input: Optional[TimeRangeInput] = None


class ListSleepUseCase:
    def __init__(self, search_service: DocumentSearchService, do_list_query: DoListQueryHelper):
        self._search_service = search_service
        self._do_list_query = do_list_query

    async def execute_async(
        self,
        user_uuid: UUID,
        time_input: TimeRangeInput,
        metadata: MetadataParametersInputBoundary,
        re_fetch: bool = False,
    ) -> ListSleepUseCaseOutputBoundary:
        and_query = (
            BooleanQueryBuilder()
            .add_queries(
                queries=[
                    metadata.to_and_query(),
                    CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_uuid),
                ]
            )
            .build_and_query()
        )

        results, used_time_input = await self._do_list_query.execute_async(
            query=and_query,
            time_input=time_input,
            re_fetch=re_fetch,
            data_type=DataType.Sleep,
            return_type=ListSleepUseCaseOutputItem,
        )
        return ListSleepUseCaseOutputBoundary(
            results=results, re_fetch_time_input=used_time_input if used_time_input != time_input else None
        )
