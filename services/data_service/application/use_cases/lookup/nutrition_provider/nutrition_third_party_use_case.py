import logging

from services.base.application.exceptions import NoContentException
from services.data_service.application.use_cases.lookup.nutrition_provider.provider import (
    NutritionProvider,
    NutritionProviderOutput,
)


class NutritionThirdPartyProviderUseCase(NutritionProvider):
    def __init__(self, providers: list[NutritionProvider]):
        self._providers = providers

    async def lookup(self, name: str | None = None, image: bytes | None = None) -> NutritionProviderOutput:
        for provider in self._providers:
            try:
                return await provider.lookup(name=name, image=image)
            except Exception as error:
                logging.exception(f"could not lookup nutrition: {name}, error: {error}")
                continue
        raise NoContentException("could not lookup nutrition")
