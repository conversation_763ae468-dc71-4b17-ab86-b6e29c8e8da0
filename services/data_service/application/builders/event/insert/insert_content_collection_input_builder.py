from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.audio import AudioCategory, AudioIdentifier
from services.base.domain.schemas.events.content.content import ContentCategory, ContentIdentifier
from services.base.domain.schemas.events.content.image import ImageCategory, ImageIdentifier
from services.base.domain.schemas.events.content.interactive import InteractiveCategory, InteractiveIdentifier
from services.base.domain.schemas.events.content.text import TextCategory, TextIdentifier
from services.base.domain.schemas.events.content.video import VideoCategory, VideoIdentifier
from services.base.domain.schemas.events.event import EventPlanExtension
from services.data_service.application.builders.event.insert.insert_event_builder_base import (
    InsertEventBuilderBase,
)
from services.data_service.application.use_cases.events.models.content.insert_content_inputs import (
    InsertAudioInput,
    InsertContentInput,
    InsertImageInput,
    InsertInteractiveInput,
    InsertTextInput,
    InsertVideoInput,
)


class InsertContentInputBuilder(InsertEventBuilderBase, ContentIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertContentInput:
        return InsertContentInput(
            type=DataType.Content,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            title=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
            rating=PrimitiveTypesGenerator.generate_random_int(allow_none=True, max_value=10),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            assets=self._assets,
            url=PrimitiveTypesGenerator.generate_https_url(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=ContentCategory),
            template_id=self._template_id,
        )


class InsertAudioInputBuilder(InsertEventBuilderBase, AudioIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertAudioInput:
        return InsertAudioInput(
            type=DataType.Audio,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            title=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
            rating=PrimitiveTypesGenerator.generate_random_int(allow_none=True, max_value=10),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            assets=self._assets,
            url=PrimitiveTypesGenerator.generate_https_url(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=AudioCategory),
            template_id=self._template_id,
            plan_extension=EventPlanExtension(plan_id=self._plan_id) if self._plan_id else None,
            group_id=self._group_id,
        )


class InsertInteractiveInputBuilder(InsertEventBuilderBase, InteractiveIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertInteractiveInput:
        return InsertInteractiveInput(
            type=DataType.Interactive,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            title=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
            rating=PrimitiveTypesGenerator.generate_random_int(allow_none=True, max_value=10),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            assets=self._assets,
            url=PrimitiveTypesGenerator.generate_https_url(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=InteractiveCategory),
            template_id=self._template_id,
            plan_extension=EventPlanExtension(plan_id=self._plan_id) if self._plan_id else None,
            group_id=self._group_id,
        )


class InsertImageInputBuilder(InsertEventBuilderBase, ImageIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertImageInput:
        return InsertImageInput(
            type=DataType.Image,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            title=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
            rating=PrimitiveTypesGenerator.generate_random_int(allow_none=True, max_value=10),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            assets=self._assets,
            url=PrimitiveTypesGenerator.generate_https_url(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=ImageCategory),
            template_id=self._template_id,
            plan_extension=EventPlanExtension(plan_id=self._plan_id) if self._plan_id else None,
            group_id=self._group_id,
        )


class InsertTextInputBuilder(InsertEventBuilderBase, TextIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertTextInput:
        return InsertTextInput(
            type=DataType.Text,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            title=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
            rating=PrimitiveTypesGenerator.generate_random_int(allow_none=True, max_value=10),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            assets=self._assets,
            url=PrimitiveTypesGenerator.generate_https_url(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=TextCategory),
            template_id=self._template_id,
            plan_extension=EventPlanExtension(plan_id=self._plan_id) if self._plan_id else None,
            group_id=self._group_id,
        )


class InsertVideoInputBuilder(InsertEventBuilderBase, VideoIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> InsertVideoInput:
        return InsertVideoInput(
            type=DataType.Video,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            title=PrimitiveTypesGenerator.generate_random_string(max_length=128, allow_none=True),
            rating=PrimitiveTypesGenerator.generate_random_int(allow_none=True, max_value=10),
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            assets=self._assets,
            url=PrimitiveTypesGenerator.generate_https_url(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=VideoCategory),
            template_id=self._template_id,
            plan_extension=EventPlanExtension(plan_id=self._plan_id) if self._plan_id else None,
            group_id=self._group_id,
        )
