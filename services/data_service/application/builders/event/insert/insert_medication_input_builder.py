from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import EventPlanExtension
from services.base.domain.schemas.events.medication.medication import (
    MedicationCategory,
    MedicationIdentifier,
    SingleDoseInformation,
    WeightUnit,
)
from services.data_service.application.builders.event.insert.insert_event_builder_base import (
    InsertEventBuilderBase,
)
from services.data_service.application.use_cases.events.models.insert_medication_input import InsertMedicationInput


class InsertMedicationInputBuilder(InsertEventBuilderBase, MedicationIdentifier):

    def __init__(self):
        super().__init__()

    def build(self) -> InsertMedicationInput:
        return InsertMedicationInput(
            type=DataType.Medication,
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            name=PrimitiveTypesGenerator.generate_random_string(max_length=32),
            assets=self._assets,
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=MedicationCategory),
            template_id=self._template_id,
            consumed_amount=PrimitiveTypesGenerator.generate_random_float(max_value=100),
            consume_unit=PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            plan_extension=EventPlanExtension(plan_id=self._plan_id) if self._plan_id else None,
            group_id=self._group_id,
            single_dose_information=SingleDoseInformation(amount=100, amount_unit=WeightUnit.MG, items_quantity=1),
        )
