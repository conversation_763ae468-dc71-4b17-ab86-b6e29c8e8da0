from __future__ import annotations

from datetime import datetime, timedelta
from typing import Self, Sequence

from services.base.application.builders.input_time_interval_builder import InputTimeIntervalBuilder
from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.sleep_stages import SleepStage
from services.data_service.application.use_cases.loading.sleep.models.load_sleep_input import LoadSleepInput


class LoadSleepInputBuilder:

    def __init__(self):
        self._stage: SleepStage | None = None
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None

    def build(self) -> LoadSleepInput:
        time_interval = CustomModelsGenerator.generate_random_time_interval(
            timestamp=self._timestamp, end_time=self._end_time
        )
        timestamp = self._timestamp or time_interval.timestamp

        return LoadSleepInput(
            timestamp=timestamp,
            end_time=self._end_time
            or time_interval.end_time
            or PrimitiveTypesGenerator.generate_random_aware_datetime(gte=timestamp),
            sleep_stage=self._stage or PrimitiveTypesGenerator.generate_random_enum(enum_type=SleepStage),
        )

    def with_stage(self, stage: SleepStage) -> LoadSleepInputBuilder:
        self._stage = stage
        return self

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        return self

    def build_series(self, n: int | None = None, max_time_delta: timedelta | None = None) -> Sequence[LoadSleepInput]:
        time_intervals = InputTimeIntervalBuilder().build_series(n=n, max_time_delta=max_time_delta)
        return [
            self.with_timestamp(timestamp=t.timestamp).with_end_time(end_time=t.end_time).build()
            for t in time_intervals
        ]
