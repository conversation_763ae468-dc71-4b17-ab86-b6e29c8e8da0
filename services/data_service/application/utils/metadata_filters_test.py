import pytest

from services.base.application.boundaries.metadata_parameters_input_boundary import MetadataParametersInputBoundary
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.metadata import Organization, Service
from services.base.domain.schemas.metadata import MetadataFields
from services.base.domain.schemas.query.leaf_query import LeafQuery, ValuesQuery
from services.data_service.application.utils.metadata_filters import (
    prepare_metadata_query,
)


@pytest.fixture
def metadata_complete_input_expected_result() -> list[LeafQuery]:
    return [
        ValuesQuery(
            field_name=f"{DocumentLabels.METADATA}.{MetadataFields.ORGANIZATION}", values=[Organization.LLIF.value]
        ),
        ValuesQuery(field_name=f"{DocumentLabels.METADATA}.{MetadataFields.SERVICE}", values=[Service.DIARY.value]),
        ValuesQuery(field_name=f"{DocumentLabels.METADATA}.{MetadataFields.IMPORTANT}", values=["false"]),
        ValuesQuery(field_name=f"{DocumentLabels.METADATA}.{MetadataFields.URGENT}", values=["false"]),
        ValuesQuery(field_name=DocumentLabels.TAGS, values=["hello", "world"]),
    ]


@pytest.fixture
def metadata_partial_input_expected_result() -> list[LeafQuery]:
    return [
        ValuesQuery(
            field_name=f"{DocumentLabels.METADATA}.{MetadataFields.ORGANIZATION}", values=[Organization.LLIF.value]
        ),
        ValuesQuery(field_name=f"{DocumentLabels.METADATA}.{MetadataFields.IMPORTANT}", values=["true"]),
        ValuesQuery(field_name=f"{DocumentLabels.METADATA}.{MetadataFields.URGENT}", values=["false"]),
    ]


def test_prepare_metadata_filters_returns_empty_filters(
    metadata_empty_input: MetadataParametersInputBoundary,
):
    filters = prepare_metadata_query(metadata_input=metadata_empty_input)
    assert filters == []


def test_prepare_metadata_filters_returns_all_filters(
    metadata_complete_input: MetadataParametersInputBoundary,
    metadata_complete_input_expected_result: list[LeafQuery],
):
    queries = prepare_metadata_query(metadata_input=metadata_complete_input)
    assert queries == metadata_complete_input_expected_result


def test_prepare_metadata_filters_returns_partial_filters(
    metadata_partial_input: MetadataParametersInputBoundary,
    metadata_partial_input_expected_result: list[LeafQuery],
):
    queries = prepare_metadata_query(metadata_input=metadata_partial_input)
    assert queries == metadata_partial_input_expected_result
