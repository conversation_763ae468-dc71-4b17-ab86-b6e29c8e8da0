from enum import StrEnum
from typing import Type

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.shared_v2 import DeprEventModel
from services.base.type_resolver import TypeResolver


class UpdatableDataType(StrEnum):
    HeartRate = DataType.HeartRate
    RestingHeartRate = DataType.RestingHeartRate
    Sleep = DataType.Sleep
    Steps = DataType.Steps
    Location = DataType.Location
    ShoppingActivity = DataType.ShoppingActivity

    def to_domain_model(self) -> Type[DeprEventModel]:
        return TypeResolver.get_event_v2(type_id=self)
