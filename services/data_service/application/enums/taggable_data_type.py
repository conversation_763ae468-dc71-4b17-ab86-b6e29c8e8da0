from enum import StrEnum
from typing import Type

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import Document
from services.base.type_resolver import TypeResolver


class TaggableDataType(StrEnum):
    HeartRate = DataType.HeartRate
    RestingHeartRate = DataType.RestingHeartRate
    Sleep = DataType.Sleep
    Steps = DataType.Steps
    Location = DataType.Location
    ShoppingActivity = DataType.ShoppingActivity

    def to_domain_model(self) -> Type[Document]:
        return TypeResolver.get_document(type_id=self)
