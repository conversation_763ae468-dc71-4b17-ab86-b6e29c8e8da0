import asyncio
import logging

from services.base.telemetry.lambda_telemetry_instrumentor import LambdaTelemetryInstrumentor
from services.serverless.apps.usage_statistics_generator.application.mappings import input_data_to_report_timeframe
from services.serverless.apps.usage_statistics_generator.application.usage_statistics_use_case import (
    UsageStatisticsUseCase,
)
from services.serverless.apps.usage_statistics_generator.dependency_bootstrapper import (
    DependencyBootstrapper,
)
from settings.app_config import settings
from settings.app_secrets import secrets

LambdaTelemetryInstrumentor.initialize(service_name="usage_statistics_generator", settings=settings, secrets=secrets)


logging.info("starting usage statistics generator")
bootstrapper = DependencyBootstrapper().build()


def handler(event, context):
    logging.info("Lambda handler backend usage statistics")

    # Access the report_timeframe value directly from the event
    try:
        report_time_frame = event["report_timeframe"]
    except KeyError as e:
        logging.error(f"Error accessing key: {e}")
        raise ValueError(f"Invalid input structure or missing key in event:{event}")

    report_timeframe = input_data_to_report_timeframe[report_time_frame]

    loop = asyncio.get_event_loop()
    if loop.is_closed():
        loop = asyncio.new_event_loop()
    loop.run_until_complete(
        bootstrapper.get(interface=UsageStatisticsUseCase).execute_async(report_timeframe=report_timeframe)
    )
