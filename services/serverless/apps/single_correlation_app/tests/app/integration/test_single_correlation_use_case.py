import random
from typing import AsyncGenerator, Awaitable, Callable, Sequence
from uuid import uuid4

import pytest

from services.base.application.boundaries.documents import ExtensionResultOutputBoundary, SearchDocumentsOutputBoundary
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.extension_labels.extension_labels import ExtensionLabels
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.extension_result_repository import ExtensionResultRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.extension_output import ExtensionResult, ExtensionRun
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.tests.domain.builders.symptom_builder import SymptomBuilder
from services.base.type_resolver import TypeResolver
from services.serverless.apps.single_correlation_app.app.boundaries.single_correlation_input_boundary import (
    CorrelationInputBoundary,
)
from services.serverless.apps.single_correlation_app.app.correlation_preparer import CorrelationPreparer
from services.serverless.apps.single_correlation_app.app.models.single_correlation_models import (
    CorrelationInput,
    SingleOutcomeCorrelationResult,
)
from services.serverless.apps.single_correlation_app.app.single_correlation_use_case import SingleCorrelationUseCase
from settings.extension_constants import LLIF_EXTENSION_PROVIDER_UUID, SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID


class TestSingleCorrelationUseCase:
    @pytest.fixture
    async def user_with_random_event_data(
        self, event_repository: EventRepository, user_factory: Callable[[], Awaitable[MemberUser]]
    ) -> AsyncGenerator[tuple[MemberUser, Sequence[Event]], None]:
        user = await user_factory()

        events = []

        for i in range(random.randint(5, 10)):
            events.extend(
                SymptomBuilder()
                .with_name(PrimitiveTypesGenerator.generate_random_string())
                .with_owner_id(user.user_uuid)
                .build_n(random.randint(20, 35))
            )
        for i in range(random.randint(10, 20)):
            builder_type = random.choice(TypeResolver.EVENT_BUILDERS)
            events.extend(
                builder_type()
                .with_name(PrimitiveTypesGenerator.generate_random_string())
                .with_owner_id(user.user_uuid)
                .build_n(random.randint(20, 35))
            )

        inserted_events = await event_repository.insert(events=events, force_strong_consistency=True)

        yield user, inserted_events

        # teardown
        if inserted_events:
            await event_repository.delete_by_id(ids=[e.id for e in inserted_events])

    async def test_run_correlation_use_case_user_with_no_data_should_pass(
        self, single_correlation_uc: SingleCorrelationUseCase
    ):
        """
        This tests simulates the case, where the user has not enough events or synced data,
        The get_base_user_settings() method returns None for outcome_triggers_input in this case
        """
        # act
        stored_run, stored_results = await single_correlation_uc.execute_async(
            input_boundary=CorrelationInputBoundary(
                extension_id=SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID,
                extension_input=CorrelationInput(outcome_triggers_input=None, should_notify=True),
                provider_id=LLIF_EXTENSION_PROVIDER_UUID,
                user_id=uuid4(),
            )
        )

        # assert
        assert stored_run
        # validate the extension is serializable
        assert ExtensionRun(**stored_run.model_dump())
        for result in stored_results:
            assert ExtensionResult(**result.model_dump())

    async def test_evaluate_analytics_and_store_result_user_with_multiple_data_should_pass(
        self,
        single_correlation_uc: SingleCorrelationUseCase,
        corr_preparer: CorrelationPreparer,
        dependency_bootstrapper,
        user_with_random_event_data: tuple[MemberUser, Sequence[Event]],
    ):
        user, inserted_events = user_with_random_event_data
        user_settings_outcome_trigger = await corr_preparer.get_user_outcome_triggers(user_id=user.user_uuid)

        stored_run, stored_results = await single_correlation_uc.execute_async(
            input_boundary=CorrelationInputBoundary(
                extension_id=SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID,
                extension_input=CorrelationInput(
                    outcome_triggers_input=user_settings_outcome_trigger, should_notify=True
                ),
                provider_id=LLIF_EXTENSION_PROVIDER_UUID,
                user_id=user.user_uuid,
            )
        )

        # assert
        assert stored_run
        # validate the extension is serializable
        assert ExtensionRun(**stored_run.model_dump())
        for result in stored_results:
            assert ExtensionResult(**result.model_dump())

        # Re-fetch the results back from database
        query = (
            BooleanQueryBuilder()
            .add_queries(
                queries=[
                    ValuesQuery(
                        field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.USER_UUID}", values=[str(user.user_uuid)]
                    ),
                    (
                        ValuesQuery(
                            field_name=f"{DocumentLabels.METADATA}.{ExtensionLabels.EXTENSION_ID}",
                            values=[str(SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID)],
                        )
                    ),
                ]
            )
            .build_and_query()
        )
        query = Query(type_queries=[TypeQuery(domain_types=[ExtensionRun], query=query)])

        run_search_response: SearchDocumentsOutputBoundary[ExtensionRun] = await dependency_bootstrapper.get(
            interface=ExtensionRunRepository
        ).search_by_query(
            size=1,
            query=query,
        )
        returned_run = run_search_response.results[0].document
        # Validate Run document
        assert returned_run == stored_run
        searched_results = await dependency_bootstrapper.get(interface=ExtensionResultRepository).get_all_children(
            parent_id=returned_run.id
        )
        assert searched_results
        # Validate Result documents
        for i, searched_result in enumerate(searched_results):
            searched_result: ExtensionResultOutputBoundary
            assert searched_result.id == stored_results[i].id
            assert SingleOutcomeCorrelationResult(**searched_result.output)
