import random
from datetime import datetime, timedelta

import pytest

from services.serverless.apps.single_correlation_app.app.correlation_data_transformer import CorrelationDataTransformer


def _generate_sample_timestamps(num_timestamps) -> list[datetime]:
    return list(
        set(
            [
                datetime(
                    year=random.randint(2022, 2023),
                    month=random.randint(1, 12),
                    day=random.randint(1, 28),
                    hour=random.randint(0, 23),
                    minute=random.randint(0, 59),
                )
                for _ in range(num_timestamps)
            ]
        )
    )


class TestCorrelationDataTransformer:
    @pytest.mark.parametrize(
        "sample_timestamps,active_days",
        [(_generate_sample_timestamps(num_timestamps=20), _generate_sample_timestamps(num_timestamps=50))],
    )
    def test_create_negative_outcome_timestamps(self, sample_timestamps: list[datetime], active_days: list[datetime]):
        new_timestamps = CorrelationDataTransformer.create_negative_outcome_timestamps(
            outcome_timestamps=sample_timestamps, active_days=active_days
        )
        for timestamp in new_timestamps:
            assert isinstance(timestamp, datetime)
        assert len(sample_timestamps + new_timestamps) >= len(sample_timestamps) * 2

    @pytest.mark.parametrize(
        "outcomes,triggers,top_boundary,low_boundary",
        [(_generate_sample_timestamps(60), _generate_sample_timestamps(120), timedelta(days=2), timedelta(days=0))],
    )
    def test_create_df_from_timestamps(
        self, outcomes: list[datetime], triggers: list[datetime], top_boundary: timedelta, low_boundary: timedelta
    ):
        random.shuffle(outcomes)
        positive_outcomes = outcomes[: len(outcomes) // 2]
        negative_outcomes = outcomes[len(outcomes) // 2 :]
        df = CorrelationDataTransformer.create_df_from_timestamps(
            positive_outcomes=positive_outcomes,
            negative_outcomes=negative_outcomes,
            triggers=triggers,
            top_boundary=top_boundary,
            low_boundary=low_boundary,
        )
        assert len(df) == len(outcomes)
        for trigger_count in set(df["Trigger Count"].values):
            assert trigger_count >= 0
        assert set(df["Outcome Value"].values) == {-1, 1}
