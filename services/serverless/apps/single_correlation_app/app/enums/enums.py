from enum import StrEnum


class CorrelationConfidence(StrEnum):
    HIGH_CONFIDENCE = "high_confidence"
    LOW_CONFIDENCE = "low_confidence"


class CorrelationInputTypes(StrEnum):
    OUTCOME = "outcome"
    TRIGGER = "trigger"


class ResultImplication(StrEnum):
    NO_CORRELATION = "No Correlation"
    VERY_WEAK = "Very Weak"
    WEAK = "Weak"
    MODERATE = "Moderate"
    STRONG = "Strong"
    VERY_STRONG = "Very Strong"
