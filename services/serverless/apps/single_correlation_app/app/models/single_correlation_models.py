from datetime import <PERSON><PERSON><PERSON>
from typing import Optional, Sequence

from pydantic import AwareDatetime, Field

from services.base.domain.annotated_types import RoundedFloat
from services.base.domain.constants.extension_labels.single_correlation_labels import SingleCorrelationLabels
from services.base.domain.enums.event_v3_type import EventV3Type
from services.base.domain.schemas.extension_output import ExtensionResult, ExtensionRun
from services.base.domain.schemas.shared import BaseDataModel
from services.serverless.apps.single_correlation_app.app.enums.enums import (
    CorrelationConfidence,
    CorrelationInputTypes,
)


class SingleCorrelationSingleInput(BaseDataModel):
    """
    This DataModel is used to store information about either an outcome or a trigger, differentiated by the input_type
    field. Next contains the schema, correlation type, names and confidence. The names field is a list which can contain
    either one value in the case of DiaryEvents or more values indicating column names for other defined datatypes.
    Confidence relates how many outcome events were recorded, if over 30 we say the extension works with high confidence
    under 30 but over 10 low confidence.
    """

    event_type: EventV3Type = Field()
    input_type: CorrelationInputTypes = Field()
    name: str = Field(description="field name of the outcome or trigger")
    confidence: Optional[CorrelationConfidence] = Field(default=None, alias=SingleCorrelationLabels.CONFIDENCE)


class FetchedDocumentOutputItem(BaseDataModel):
    input_object: SingleCorrelationSingleInput
    timestamps: Sequence[AwareDatetime] | None = Field(min_length=1)


class OutcomeTriggersObject(BaseDataModel):
    """Defines between what outcome and triggers we want to calculate correlation.
    Example: outcome headache, triggers all events that preceded it"""

    outcome: SingleCorrelationSingleInput
    triggers: Sequence[SingleCorrelationSingleInput] = Field(min_length=1)


class CorrelationInput(BaseDataModel):
    outcome_triggers_input: Optional[Sequence[OutcomeTriggersObject]] = Field(default=None, min_length=1)
    historical_offset: timedelta | None = Field(default=None)
    should_notify: bool


class EligibleCorrelationInput(BaseDataModel):
    eligible_outcome_events: Optional[Sequence[str]] = Field(
        alias="eligible_outcome_events",
        default=None,
        min_length=1,
        description="Collection of outcomes that are used to create outcome trigger pairs",
    )
    eligible_triggers_events: Optional[Sequence[str]] = Field(
        alias="eligible_triggers_events",
        default=None,
        min_length=1,
        description="Collection of triggers that are used to create outcome trigger pairs",
    )


class CorrelationResultsSummary(BaseDataModel):
    very_weak_count: int = Field(default=0, description="summarizes how many correlations are very low")
    weak_count: int = Field(default=0, description="summarizes how many correlations are low")
    moderate_count: int = Field(default=0, description="summarizes how many correlations are medium")
    strong_count: int = Field(default=0, description="summarizes how many correlations are high")
    very_strong_count: int = Field(default=0, description="summarizes how many correlations are very high")


class EventCorrelationResults(BaseDataModel):
    immediate_term: RoundedFloat = Field(ge=-1, le=1, alias=SingleCorrelationLabels.IMMEDIATE_TERM)
    short_term: RoundedFloat = Field(ge=-1, le=1, alias=SingleCorrelationLabels.SHORT_TERM)
    medium_term: RoundedFloat = Field(ge=-1, le=1, alias=SingleCorrelationLabels.MEDIUM_TERM)
    long_term: RoundedFloat = Field(ge=-1, le=1, alias=SingleCorrelationLabels.LONG_TERM)


class SingleCorrelationSingleOutput(BaseDataModel):
    trigger_name: str = Field(alias=SingleCorrelationLabels.TRIGGER_NAME)
    trigger_document_count: int = Field(alias=SingleCorrelationLabels.TRIGGER_DOCUMENT_COUNT)
    correlation: EventCorrelationResults = Field(alias=SingleCorrelationLabels.CORRELATION)
    max_correlation: RoundedFloat = Field(ge=-1, le=1, alias=SingleCorrelationLabels.MAX_CORRELATION)
    confidence: Optional[CorrelationConfidence] = Field(default=None, alias=SingleCorrelationLabels.CONFIDENCE)
    result_implication: Optional[str] = Field(default=None, alias=SingleCorrelationLabels.RESULT_IMPLICATION)


class SingleOutcomeCorrelationResult(BaseDataModel):
    # Result model that stores individual results for a single outcome
    outcome_name: str = Field(alias=SingleCorrelationLabels.OUTCOME_NAME)
    outcome_document_count: int = Field(alias=SingleCorrelationLabels.OUTCOME_DOCUMENT_COUNT)
    single_correlation_results: list[SingleCorrelationSingleOutput] = Field(
        alias=SingleCorrelationLabels.SINGLE_CORRELATION_RESULTS
    )


class CorrelationOutputModel(BaseDataModel):
    correlation_run_document: ExtensionRun
    correlation_result_documents: list[ExtensionResult]
