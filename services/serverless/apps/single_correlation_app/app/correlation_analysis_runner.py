import logging
from datetime import datetime, timedelta, timezone
from typing import Sequence
from uuid import UUID

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import CommonSorts, SortOrder
from services.base.domain.schemas.events.event import EventFields
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.serverless.apps.single_correlation_app.app.event_to_event_correlation_analyzer import (
    EventToEventCorrelationAnalyzer,
)
from services.serverless.apps.single_correlation_app.app.models.single_correlation_models import (
    FetchedDocumentOutputItem,
    SingleCorrelationSingleInput,
    SingleCorrelationSingleOutput,
    SingleOutcomeCorrelationResult,
)


class CorrelationAnalysisRunner:
    def __init__(
        self,
        search_service: DocumentSearchService,
        corr_analyzer: EventToEventCorrelationAnalyzer,
    ):
        self._search_service = search_service
        self._corr_analyzer = corr_analyzer

    async def run(
        self,
        outcome: SingleCorrelationSingleInput,
        triggers: Sequence[SingleCorrelationSingleInput],
        user_id: UUID,
        historical_offset: timedelta | None,
        document_cache: dict[str, FetchedDocumentOutputItem],
        active_days: Sequence[datetime],
    ) -> SingleOutcomeCorrelationResult:
        # Fetch outcome documents from cache or service
        if outcome.name not in document_cache:
            logging.info(f"Fetching documents for outcome: {outcome.name}")
            outcome_documents = await self._fetch_documents(
                input_object=outcome,
                user_id=user_id,
                historical_offset=historical_offset,
            )
            document_cache[outcome.name] = outcome_documents
        else:
            outcome_documents = document_cache[outcome.name]

        single_outcome_correlation_result = SingleOutcomeCorrelationResult(
            outcome_name=outcome.name,
            outcome_document_count=len(outcome_documents.timestamps) if outcome_documents.timestamps else 0,
            single_correlation_results=[],
        )
        if outcome_documents.timestamps:
            for trigger in triggers:
                # Fetch trigger documents from cache or service
                if trigger.name not in document_cache:
                    logging.info(f"Outcome: {outcome.name}, fetching documents for trigger: {trigger.name}")
                    trigger_documents = await self._fetch_documents(
                        input_object=trigger,
                        user_id=user_id,
                        historical_offset=historical_offset,
                    )
                    document_cache[trigger.name] = trigger_documents
                else:
                    trigger_documents = document_cache[trigger.name]

                if trigger_documents.timestamps:
                    result: SingleCorrelationSingleOutput = await self._corr_analyzer.analyze(
                        active_days=active_days,
                        outcome_documents=outcome_documents,
                        trigger_documents=trigger_documents,
                    )
                    if abs(result.max_correlation) > 0.1:
                        single_outcome_correlation_result.single_correlation_results.append(result)

        single_outcome_correlation_result.single_correlation_results.sort(key=lambda x: x.max_correlation, reverse=True)
        return single_outcome_correlation_result

    async def _fetch_documents(
        self,
        input_object: SingleCorrelationSingleInput,
        user_id: UUID,
        historical_offset: timedelta | None = None,
    ) -> FetchedDocumentOutputItem:
        bool_query = (
            BooleanQueryBuilder()
            .add_queries(
                queries=[
                    (
                        CommonLeafQueries.timestamp_range_query(
                            gte=datetime.now(tz=timezone.utc) - historical_offset, lte=None
                        )
                        if historical_offset
                        else None
                    ),
                    CommonLeafQueries.owner_id_value_query(user_uuid=user_id),
                    ValuesQuery(values=[input_object.name], field_name=EventFields.NAME),
                ]
            )
            .build_and_query()
        )

        type_query = TypeQuery(domain_types=[input_object.event_type.to_event_model()], query=bool_query)
        query = Query(type_queries=[type_query])

        response = await self._search_service.search_documents_by_query(
            query=query,
            sorts=[CommonSorts.timestamp(order=SortOrder.DESCENDING)],
            size=10_000,
        )
        all_documents: list = []
        all_documents.extend(response.documents)  # pyright: ignore
        while response.continuation_token:
            response = await self._search_service.search_documents_by_query(
                query=query,
                sorts=[CommonSorts.timestamp(order=SortOrder.DESCENDING)],
                size=10_000,
                continuation_token=response.continuation_token,
            )
            all_documents.extend(response.documents)  # pyright: ignore
        if not all_documents:
            return FetchedDocumentOutputItem(timestamps=None, input_object=input_object)

        # TODO Extract value from document
        return FetchedDocumentOutputItem(
            timestamps=[d.timestamp.astimezone(timezone.utc) for d in all_documents], input_object=input_object
        )
