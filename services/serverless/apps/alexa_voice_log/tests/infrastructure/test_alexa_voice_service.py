from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4

import pytest

from services.base.domain.schemas.member_user.login_google import LoginGoogle
from services.base.domain.schemas.member_user.member_user_oauth2 import MemberUserOAuth2
from services.serverless.apps.alexa_voice_log.application.voice_exceptions import (
    UserAlreadyLinkedToAnotherAccountException,
    UserNotRegisteredException,
)
from services.serverless.apps.alexa_voice_log.infrastructure.alexa_voice_service import AlexaVoiceService


@pytest.mark.asyncio
async def test_log_alexa_voice_service_get_member_oauth_data_provider_account_not_found_should_raise():
    # Arrange
    login_google_repository_mock = AsyncMock()
    member_user_oauth_repository_mock = AsyncMock()
    login_google_repository_mock.get_by_google_id.return_value = None

    alexa_voice_service = AlexaVoiceService(
        login_google_repository=login_google_repository_mock,
        member_user_oauth2_repository=member_user_oauth_repository_mock,
    )
    alexa_voice_service._exchange_access_token_for_google_data = MagicMock()
    alexa_voice_service._exchange_access_token_for_google_data.return_value = {"test": "test"}
    # Act
    with pytest.raises(AttributeError):
        # Assert
        await alexa_voice_service.get_member_oauth_data(provider_id="testid", linked_access_token="test")


@pytest.mark.asyncio
async def test_log_alexa_voice_service_get_member_oauth_data_no_registered_account_should_raise():
    # Arrange
    login_google_repository_mock = AsyncMock()
    member_user_oauth_repository_mock = AsyncMock()
    login_google_repository_mock.get_by_google_id.return_value = None

    alexa_voice_service = AlexaVoiceService(
        login_google_repository=login_google_repository_mock,
        member_user_oauth2_repository=member_user_oauth_repository_mock,
    )
    alexa_voice_service._exchange_access_token_for_google_data = MagicMock()
    alexa_voice_service._exchange_access_token_for_google_data.return_value = {"id": "googleid"}
    # Act
    with pytest.raises(UserNotRegisteredException):
        # Assert
        await alexa_voice_service.get_member_oauth_data(provider_id="testid", linked_access_token="test")


@pytest.mark.asyncio
async def test_log_alexa_voice_service_get_member_oauth_data_another_user_already_linked_should_raise():
    # Arrange
    login_google_repository_mock = AsyncMock()
    member_user_oauth_repository_mock = AsyncMock()
    desired_return_value = MemberUserOAuth2(user_uuid=uuid4(), provider="google", provider_user_id="1234")
    member_user_oauth_repository_mock.get_by_primary_key.return_value = desired_return_value

    alexa_voice_service = AlexaVoiceService(
        login_google_repository=login_google_repository_mock,
        member_user_oauth2_repository=member_user_oauth_repository_mock,
    )

    alexa_voice_service._exchange_access_token_for_google_data = MagicMock()
    alexa_voice_service._exchange_access_token_for_google_data.return_value = {"id": "googleid"}
    # Act
    with pytest.raises(UserAlreadyLinkedToAnotherAccountException):
        # Assert
        await alexa_voice_service.get_member_oauth_data(provider_id="testid", linked_access_token="test")


@pytest.mark.asyncio
async def test_log_alexa_voice_service_get_member_oauth_data_alexa_not_linked_should_be_created():
    # Arrange
    uuid = uuid4()
    google_id = "googleid"
    login_google_repository_mock = AsyncMock()

    login_google_repository_mock.get_by_google_id.return_value = LoginGoogle(user_uuid=uuid, google_id=google_id)
    desired_return_value = MemberUserOAuth2(user_uuid=uuid4(), provider="google", provider_user_id=google_id)

    member_user_oauth_repository_mock = AsyncMock()
    member_user_oauth_repository_mock.get_by_primary_key.return_value = None
    member_user_oauth_repository_mock.insert_or_update.return_value = desired_return_value

    alexa_voice_service = AlexaVoiceService(
        login_google_repository=login_google_repository_mock,
        member_user_oauth2_repository=member_user_oauth_repository_mock,
    )

    alexa_voice_service._exchange_access_token_for_google_data = MagicMock()
    alexa_voice_service._exchange_access_token_for_google_data.return_value = {"id": "googleid"}

    # Act
    user_oauth_data = await alexa_voice_service.get_member_oauth_data(provider_id="testid", linked_access_token="test")
    # Assert
    member_user_oauth_repository_mock.insert_or_update.assert_awaited()
    assert user_oauth_data == desired_return_value
