from typing import Union

from ask_sdk_core.dispatch_components.request_components import <PERSON>bstract<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ask_sdk_core.handler_input import HandlerInput
from ask_sdk_core.utils.predicate import is_request_type
from ask_sdk_model.response import Response

from services.serverless.apps.alexa_voice_log.domain.phrases import EXAMPLE_COMMAND_USAGE
from services.serverless.apps.alexa_voice_log.domain.request_types import RequestType


class LaunchRequestHandler(AbstractRequestHandler):
    """Handler for launching the Best Life skill action"""

    def can_handle(self, handler_input: HandlerInput) -> bool:
        return is_request_type(RequestType.LAUNCH_REQUEST)(handler_input)

    def handle(self, handler_input: HandlerInput) -> Union[None, Response]:
        speak_output = f"Welcome to Best Life! You can say, {EXAMPLE_COMMAND_USAGE}. What do you want to log?"

        handler_input.response_builder.speak(speak_output).set_should_end_session(False)

        return handler_input.response_builder.response
