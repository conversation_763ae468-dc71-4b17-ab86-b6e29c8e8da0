from typing import Union

from ask_sdk_core.dispatch_components.request_components import <PERSON>bstractRe<PERSON><PERSON><PERSON><PERSON>
from ask_sdk_core.handler_input import HandlerInput
from ask_sdk_core.utils.predicate import is_intent_name
from ask_sdk_model.response import Response

from services.serverless.apps.alexa_voice_log.domain.intent_names import IntentName


class CancelOrStopIntentHandler(AbstractRequestHandler):
    """Single handler for Cancel and Stop Intent."""

    def can_handle(self, handler_input: HandlerInput) -> bool:
        is_cancel_intent: bool = is_intent_name(IntentName.CANCEL_INTENT)(handler_input)
        is_stop_intent: bool = is_intent_name(IntentName.STOP_INTENT)(handler_input)
        return is_cancel_intent or is_stop_intent

    def handle(self, handler_input: HandlerInput) -> Union[None, Response]:
        speak_output = "Goodbye!"
        return handler_input.response_builder.speak(speak_output).response
