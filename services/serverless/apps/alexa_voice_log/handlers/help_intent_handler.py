from typing import Union

from ask_sdk_core.dispatch_components.request_components import <PERSON>bstract<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from ask_sdk_core.handler_input import HandlerInput
from ask_sdk_core.utils.predicate import is_intent_name
from ask_sdk_model.response import Response

from services.serverless.apps.alexa_voice_log.domain.intent_names import IntentName
from services.serverless.apps.alexa_voice_log.domain.phrases import EXAMPLE_COMMAND_USAGE


class HelpIntentHandler(AbstractRequestHandler):
    """Handler for Help Intent."""

    def can_handle(self, handler_input: HandlerInput) -> bool:
        return is_intent_name(IntentName.HELP_INTENT)(handler_input)

    def handle(self, handler_input: HandlerInput) -> Union[None, Response]:
        speak_output = f"You can say something like {EXAMPLE_COMMAND_USAGE}."
        return handler_input.response_builder.speak(speak_output).ask(speak_output).response
