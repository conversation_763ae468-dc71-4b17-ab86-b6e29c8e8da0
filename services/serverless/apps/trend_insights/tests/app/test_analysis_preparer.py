import logging
from datetime import datetime, timedelta, timezone

import pandas as pd
from dateutil.relativedelta import relativedelta

from services.base.application.boundaries.time_input import TimeInput
from services.base.application.database.aggregation_service import AggregationService
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.domain.constants.extension_labels.trend_insights_labels import TrendInsightsLabels
from services.base.domain.schemas.events.feeling.emotion import EmotionCategory
from services.base.domain.schemas.events.feeling.stress import StressCategory
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.steps import Steps
from services.serverless.apps.trend_insights.app.analysis_preparer import AnalysisPreparer
from services.serverless.apps.trend_insights.app.analytic_mappings import AnalyticTypeToSeriesMapping
from services.serverless.apps.trend_insights.app.models.analytic_type import AnalyticType
from settings.app_constants import DEMO1_UUID


async def test_prepare_analysis_time_input():
    analysis_time_input = await AnalysisPreparer.prepare_analysis_time_input()
    yesterday_midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0) - relativedelta(
        days=1
    )
    time_gte_list = [
        yesterday_midnight - relativedelta(days=28),
        yesterday_midnight - relativedelta(days=120),
        yesterday_midnight - relativedelta(days=364),
    ]
    for analytic_time_input in analysis_time_input:
        assert analytic_time_input[TrendInsightsLabels.AGGREGATION_INTERVAL] in ["7d", "30d", "91d"]
        assert analytic_time_input[TrendInsightsLabels.TIME_INPUT].time_gte in time_gte_list


async def test_get_user_data_with_stress_data_present(search_service: DocumentSearchService, user_with_stress_data):
    user, inserted_stress = user_with_stress_data
    analytic_type = AnalyticType.Stress
    viable_series = [
        StressCategory.PHYSICAL_ACTIVITY.value,
        StressCategory.MENTAL_ACTIVITY.value,
        StressCategory.SOCIAL_ACTIVITY.value,
    ]
    today_midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
    time_input = TimeInput(
        interval="1d", time_gte=today_midnight - relativedelta(days=28), time_lte=today_midnight - timedelta(seconds=1)
    )
    for series in viable_series:
        series: str
        stress_docs = [doc for doc in inserted_stress if doc.category == series]
        input_dataframe = await AnalysisPreparer.get_analysis_input_dataframe(
            current_uuid=user.user_uuid,
            time_input=time_input,
            analytic_type=analytic_type,
            search_service=search_service,
            analytic_series=series,
        )
        assert input_dataframe is not None
        for rating, expected_item in zip(input_dataframe[series].values, stress_docs):
            assert rating == expected_item.rating


async def test_get_user_data_with_emotion_data_present(search_service: DocumentSearchService, user_with_emotion_data):
    user, emotion = user_with_emotion_data
    analytic_type = AnalyticType.Emotion
    today_midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
    time_input = TimeInput(
        interval="1d", time_gte=today_midnight - relativedelta(days=28), time_lte=today_midnight - timedelta(seconds=1)
    )
    viable_series = [EmotionCategory.MOOD.value, "PartnerTrust"]
    for series in viable_series:
        series: str
        inserted_series_emotion = [emotion_doc for emotion_doc in emotion if emotion_doc.name == series]
        input_dataframe = await AnalysisPreparer.get_analysis_input_dataframe(
            current_uuid=user.user_uuid,
            time_input=time_input,
            analytic_type=analytic_type,
            search_service=search_service,
            analytic_series=series,
        )
        assert input_dataframe is not None
        for rating, expected_item in zip(input_dataframe[series].values, inserted_series_emotion):
            assert rating == expected_item.rating


async def test_get_user_data_with_events_nutrition_data_present(
    search_service: DocumentSearchService, user_with_events_nutrition_data: tuple[MemberUser, pd.DataFrame]
):
    user, nutrition_dataframe = user_with_events_nutrition_data
    today_midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
    time_input = TimeInput(
        interval="1d", time_gte=today_midnight - relativedelta(days=28), time_lte=today_midnight - timedelta(seconds=1)
    )
    for analytic_type in [AnalyticType.Nutrition]:
        viable_series = AnalyticTypeToSeriesMapping[analytic_type]
        for series in viable_series:
            input_dataframe = await AnalysisPreparer.get_analysis_input_dataframe(
                current_uuid=user.user_uuid,
                time_input=time_input,
                analytic_type=analytic_type,
                search_service=search_service,
                analytic_series=series,
            )
            assert input_dataframe is not None
            for value, expected_value in zip(input_dataframe[series].values, nutrition_dataframe[series].values):
                logging.info(f"series: {series}, value: {value}, expected_value: {expected_value}")
                assert value == expected_value


async def test_get_user_data_with_steps_data_present(
    search_service: DocumentSearchService, user_with_steps_data: tuple[MemberUser, list[int]]
):
    user, steps = user_with_steps_data
    analytic_type = AnalyticType.Steps
    today_midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
    time_input = TimeInput(
        interval="1d", time_gte=today_midnight - relativedelta(days=28), time_lte=today_midnight - timedelta(seconds=1)
    )
    viable_series = AnalyticTypeToSeriesMapping[analytic_type]
    for series in viable_series:
        input_data_frame = await AnalysisPreparer.get_analysis_input_dataframe(
            current_uuid=user.user_uuid,
            time_input=time_input,
            analytic_type=analytic_type,
            search_service=search_service,
            analytic_series=series,
        )
        assert input_data_frame is not None
        for value, expected_item in zip(input_data_frame[series].values, steps):
            assert value == expected_item


async def test_get_user_data_with_steps_and_three_org_present(
    search_service: DocumentSearchService, user_with_steps_data_three_orgs: tuple[MemberUser, list[Steps]]
):
    user, steps = user_with_steps_data_three_orgs
    analytic_type = AnalyticType.Steps
    today_midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
    time_input = TimeInput(
        interval="1d", time_gte=today_midnight - relativedelta(days=28), time_lte=today_midnight - timedelta(seconds=1)
    )
    viable_series = AnalyticTypeToSeriesMapping[analytic_type]
    for series in viable_series:
        input_dataframe = await AnalysisPreparer.get_analysis_input_dataframe(
            current_uuid=user.user_uuid,
            time_input=time_input,
            analytic_type=analytic_type,
            search_service=search_service,
            analytic_series=series,
        )
        assert input_dataframe is not None
        for value, expected_item in zip(input_dataframe[series].values, steps):
            assert value == expected_item


async def test_get_user_data_no_data_present_return_none(search_service: DocumentSearchService):
    current_uuid = DEMO1_UUID
    analytic_type = AnalyticType.RestingHeartRate
    today_midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
    time_input = TimeInput(
        interval="1d", time_gte=today_midnight - relativedelta(days=28), time_lte=today_midnight - timedelta(seconds=1)
    )
    viable_series = AnalyticTypeToSeriesMapping[analytic_type]
    for series in viable_series:
        input_dataframe = await AnalysisPreparer.get_analysis_input_dataframe(
            current_uuid=current_uuid,
            time_input=time_input,
            analytic_type=analytic_type,
            search_service=search_service,
            analytic_series=series,
        )
        assert input_dataframe is None or input_dataframe.empty


async def test_get_body_metric_track_names_from_v3_type(
    aggregation_service: AggregationService, user_with_body_metric_v3_data
):
    user, inserted_body_metric = user_with_body_metric_v3_data
    today_midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
    time_input = TimeInput(
        interval="1d", time_gte=today_midnight - relativedelta(days=28), time_lte=today_midnight - timedelta(seconds=1)
    )
    viable_track_names = await AnalysisPreparer.identify_viable_series_from_v3_type(
        current_uuid=user.user_uuid,
        analytic_type=AnalyticType.BodyMetric,
        aggregation_service=aggregation_service,
        time_input=time_input,
    )
    expected_names = []
    for body_metric_event in inserted_body_metric:
        expected_names.append(body_metric_event.name)

    for name in viable_track_names:
        assert name in set(expected_names)
