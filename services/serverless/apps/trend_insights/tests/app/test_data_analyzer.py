from datetime import datetime, timezone

import pytest
from pandas import Series

from services.base.domain.enums.analytics.trend_insights import TrendDirection
from services.base.domain.schemas.shared import TimeIntervalModel
from services.serverless.apps.trend_insights.app.data_analyzer import DataAnalyzer
from services.serverless.apps.trend_insights.app.models.analysis_models import AnalysisTrendOutputModel
from services.serverless.apps.trend_insights.app.models.analytic_series_models import (
    SeriesAnalysisAveragedOutputBucket,
)


@pytest.mark.parametrize(
    "test_input,expected_result",
    [
        (
            {"series": [70, 80, 90, 100]},
            AnalysisTrendOutputModel(
                consecutive_trend=TrendDirection.DECREASING,
                independent_variables_mean=90,
                absolute_difference_from_previous_bucket=-10,
                absolute_difference_from_aggregated_buckets=-20,
                percentage_difference_from_aggregated_buckets=-22.222,
                percentage_difference_from_previous_bucket=-12.5,
            ),
        ),
        (
            {"series": [70, 80, 70, 80]},
            AnalysisTrendOutputModel(
                consecutive_trend=TrendDirection.NON_MONOTONOUS,
                independent_variables_mean=76.667,
                absolute_difference_from_previous_bucket=-10,
                absolute_difference_from_aggregated_buckets=-6.667,
                percentage_difference_from_aggregated_buckets=-8.696,
                percentage_difference_from_previous_bucket=-12.5,
            ),
        ),
        (
            {"series": [70, 80, 80, None]},
            AnalysisTrendOutputModel(
                consecutive_trend=TrendDirection.DECREASING,
                independent_variables_mean=80,
                absolute_difference_from_previous_bucket=-10,
                absolute_difference_from_aggregated_buckets=-10,
                percentage_difference_from_aggregated_buckets=-12.5,
                percentage_difference_from_previous_bucket=-12.5,
            ),
        ),
        (
            {"series": [80, 80, 70, None]},
            AnalysisTrendOutputModel(
                consecutive_trend=TrendDirection.INCREASING,
                independent_variables_mean=75,
                absolute_difference_from_previous_bucket=0,
                absolute_difference_from_aggregated_buckets=5,
                percentage_difference_from_aggregated_buckets=6.667,
                percentage_difference_from_previous_bucket=0,
            ),
        ),
        (
            {"series": [100, 90, 80, 70]},
            AnalysisTrendOutputModel(
                consecutive_trend=TrendDirection.INCREASING,
                independent_variables_mean=80,
                absolute_difference_from_previous_bucket=10,
                absolute_difference_from_aggregated_buckets=20,
                percentage_difference_from_aggregated_buckets=25,
                percentage_difference_from_previous_bucket=11.111,
            ),
        ),
        (
            {"series": [70, 20, None, None]},
            AnalysisTrendOutputModel(
                consecutive_trend=TrendDirection.INCREASING,
                independent_variables_mean=20,
                absolute_difference_from_previous_bucket=50,
                absolute_difference_from_aggregated_buckets=50,
                percentage_difference_from_aggregated_buckets=250,
                percentage_difference_from_previous_bucket=250,
            ),
        ),
        ({"series": [20, 20, 20, 20]}, None),
        (
            {"series": [0.5, 80, 90, 100]},
            AnalysisTrendOutputModel(
                consecutive_trend=TrendDirection.DECREASING,
                independent_variables_mean=90,
                absolute_difference_from_previous_bucket=-79.5,
                absolute_difference_from_aggregated_buckets=-89.5,
                percentage_difference_from_aggregated_buckets=-99.444,
                percentage_difference_from_previous_bucket=-99.375,
            ),
        ),
        (
            {"series": [35000, 20, None, None]},
            AnalysisTrendOutputModel(
                consecutive_trend=TrendDirection.INCREASING,
                independent_variables_mean=20,
                absolute_difference_from_previous_bucket=34980.0,
                absolute_difference_from_aggregated_buckets=34980.0,
                percentage_difference_from_aggregated_buckets=174900.0,
                percentage_difference_from_previous_bucket=174900.0,
            ),
        ),
        (
            {"series": [None, 20, 30, 40]},
            None,
        ),
        (
            {"series": [2, -1, 1, 0]},
            AnalysisTrendOutputModel(
                consecutive_trend=TrendDirection.NON_MONOTONOUS,
                independent_variables_mean=0,
                absolute_difference_from_previous_bucket=3,
                absolute_difference_from_aggregated_buckets=2,
                percentage_difference_from_aggregated_buckets=0,
                percentage_difference_from_previous_bucket=-300,
            ),
        ),
        (
            {"series": [2, 0, 1, -1]},
            AnalysisTrendOutputModel(
                consecutive_trend=TrendDirection.NON_MONOTONOUS,
                independent_variables_mean=0,
                absolute_difference_from_previous_bucket=2,
                absolute_difference_from_aggregated_buckets=2,
                percentage_difference_from_aggregated_buckets=0,
                percentage_difference_from_previous_bucket=0,
            ),
        ),
    ],
)
def test_calculate_trend_valid_input_should_pass(test_input, expected_result):
    buckets = [
        SeriesAnalysisAveragedOutputBucket(
            aggregated_value=test_input["series"][0],
            time_range=TimeIntervalModel(
                timestamp=datetime(2021, 10, 2, 0, 0, 0, tzinfo=timezone.utc),
                end_time=datetime(2021, 10, 9, 0, 0, 0, tzinfo=timezone.utc),
                duration=604800,
            ),
        ),
        SeriesAnalysisAveragedOutputBucket(
            aggregated_value=test_input["series"][1],
            time_range=TimeIntervalModel(
                timestamp=datetime(2021, 9, 25, 0, 0, 0, tzinfo=timezone.utc),
                end_time=datetime(2021, 10, 2, 0, 0, 0, tzinfo=timezone.utc),
                duration=604800,
            ),
        ),
        SeriesAnalysisAveragedOutputBucket(
            aggregated_value=test_input["series"][2],
            time_range=TimeIntervalModel(
                timestamp=datetime(2021, 9, 18, 0, 0, 0, tzinfo=timezone.utc),
                end_time=datetime(2021, 9, 25, 0, 0, 0, tzinfo=timezone.utc),
                duration=604800,
            ),
        ),
        SeriesAnalysisAveragedOutputBucket(
            aggregated_value=test_input["series"][3],
            time_range=TimeIntervalModel(
                timestamp=datetime(2021, 9, 11, 0, 0, 0, tzinfo=timezone.utc),
                end_time=datetime(2021, 9, 18, 0, 0, 0, tzinfo=timezone.utc),
                duration=604800,
            ),
        ),
    ]
    trend = DataAnalyzer.calculate_trend(buckets=buckets)
    if trend:
        assert trend.consecutive_trend == expected_result.consecutive_trend
        assert trend.independent_variables_mean == expected_result.independent_variables_mean
        assert (
            trend.absolute_difference_from_previous_bucket == expected_result.absolute_difference_from_previous_bucket
        )
        assert (
            trend.absolute_difference_from_aggregated_buckets
            == expected_result.absolute_difference_from_aggregated_buckets
        )
        assert (
            trend.percentage_difference_from_previous_bucket
            == expected_result.percentage_difference_from_previous_bucket
        )
        assert (
            trend.percentage_difference_from_aggregated_buckets
            == expected_result.percentage_difference_from_aggregated_buckets
        )


@pytest.mark.parametrize(
    "test_input,expected_result",
    [
        ({"series": Series([1])}, (1, 1, 1, 0, 1, 1, 1)),
        ({"series": Series([70, 80, 90, 100, 105, 90])}, (105, 70, 89.167, 12.813, 97.5, 82.5, 535)),
        ({"series": Series([70, 80, 70, 80, 110, 105])}, (110, 70, 85.833, 17.44, 98.75, 72.5, 515)),
        ({"series": Series([70, 80, 80, 60, 65])}, (80, 60, 71.0, 8.944, 80, 65, 355)),
        ({"series": Series([80, 80, 70, 60, 90, 80])}, (90, 60, 76.667, 10.328, 80, 72.5, 460)),
        ({"series": Series([100, 90, 80, 70, 65, 70])}, (100, 65, 79.167, 13.571, 87.5, 70, 475)),
        ({"series": Series([100])}, (100, 100, 100, 0, 100, 100, 100)),
    ],
)
def test_calculate_descriptive_statistics_valid_input_should_pass(test_input, expected_result):
    result = DataAnalyzer.calculate_statistics(**test_input)
    assert result.max == expected_result[0]
    assert result.min == expected_result[1]
    assert result.mean == expected_result[2]
    assert result.std == expected_result[3]
    assert result.quartile_upper == expected_result[4]
    assert result.quartile_lower == expected_result[5]
    assert result.sum == expected_result[6]
