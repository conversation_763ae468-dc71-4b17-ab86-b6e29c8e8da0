import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List
from uuid import UUID

from pandas import DataFrame

from services.base.application.boundaries.time_input import TimeInput
from services.base.application.database.aggregation_service import AggregationService
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.exceptions import IncorrectOperationException, NoContentException
from services.base.domain.constants.extension_labels.trend_insights_labels import TrendInsightsLabels
from services.base.domain.enums.event_v3_type import EventV3Type
from services.base.domain.schemas.events.event import EventFields
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.serverless.apps.trend_insights.app.analytic_mappings import (
    AnalyticTypeToAggregationUseCaseMapping,
    AnalyticTypeToSeriesMapping,
    AnalyticTypeToValueFieldMapping,
)
from services.serverless.apps.trend_insights.app.data_transformer import DataTransformer
from services.serverless.apps.trend_insights.app.models.analysis_models import (
    AnalysisTypeInputModel,
)
from services.serverless.apps.trend_insights.app.models.analytic_type import AnalyticType
from services.serverless.apps.trend_insights.data_access_use_cases.list_v3_data_use_case import ListV3DataUseCase


class AnalysisPreparer:
    @classmethod
    async def prepare_analysis_type_input(
        cls,
        analytic_type: AnalyticType,
        user_id: UUID,
        aggregation_service: AggregationService,
    ) -> List[AnalysisTypeInputModel]:
        logging.info(f"Preparing analysis type input for AnalyticType: {analytic_type}")
        analysis_time_input = await cls.prepare_analysis_time_input()
        analytic_input_list = []
        # Maps the time inputs to the short-medium-long term analytic inputs
        for analytic_time_input in analysis_time_input:
            logging.info(
                f"Preparing analysis time input for aggregation interval: {analytic_time_input[TrendInsightsLabels.AGGREGATION_INTERVAL]}"
            )
            viable_series = (
                AnalyticTypeToSeriesMapping[analytic_type]
                if analytic_type.value in AnalyticTypeToSeriesMapping.keys()
                else await AnalysisPreparer.identify_viable_series_from_v3_type(
                    current_uuid=user_id,
                    analytic_type=analytic_type,
                    aggregation_service=aggregation_service,
                    time_input=analytic_time_input[TrendInsightsLabels.TIME_INPUT],
                )
            )

            analytic_input_list.append(
                AnalysisTypeInputModel(
                    time_input=analytic_time_input[TrendInsightsLabels.TIME_INPUT],
                    aggregation_interval=analytic_time_input[TrendInsightsLabels.AGGREGATION_INTERVAL],
                    viable_series=viable_series,
                )
            )
        return analytic_input_list

    @classmethod
    async def prepare_analysis_time_input(cls) -> List[Dict]:
        """Generates different time series analytic inputs for given analytic type"""
        yesterday_midnight = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(
            days=1
        )

        time_frames = [("7d", 28), ("30d", 120), ("91d", 364)]

        result = []
        for aggregation_interval, days_back in time_frames:
            time_input = TimeInput(
                time_gte=yesterday_midnight - timedelta(days=days_back), time_lte=yesterday_midnight, interval="1d"
            )
            result.append(
                {
                    TrendInsightsLabels.AGGREGATION_INTERVAL: aggregation_interval,
                    TrendInsightsLabels.TIME_INPUT: time_input,
                }
            )

        return result

    @staticmethod
    async def get_analysis_input_dataframe(
        current_uuid: UUID,
        time_input: TimeInput,
        analytic_type: AnalyticType,
        search_service: DocumentSearchService,
        analytic_series: str,
    ) -> DataFrame | None:
        try:
            logging.info(f"Getting AnalysisInputDataFrame for {analytic_series}")
            if data_fetching_use_case := AnalyticTypeToAggregationUseCaseMapping.get(analytic_type):
                user_data = await data_fetching_use_case().execute_async(user_uuid=current_uuid, time_input=time_input)
                if user_data:
                    return DataTransformer.to_sorted_data_frame(
                        input_data=[m.model_dump() for m in user_data], analytic_series=analytic_series
                    )
            elif analytic_type.value in EventV3Type:
                user_data = await ListV3DataUseCase().execute_async(
                    user_uuid=current_uuid,
                    time_input=time_input,
                    search_service=search_service,
                    series=analytic_series,
                    analytic_type=analytic_type,
                )
                return DataTransformer.from_v3_documents_to_sorted_data_frame(
                    documents=user_data.documents,
                    series_value_field=AnalyticTypeToValueFieldMapping[analytic_type][0],
                    series=analytic_series,
                )
            else:
                raise IncorrectOperationException(f"no use case for given analytic type {analytic_type}")
        except NoContentException as error:
            logging.info(f"No content found: {error}")
            return None
        except IncorrectOperationException as error:
            logging.warning(f"Incorrect operation: {error}")
            return None
        except Exception as error:
            logging.error(f"Unexpected error in get_analysis_input_dataframe: {error}")
            return None

    @staticmethod
    async def identify_viable_series_from_v3_type(
        current_uuid: UUID,
        time_input: TimeInput,
        analytic_type: AnalyticType,
        aggregation_service: AggregationService,
        threshold: int = 0,
    ) -> list[str]:
        # TODO Figure out a reasonable threshold for V3 types
        logging.info(f"Identifying viable series for V3 AnalyticType: {analytic_type}")
        and_query = (
            BooleanQueryBuilder()
            .add_queries(
                [
                    CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte),
                    CommonLeafQueries.owner_id_value_query(user_uuid=current_uuid),
                ]
            )
            .build_and_query()
        )
        query = Query(type_queries=[TypeQuery(domain_types=[analytic_type.to_model()], query=and_query)])
        unique_track_names = await aggregation_service.frequency_distribution_by_query(
            query=query, field_name=f"{EventFields.NAME}.keyword", size=100_000
        )
        logging.info(f"Found unique track names: {[item.aggregation_key for item in unique_track_names]}")
        result_list = []
        logging.info(f"Filtering track names with occurrence over threshold: {threshold}")
        for item in unique_track_names:
            if item.document_count > threshold:
                result_list.append(item.aggregation_key)
        logging.info(f"Found {len(result_list)} viable track names: {result_list}")

        return result_list
