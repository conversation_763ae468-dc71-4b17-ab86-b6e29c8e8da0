from typing import Dict, List, Optional

from pydantic import Field, field_validator

from services.base.application.boundaries.time_input import TimeInput
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.constants.extension_labels.trend_insights_labels import TrendInsightsLabels
from services.base.domain.constants.value_limits import AnalyticModelsLimits
from services.base.domain.enums.analytics.trend_insights import TrendInsightsResultStatus
from services.base.domain.schemas.shared import BaseDataModel, TimeIntervalModel
from services.serverless.apps.trend_insights.app.models.analysis_models import (
    AnalysisOutputModel,
)
from services.serverless.apps.trend_insights.app.models.analytic_type import AnalyticType


class SeriesAnalysisEvaluationInputModel(BaseDataModel):
    aggregation_interval: str = Field(
        description="Interval used to aggregate values before they were compared.",
        alias=TrendInsightsLabels.AGGREGATION_INTERVAL,
    )
    time_input: TimeInput = Field(alias=TrendInsightsLabels.TIME_INPUT)


class SeriesAnalysisListValuesBucket(BaseDataModel):
    value_list: List[float] = Field(default_factory=list)
    time_range: TimeIntervalModel


class SeriesAnalysisAveragedOutputBucket(BaseDataModel):
    aggregated_value: Optional[float] = Field(
        ge=-AnalyticModelsLimits.BILLION, le=AnalyticModelsLimits.BILLION, default=None
    )
    time_range: TimeIntervalModel

    @field_validator("aggregated_value")
    @classmethod
    def round_float_values(cls, value):
        return round(value, 2) if value else value


class SeriesAnalysisOutputModel(BaseDataModel):
    # Stores the result for one series in one time range
    evaluation_input: SeriesAnalysisEvaluationInputModel
    evaluation_output: Optional[AnalysisOutputModel] = None
    result_status: TrendInsightsResultStatus
    message: NonEmptyStr
    averaged_buckets: Optional[List[SeriesAnalysisAveragedOutputBucket]] = Field(min_length=1, default=None)


class AnalysisInputDataFrameModel(BaseDataModel):
    aggregation_interval: str = Field(min_length=1, alias=TrendInsightsLabels.AGGREGATION_INTERVAL)
    input_data: Optional[List[Dict]] = None
    time_input: TimeInput = Field(alias=TrendInsightsLabels.TIME_INPUT)


class SeriesAnalysisInputModel(BaseDataModel):
    analytic_type: AnalyticType = Field(alias=TrendInsightsLabels.ANALYTIC_TYPE)
    analytic_series: NonEmptyStr = Field(alias=TrendInsightsLabels.ANALYTIC_SERIES)
    agg_function: NonEmptyStr
    analytic_time_input: TimeInput
    aggregation_interval: str = Field(alias=TrendInsightsLabels.AGGREGATION_INTERVAL)
