import logging
from typing import List
from uuid import UUID

import numpy
from pandas import <PERSON><PERSON>rame

from services.base.application.database.aggregation_service import AggregationService
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.utils.time import TimeUtils
from services.base.domain.enums.analytics.trend_insights import TrendDirection, TrendInsightsResultStatus
from services.serverless.apps.trend_insights.app.analysis_preparer import AnalysisPreparer
from services.serverless.apps.trend_insights.app.analytic_mappings import (
    AnalyticSeriesFieldToAggregationFunction,
)
from services.serverless.apps.trend_insights.app.constants.messages import SeriesAnalysisResultMessages
from services.serverless.apps.trend_insights.app.data_analyzer import DataAnalyzer
from services.serverless.apps.trend_insights.app.data_transformer import DataTransformer
from services.serverless.apps.trend_insights.app.models.analysis_models import (
    AnalysisOutputModel,
    AnalysisTypeInputModel,
)
from services.serverless.apps.trend_insights.app.models.analytic_series_models import (
    SeriesAnalysisEvaluationInputModel,
    SeriesAnalysisInputModel,
    SeriesAnalysisOutputModel,
)
from services.serverless.apps.trend_insights.app.models.analytic_type import AnalyticType
from services.serverless.apps.trend_insights.app.models.trend_insights_models import TrendInsightsSeriesOutput


class AnalysisRunner:
    @classmethod
    async def evaluate_analytic_type(
        cls,
        search_service: DocumentSearchService,
        analytic_type: AnalyticType,
        user_id: UUID,
        aggregation_service: AggregationService,
    ) -> list[TrendInsightsSeriesOutput]:
        """
        Evaluate and analyze data for a specific analytic type across multiple time ranges and series.

        This method performs the following steps:
        1. Prepares analysis inputs for different time ranges (short, medium, long).
        2. Iterates through each input and viable series.
        3. Analyzes each series for each time range.
        4. Consolidates results under each series.

        Note:
        Results are temporarily stored in a dictionary keyed by analytic series
        before being consolidated into the final output format.
        """
        analytics_inputs: List[AnalysisTypeInputModel] = await AnalysisPreparer.prepare_analysis_type_input(
            user_id=user_id,
            analytic_type=analytic_type,
            aggregation_service=aggregation_service,
        )
        temp_type_results = {
            analytic_series: []
            for analytic_input in analytics_inputs
            for analytic_series in analytic_input.viable_series
        }
        for analytic_input in analytics_inputs:
            for analytic_series in analytic_input.viable_series:
                logging.info(f"Processing AnalyticSeries: {analytic_series}")
                single_time_range_series_output = await cls.analyze_series(
                    search_service=search_service,
                    user_id=user_id,
                    series_analysis_input=SeriesAnalysisInputModel(
                        analytic_series=analytic_series,
                        analytic_type=analytic_type,
                        analytic_time_input=analytic_input.time_input,
                        agg_function=AnalyticSeriesFieldToAggregationFunction.get(analytic_series, "mean"),
                        aggregation_interval=analytic_input.aggregation_interval,
                    ),
                )
                temp_type_results[analytic_series].append(single_time_range_series_output)

        type_results = [
            TrendInsightsSeriesOutput(
                analytic_type=analytic_type, analytic_series=series, series_analysis_results=series_outputs
            )
            for series, series_outputs in temp_type_results.items()
        ]
        logging.info(f"Finished evaluating analytic type: {analytic_type}")
        logging.info(f"Returning {len(type_results)} series results")
        return type_results

    @classmethod
    async def analyze_series(
        cls,
        series_analysis_input: SeriesAnalysisInputModel,
        search_service: DocumentSearchService,
        user_id: UUID,
    ) -> SeriesAnalysisOutputModel:
        input_data_frame = await AnalysisPreparer.get_analysis_input_dataframe(
            search_service=search_service,
            current_uuid=user_id,
            analytic_type=series_analysis_input.analytic_type,
            analytic_series=series_analysis_input.analytic_series,
            time_input=series_analysis_input.analytic_time_input,
        )
        if input_data_frame is not None:
            return cls.perform_analysis(
                series_analysis_input=series_analysis_input,
                analysis_input_dataframe=input_data_frame,
            )
        else:
            return SeriesAnalysisOutputModel(
                evaluation_input=SeriesAnalysisEvaluationInputModel(
                    aggregation_interval=series_analysis_input.aggregation_interval,
                    time_input=series_analysis_input.analytic_time_input,
                ),
                evaluation_output=None,
                result_status=TrendInsightsResultStatus.FAILED,
                message=SeriesAnalysisResultMessages.NOT_ENOUGH_DATA_POINTS,
            )

    @classmethod
    def perform_analysis(
        cls,
        series_analysis_input: SeriesAnalysisInputModel,
        analysis_input_dataframe: DataFrame,
    ) -> SeriesAnalysisOutputModel:
        averaged_buckets = None
        try:
            # Check if at least one data point
            if (
                not len(
                    analysis_input_dataframe[
                        analysis_input_dataframe[series_analysis_input.analytic_series].replace(0, numpy.nan).notna()
                    ]
                )
                > 0
            ):
                return SeriesAnalysisOutputModel(
                    evaluation_input=SeriesAnalysisEvaluationInputModel(
                        aggregation_interval=series_analysis_input.aggregation_interval,
                        time_input=series_analysis_input.analytic_time_input,
                    ),
                    evaluation_output=None,
                    result_status=TrendInsightsResultStatus.SKIPPED,
                    message=SeriesAnalysisResultMessages.NOT_ENOUGH_DATA_POINTS,
                )
            # Calculate Descriptive Statistics from all data points
            logging.info(f"Calculating descriptive statistics for {series_analysis_input.analytic_series}")
            statistics = DataAnalyzer.calculate_statistics(
                series=analysis_input_dataframe[series_analysis_input.analytic_series]  # pyright: ignore
            )
            logging.info(f"Creating buckets for {series_analysis_input.analytic_series}")
            averaged_buckets = DataTransformer.create_averaged_buckets(
                analytic_series=series_analysis_input.analytic_series,
                bucket_range=TimeUtils.get_relativedelta_from_aggregation_interval(
                    aggregation_interval=series_analysis_input.aggregation_interval
                ),
                time_input=series_analysis_input.analytic_time_input,
                data_frame=analysis_input_dataframe,
                agg_function=series_analysis_input.agg_function,
            )
            trend = None
            result_status = TrendInsightsResultStatus.CALCULATED_STATISTICS
            message = f"{SeriesAnalysisResultMessages.STATISTICS_SUCCESSFULLY_EVALUATED}"
            if DataTransformer.has_enough_consecutive_buckets(averaged_buckets=averaged_buckets):
                logging.info(f"Calculating trend for {series_analysis_input.analytic_series}")
                trend = DataAnalyzer.calculate_trend(buckets=averaged_buckets)
                if trend:
                    result_status = TrendInsightsResultStatus.CALCULATED_STATISTICS_AND_TREND
                    if trend.consecutive_trend == TrendDirection.INCREASING:
                        message = f"{SeriesAnalysisResultMessages.EVALUATED_SUCCESSFULLY_TREND_INCREASING}"
                    elif trend.consecutive_trend == TrendDirection.DECREASING:
                        message = f"{SeriesAnalysisResultMessages.EVALUATED_SUCCESSFULLY_TREND_DECREASING}"
                    else:
                        message = f"{SeriesAnalysisResultMessages.EVALUATED_SUCCESSFULLY_NO_TREND}"
                else:
                    message = f"{SeriesAnalysisResultMessages.STATISTICS_SUCCESSFULLY_EVALUATED}"
                    result_status = TrendInsightsResultStatus.CALCULATED_STATISTICS
            evaluation_output = AnalysisOutputModel(trend=trend, statistics=statistics)

        except Exception as error:
            logging.exception(
                f"Error while evaluating analytic series {series_analysis_input.analytic_series} for type {series_analysis_input.analytic_type}: {error}"
            )
            message = SeriesAnalysisResultMessages.FAILED_FOR_UNKNOWN_REASON
            result_status = TrendInsightsResultStatus.FAILED
            evaluation_output = None

        return SeriesAnalysisOutputModel(
            evaluation_input=SeriesAnalysisEvaluationInputModel(
                aggregation_interval=series_analysis_input.aggregation_interval,
                time_input=series_analysis_input.analytic_time_input,
            ),
            evaluation_output=evaluation_output,
            result_status=result_status,
            message=message,
            averaged_buckets=averaged_buckets,
        )
