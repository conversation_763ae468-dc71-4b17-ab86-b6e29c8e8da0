import logging
from typing import List
from uuid import UUID

from services.base.application.boundaries.time_input import TimeInput
from services.base.application.exceptions import NoContentException
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import TimestampModel
from services.base.domain.schemas.sleep import Sleep, SleepFields, SleepSummaryFields
from services.base.infrastructure.database.opensearch.query_methods.async_fetchers import (
    get_time_aggregated_fields_async,
)
from services.base.infrastructure.database.opensearch.query_methods.results_filters import (
    filter_results_from_aggregation,
)
from services.base.infrastructure.database.opensearch.query_methods.utils import (
    are_results_empty,
    get_fields_and_aggregator_as_tuple,
)


class AggregateSleepUseCaseOutputItem(TimestampModel):
    asleep_seconds: float


field_summary = f"{SleepFields.SLEEP_EVENTS}.{SleepFields.SLEEP_SUMMARY}"
field_asleep_seconds = f"{field_summary}.{SleepSummaryFields.ASLEEP_SECONDS}"
field_asleep_seconds_single_provider_sum = f"single_provider_{SleepSummaryFields.ASLEEP_SECONDS}_sum"
field_asleep_seconds_per_provider = f"{SleepSummaryFields.ASLEEP_SECONDS}_per_{DocumentLabels.ORGANIZATION}"


class AggregateSleepUseCase:
    async def execute_async(self, user_uuid: UUID, time_input: TimeInput) -> List[AggregateSleepUseCaseOutputItem]:
        output_results = []

        # Get user logs from OpenSearch
        requested_fields = get_fields_and_aggregator_as_tuple(
            [
                field_asleep_seconds,
            ],
            "sum",
        )
        and_query = (
            BooleanQueryBuilder()
            .add_query(query=CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_uuid))
            .add_query(query=CommonLeafQueries.timestamp_range_query(lte=time_input.time_lte, gte=time_input.time_gte))
        ).build_and_query()

        query = Query(type_queries=[TypeQuery(domain_types=[Sleep], query=and_query)])

        """
        max_bucket aggregation: This is an Elasticsearch aggregation that is used to find the maximum value among the specified buckets. 
        buckets_path: This parameter in the max_bucket aggregation specifies the path to the values that should be considered for finding the maximum.
        The ">" symbol in the buckets_path indicates the path traversal from one aggregation to another.
        """
        sub_aggs = {
            field_asleep_seconds_per_provider: {
                "terms": {"field": f"{DocumentLabels.METADATA}.{DocumentLabels.ORGANIZATION}", "size": 10},
                "aggs": {field_asleep_seconds_single_provider_sum: {"sum": {"field": field_asleep_seconds}}},
            },
            f"{field_asleep_seconds}_sum": {
                "max_bucket": {
                    "buckets_path": f"{field_asleep_seconds_per_provider}>{field_asleep_seconds_single_provider_sum}"
                }
            },
        }

        results = await get_time_aggregated_fields_async(
            requested_fields_and_agg=requested_fields,
            time_gte=time_input.time_gte,
            time_lte=time_input.time_lte,
            interval=time_input.interval,
            query=query,
            time_field_name=DocumentLabels.END_TIME,
            custom_sub_aggs=sub_aggs,
        )

        if are_results_empty(results):
            raise NoContentException(message="No data available for the given time range.")

        # Define output models
        filtered_results = filter_results_from_aggregation(
            in_results=results, requested_fields_and_agg=requested_fields
        )
        for result in filtered_results:
            try:
                timestamp = result.get(DocumentLabels.TIMESTAMP)
                assert timestamp
                sleep_data = AggregateSleepUseCaseOutputItem(
                    timestamp=timestamp,
                    asleep_seconds=(result.get(field_asleep_seconds) or 0),
                )
                output_results.append(sleep_data)
            except (KeyError, ValueError):
                logging.exception(f"Missing data in detail: {result}, skipping.")
                continue

        return output_results
