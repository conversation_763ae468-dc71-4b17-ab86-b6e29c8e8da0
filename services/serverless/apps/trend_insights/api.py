import asyncio
import logging

from services.base.application.event_models.analytics_scheduled_event_model import AnalyticsScheduledEventModel
from services.base.telemetry.lambda_telemetry_instrumentor import LambdaTelemetryInstrumentor
from services.serverless.apps.trend_insights.app.boundaries.trend_insights_input_boundary import (
    TrendInsightsInputBoundary,
)
from services.serverless.apps.trend_insights.app.models.analytic_type import AnalyticType
from services.serverless.apps.trend_insights.app.models.trend_insights_models import TrendInsightsInput
from services.serverless.apps.trend_insights.app.trend_insights_use_case import (
    TrendInsightsUseCase,
)
from services.serverless.apps.trend_insights.dependency_bootstrapper import (
    DependencyBootstrapper,
)
from services.serverless.base.parsers.parse_lambda_triggers import parse_lambda_triggers
from settings.app_config import settings
from settings.app_secrets import secrets

LambdaTelemetryInstrumentor.initialize(service_name="trend_insights", settings=settings, secrets=secrets)


logging.info("starting trend insights")
bootstrapper = DependencyBootstrapper().build()


async def handle_async(models: list[AnalyticsScheduledEventModel]):
    for model in models:
        await bootstrapper.get(TrendInsightsUseCase).execute_async(
            input_boundary=TrendInsightsInputBoundary(
                extension_id=model.extension_id,
                provider_id=model.provider_id,
                user_uuid=model.user_uuid,
                extension_input=TrendInsightsInput(
                    analytic_types=[analytic_type for analytic_type in AnalyticType], should_notify=True
                ),
            )
        )


def handler(event, _):
    records = event.get("Records", [])
    logging.info(f"trend insights started with records: {records}")

    if not records:
        logging.error(f"Notify handler invoked without expected payload, invocation event: {event}")
        return
    payloads = parse_lambda_triggers(records=records)
    logging.info(f"Event models: {payloads}")

    loop = asyncio.get_event_loop()
    if loop.is_closed():
        loop = asyncio.new_event_loop()

    loop.run_until_complete(
        handle_async(models=[AnalyticsScheduledEventModel(**payload.payload) for payload in payloads])
    )
