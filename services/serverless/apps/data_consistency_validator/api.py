import asyncio
import logging

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.use_cases.cleanup_missing_references_use_case import CleanupMissingReferencesUseCase
from services.base.telemetry.lambda_telemetry_instrumentor import LambdaTelemetryInstrumentor
from services.serverless.apps.data_consistency_validator.application.data_consistency_usecase import (
    check_data_consistency,
)
from services.serverless.apps.data_consistency_validator.application.dependency_boostrapper import (
    get_cleanup_uc,
    get_search_service,
)
from settings.app_config import settings
from settings.app_secrets import secrets

LambdaTelemetryInstrumentor.initialize(service_name="data_consistency_validator", settings=settings, secrets=secrets)


logging.info("starting data consistency validator")


async def run_data_consistency_check(search_service: DocumentSearchService, uc: CleanupMissingReferencesUseCase):
    await check_data_consistency(search_service=search_service, uc=uc)


def handler(event, context):
    logging.info("Lambda handler analytics_scheduler started")
    loop = asyncio.get_event_loop()
    if loop.is_closed():
        loop = asyncio.new_event_loop()

    loop.run_until_complete(run_data_consistency_check(search_service=get_search_service(), uc=get_cleanup_uc()))
