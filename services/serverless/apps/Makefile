.DEFAULT_GOAL := help
.EXPORT_ALL_VARIABLES:
DOCKER_NETWORK := stack
COVERAGE := 80
DOCKER_BUILDKIT := 1
COMPOSE_DOCKER_CLI_BUILD := 1
RUN_ENV ?= local

help: ## Show this help (runs only in bash)
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

build: ## run lambda specific tests
	docker compose --profile $(RUN_ENV) build

test_serverless: ## run lambda specific tests
	docker run --network $(DOCKER_NETWORK) --rm test_handler bash -lce "pytest ./services/serverless/apps/alexa_voice_log/tests/ --verbose  \
        --cov=./services/serverless/apps/alexa_voice_log/ --cov=./services/serverless/apps/alexa_voice_log/ "

	docker run --network $(DOCKER_NETWORK) --rm test_handler bash -lce "pytest ./services/serverless/apps/analytics_scheduler/tests/ --verbose  \
        --cov=./services/serverless/apps/analytics_scheduler/ --cov=./services/serverless/apps/analytics_scheduler/ "

	docker run --network $(DOCKER_NETWORK) --rm test_handler bash -lce "pytest ./services/serverless/apps/data_consistency_validator/tests/ --verbose  \
		--cov=./services/serverless/apps/data_consistency_validator/ --cov=./services/serverless/apps/data_consistency_validator/ "

	docker run --network $(DOCKER_NETWORK) --rm test_handler bash -lce "pytest ./services/serverless/apps/notify_handler/tests/ --verbose  \
        --cov=./services/serverless/apps/notify_handler/ --cov=./services/serverless/apps/notify_handler/ "

	docker run --network $(DOCKER_NETWORK) --rm test_handler bash -lce "pytest ./services/serverless/apps/single_correlation_app/tests/ --verbose  \
		--cov=./services/serverless/apps/single_correlation_app/ --cov=./services/serverless/apps/single_correlation_app/ "

	docker run --network $(DOCKER_NETWORK) --rm test_handler bash -lce "pytest ./services/serverless/apps/trend_insights/tests/ --verbose  \
        --cov=./services/serverless/apps/trend_insights/ --cov=./services/serverless/apps/trend_insights/ "

	docker run --network $(DOCKER_NETWORK) --rm test_handler bash -lce "pytest ./services/serverless/apps/usage_statistics_generator/tests/ --verbose  \
    --cov=./services/serverless/apps/usage_statistics_generator/ --cov=./services/serverless/apps/usage_statistics_generator/ "

test_serverless_integration: ## run integration specific tests
	docker run --network $(DOCKER_NETWORK) --rm test_handler bash -lce "pytest ./services/serverless/apps/usage_statistics_generator/tests/ --verbose  \
    --cov=./services/serverless/apps/usage_statistics_generator/ --cov=./services/serverless/apps/usage_statistics_generator/ -m integration" \
