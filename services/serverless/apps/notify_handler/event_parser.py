import logging
from typing import Dict, List, Type

from aws_lambda_typing.events.sns import SNSMessageAttribute

from services.base.application.event_models.analytics_completed_event_model import AnalyticsCompletedEventModel
from services.base.application.event_models.member_user_account_linked_event_model import (
    MemberUserAccountLinkedEventModel,
)
from services.base.application.event_models.member_user_registered_event_model import MemberUserRegisteredMessageModel
from services.base.application.event_models.takeout_export_finished_event_model import TakeoutExportFinishedModel
from services.base.domain.constants.messaging import (
    ATT_NAME_ANALYTICS_EVENT,
    ATT_NAME_TAKEOUT_EXPORT_EVENT,
    ATT_NAME_USER_EVENT,
    MessageTopics,
)
from services.serverless.apps.notify_handler.application.event_input_models import EventInputModels
from services.serverless.base.parsers.parse_lambda_triggers import MessagePayload

_supported_events_mapping: Dict[
    MessageTopics,
    Type[EventInputModels],
] = {
    MessageTopics.TOPIC_ANALYTIC_FINISHED: AnalyticsCompletedEventModel,
    MessageTopics.TOPIC_MEMBER_USER_REGISTERED: MemberUserRegisteredMessageModel,
    MessageTopics.TOPIC_MEMBER_USER_ACCOUNT_LINKED: MemberUserAccountLinkedEventModel,
    MessageTopics.TOPIC_TAKEOUT_EXPORT_FINISHED: TakeoutExportFinishedModel,
}


def parse_event_input_to_models(payloads: List[MessagePayload]) -> List[EventInputModels]:
    """Turns the raw SNS message into our supported domain models"""
    models = []
    for payload in payloads:
        model_type = get_model_from_sns_attribute(attr=payload.message_attributes)
        models.append(model_type(**payload.payload))
    return models


def get_model_from_sns_attribute(attr: Dict[str, SNSMessageAttribute]) -> Type[EventInputModels]:
    """Processes the SNS message attributes to figure out which domain model type to return

    ValueError: It does not contain an attribute name of the event
    or the topic is not registered in the system

    KeyError: The SNS message attribute contains an attribute name which is valid, but not supported by the lambda
    """
    attr_name: str = ""

    if attr.get(ATT_NAME_USER_EVENT):
        attr_name = ATT_NAME_USER_EVENT
    elif attr.get(ATT_NAME_ANALYTICS_EVENT):
        attr_name = ATT_NAME_ANALYTICS_EVENT
    elif attr.get(ATT_NAME_TAKEOUT_EXPORT_EVENT):
        attr_name = ATT_NAME_TAKEOUT_EXPORT_EVENT

    if not attr_name:
        raise ValueError(f"unsupported message attribute provided: {attr}")

    topic = MessageTopics(attr[attr_name]["Value"])
    logging.info(f"found topic: {topic}")
    return _supported_events_mapping[topic]
