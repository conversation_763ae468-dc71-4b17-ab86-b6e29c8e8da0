from typing import Self, Type

from injector import Injector
from opensearchpy import As<PERSON><PERSON><PERSON><PERSON>earch
from sqlalchemy import URL
from sqlalchemy.ext.asyncio import <PERSON><PERSON><PERSON><PERSON><PERSON>, AsyncSession, async_sessionmaker, create_async_engine

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.notifications.push_notification_service import PushNotificationService
from services.base.domain.repository.extension_detail_repository import ExtensionDetailRepository
from services.base.domain.repository.extension_run_repository import ExtensionRunRepository
from services.base.domain.repository.inbox_message_repository import InboxMessageRepository
from services.base.domain.repository.member_user_device_repository import MemberUserDeviceRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.notification_inbox_repository import NotificationInboxRepository
from services.base.infrastructure.database.opensearch.opensearch_client import OpenSearchClient
from services.base.infrastructure.database.opensearch.os_depr_event_repository import OSDeprEventRepository
from services.base.infrastructure.database.opensearch.os_document_search_service import OSDocumentSearchService
from services.base.infrastructure.database.opensearch.repository.os_extension_run_repository import (
    OSExtensionRunRepository,
)
from services.base.infrastructure.database.opensearch.repository.os_inbox_message_repository import (
    OSInboxMessageRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_extension_detail_repository import (
    SqlAlchExtensionDetailRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_device_repository import (
    SqlAlchMemberUserDeviceRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_repository import (
    SqlAlchMemberUserRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_settings_repository import (
    SqlAlchMemberUserSettingsRepository,
)
from services.base.infrastructure.database.sql_alchemy.repository.sql_alchemy_notification_inbox_repository import (
    SqlAlchNotificationInboxRepository,
)
from services.base.infrastructure.notifications.firebase.firebase_push_notification_service import (
    FirebasePushNotificationService,
)
from services.serverless.apps.notify_handler.application.notify_users_use_case import NotifyUsersUseCase
from services.serverless.apps.notify_handler.application.store_messages_use_case import StoreMessagesUseCase
from settings.app_config import settings
from settings.app_secrets import secrets


class DependencyBootstrapper:
    def __init__(self):
        self._injector = Injector()

    @property
    def injector(self):
        return self._injector

    def get[T](self, interface: Type[T]) -> T:
        return self.injector.get(interface=interface)

    def build(self) -> Self:
        self._bind_infrastructure()
        self._bind_singleton(
            interface=DocumentSearchService, to=OSDocumentSearchService(client=self.get(interface=OpenSearchClient))
        )
        self._bind_repositories()
        self._bind_services()
        self._bind_use_cases()

        return self

    async def cleanup(self):
        await self.get(DeprEventRepository).close()

    def _bind_singleton[T](self, interface: Type[T], to: T):
        self.injector.binder.bind(interface=interface, to=to)

    def _bind_infrastructure(self):
        self._bind_singleton(
            interface=AsyncOpenSearch,
            to=AsyncOpenSearch(hosts=settings.OS_HOSTS, timeout=60, max_retries=5, retry_on_timeout=True),
        )
        self._bind_singleton(
            interface=OpenSearchClient, to=OpenSearchClient(client=self.get(interface=AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=AsyncEngine,
            to=create_async_engine(
                url=URL.create(
                    drivername="postgresql+asyncpg",
                    username=secrets.PG_USER,
                    password=secrets.PG_PASSWORD,
                    database=settings.PG_NAME,
                    host=settings.PG_HOST,
                    port=settings.PG_PORT,
                )
            ),
        )
        self._bind_singleton(
            interface=async_sessionmaker,
            to=async_sessionmaker(bind=self.get(interface=AsyncEngine), expire_on_commit=False, class_=AsyncSession),
        )

    def _bind_services(self):
        self._bind_singleton(
            interface=PushNotificationService, to=FirebasePushNotificationService(cred=secrets.FIREBASE_CREDENTIALS)
        )

    def _bind_repositories(self):
        self._bind_singleton(
            interface=DeprEventRepository, to=OSDeprEventRepository(client=self.get(interface=OpenSearchClient))
        )
        self._bind_singleton(
            interface=ExtensionRunRepository, to=OSExtensionRunRepository(client=self.get(AsyncOpenSearch))
        )
        self._bind_singleton(
            interface=ExtensionDetailRepository,
            to=SqlAlchExtensionDetailRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=MemberUserSettingsRepository,
            to=SqlAlchMemberUserSettingsRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=MemberUserDeviceRepository,
            to=SqlAlchMemberUserDeviceRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=NotificationInboxRepository,
            to=SqlAlchNotificationInboxRepository(session_maker=self.get(interface=async_sessionmaker)),
        )
        self._bind_singleton(
            interface=MemberUserRepository,
            to=SqlAlchMemberUserRepository(
                session_maker=self.get(interface=async_sessionmaker), engine=self.get(interface=AsyncEngine)
            ),
        )
        self._bind_singleton(
            interface=InboxMessageRepository,
            to=OSInboxMessageRepository(
                client=self.get(AsyncOpenSearch), search_service=self.get(interface=DocumentSearchService)
            ),
        )

    def _bind_use_cases(self):
        self._bind_singleton(
            interface=NotifyUsersUseCase,
            to=NotifyUsersUseCase(
                member_user_device_repository=self.get(MemberUserDeviceRepository),
                push_notification_service=self.get(PushNotificationService),
            ),
        )

        self._bind_singleton(
            interface=StoreMessagesUseCase,
            to=StoreMessagesUseCase(
                inbox_message_repository=self.get(InboxMessageRepository),
                notification_repository=self.get(NotificationInboxRepository),
                member_user_settings_repository=self.get(MemberUserSettingsRepository),
            ),
        )
