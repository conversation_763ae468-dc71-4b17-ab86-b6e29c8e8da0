from urllib.parse import unquote as url_decode_percentage

from pydantic import field_validator

from services.base.domain.enums.client_apps import DEFAULT_CLIENT_APP, ClientApps
from services.base.domain.schemas.shared import BaseDataModel


class AuthByCodeInput(BaseDataModel):
    client: ClientApps = DEFAULT_CLIENT_APP
    code: str
    redirect_uri: str
    state: str = ""

    # @TODO:
    # THIS IS A HOTFIX NOT A FINAL SOLUTION
    # URL DECODING SHOULD BE HANDLED ON THE GLOBAL API ENGINE LEVEL
    @field_validator("code")
    @classmethod
    def url_decode_percentage_code(cls, value):
        return url_decode_percentage(value)
