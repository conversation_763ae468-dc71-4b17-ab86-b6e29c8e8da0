from datetime import datetime
from typing import Sequence
from uuid import UUID
from zoneinfo import ZoneInfo

from services.base.application.database.models.sorts import Sort, SortOrder
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.schemas.plan.plan import Plan, PlanFields
from services.base.domain.schemas.query.boolean_query import AndQuery, NotQuery
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import ExistsQuery, RangeQuery, ValuesQuery
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.domain.schemas.shared import BaseDataModel


class PlanTimeOutputBoundary(BaseDataModel):
    upcoming: Sequence[Plan]
    late: Sequence[Plan]
    owner_id: UUID


class PlanLateAndUpcomingUseCase:
    def __init__(self, plan_repository: PlanRepository):
        self._plan_repo = plan_repository

    async def execute_async(self, owner_id: UUID, user_timezone: ZoneInfo, size: int) -> PlanTimeOutputBoundary:  # type: ignore
        now = datetime.now(tz=user_timezone)

        late_plans = await self._get_plans(
            owner_id=owner_id,
            range_query=RangeQuery(field_name=PlanFields.NEXT_SCHEDULED_AT, lte=now),
            sort=Sort(name=PlanFields.NEXT_SCHEDULED_AT, order=SortOrder.DESCENDING),
            size=size,
        )
        upcoming_plans = await self._get_plans(
            owner_id=owner_id,
            range_query=RangeQuery(field_name=PlanFields.NEXT_SCHEDULED_AT, gte=now),
            sort=Sort(name=PlanFields.NEXT_SCHEDULED_AT, order=SortOrder.ASCENDING),
            size=size,
        )

        # Filter upcoming to only those with same date as now
        upcoming_plans = [plan for plan in upcoming_plans if plan.next_scheduled_at.date() == now.date()]
        return PlanTimeOutputBoundary(
            owner_id=owner_id,
            late=late_plans,
            upcoming=upcoming_plans,
        )

    async def _get_plans(self, owner_id: UUID, range_query: RangeQuery, sort: Sort, size: int) -> Sequence[Plan]:
        # Build base queries
        confirmation_required_query = ValuesQuery(field_name=PlanFields.IS_CONFIRMATION_REQUIRED, values=["true"])
        not_archived_query = NotQuery(queries=[ExistsQuery(field_name=PlanFields.ARCHIVED_AT)])
        urgent_query = ValuesQuery(field_name=PlanFields.IS_URGENT, values=["true"])
        owner_query = CommonLeafQueries.owner_id_value_query(user_uuid=owner_id)

        single_query = SingleDocumentTypeQuery[Plan](
            domain_type=Plan,
            query=AndQuery(
                queries=[owner_query, confirmation_required_query, not_archived_query, urgent_query, range_query]
            ),
            sort_by=sort,
        )
        plans: SearchResults[Plan] = await self._plan_repo.search_by_query(query=single_query, size=size)
        return plans.documents
