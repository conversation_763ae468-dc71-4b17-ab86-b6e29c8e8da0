from typing import Optional

from services.base.domain.enums.client_apps import ClientApps
from services.base.domain.enums.google_access_type import GoogleAccessType
from services.base.domain.enums.provider import SupportedLoginProviders
from services.user_service.application.use_cases.auth_use_cases.sign_in_authorizer import (
    GetSignInParametersOutputBoundary,
    SignInAuthorizer,
)
from settings.app_config import settings


class AppleOAuth2Authorizer(SignInAuthorizer):
    @property
    def provider(self) -> SupportedLoginProviders:
        return SupportedLoginProviders.GOOGLE

    @property
    def auth_url(self) -> str:
        return "https://appleid.apple.com/auth/authorize"

    @property
    def token_url(self) -> str:
        return "https://appleid.apple.com/auth/token"

    def _get_oauth2_client_id(self, client: ClientApps) -> str:
        match client:
            case any((ClientApps.WEB, ClientApps.ANDROID)):
                return settings.APPLE_OAUTH2_WEB_CLIENT_ID
            case ClientApps.IOS:
                return settings.APPLE_OAUTH2_MOBILE_CLIENT_ID
            case _:
                raise ValueError(f"cannot process unsupported client type: {client}")

    def get_sign_in_parameters(
        self, client: ClientApps, state: str, redirect_uri: str, nonce: Optional[str] = None
    ) -> GetSignInParametersOutputBoundary:
        client_id = self._get_oauth2_client_id(client=client)

        return GetSignInParametersOutputBoundary(
            state=state,
            redirect_uri=redirect_uri,
            nonce=nonce,
            access_type=GoogleAccessType.ONLINE.value,
            client_id=client_id,
            response_type="code",
            scope="email profile",
        )
