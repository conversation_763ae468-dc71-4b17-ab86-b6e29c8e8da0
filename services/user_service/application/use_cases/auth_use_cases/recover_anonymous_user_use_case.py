import logging

from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.application.exceptions import BadRequestException
from services.base.application.recovery_encryption import decrypt
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.enums.member_user_role import MemberUserType
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.member_user.member_user_settings import MemberUserSettings
from services.base.domain.schemas.shared import BaseDataModel
from services.user_service.application.boundaries.anonymous_recovery_token import AnonymousRecoveryToken


class RecoverAnonymousUserUseCaseOutputBoundary(BaseDataModel):
    user: MemberUser
    settings: MemberUserSettings


class RecoverAnonymousUserUseCase(AsyncUseCaseBase):
    def __init__(
        self,
        member_user_repository: MemberUserRepository,
        member_user_settings_repository: MemberUserSettingsRepository,
    ):
        self._member_user_repository = member_user_repository
        self._member_user_settings_repository = member_user_settings_repository

    async def execute_async(self, recovery_token: NonEmptyStr) -> RecoverAnonymousUserUseCaseOutputBoundary:
        try:
            decrypted_token = decrypt(token=recovery_token)
            token_decoded = AnonymousRecoveryToken.model_validate_json(decrypted_token)
        except Exception as error:
            logging.warning(f"Unable to parse recovery token, token: {recovery_token}, err: {error}")
            raise BadRequestException(message="Unable to parse given recovery token.")
        user_uuid = token_decoded.user_id
        user = await self._member_user_repository.get_by_uuid(user_uuid=user_uuid)
        if not user:
            raise BadRequestException(message="User was not found.")
        if user.type != MemberUserType.ANONYMOUS:
            raise BadRequestException(message="The recover option can only be used by an anonymous user.")
        settings = await self._member_user_settings_repository.get_by_uuid(user_uuid=user_uuid)

        return RecoverAnonymousUserUseCaseOutputBoundary(user=user, settings=settings)
