import logging
from typing import List, Optional
from uuid import UUID

from pydantic import Field

from services.base.application.async_use_case_base import AsyncUseCaseBase
from services.base.application.exceptions import RetryLater
from services.base.application.mappings.oauth_authorizer import oauth_providers_mapping
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.provider import Provider, SupportedApiProviders
from services.base.domain.repository.member_user_oauth2_repository import MemberUserOAuth2Repository
from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.member_user.member_user_oauth2 import MemberUserOAuth2
from services.base.domain.schemas.shared import BaseDataModel
from services.user_service.application.use_cases.auth_use_cases.api_auth import ProviderOAuth2ApiAuthorizer


class ListLinkedAccounts(BaseDataModel):
    provider: Provider
    provider_user_id: str
    scope: Optional[str] = Field(default=None)
    access_token: Optional[str] = Field(default=None)


class ListLinkedAccountsOutputBoundary(BaseDataModel):
    providers: List[ListLinkedAccounts]


class ListLinkedAccountsUseCase(AsyncUseCaseBase):
    def __init__(self, member_oauth2_repository: MemberUserOAuth2Repository):
        self._member_oauth2_repository = member_oauth2_repository

    async def execute_async(self, user_uuid: UUID) -> ListLinkedAccountsOutputBoundary:
        wrapper = ReadFromDatabaseWrapper(search_keys={DocumentLabels.USER_UUID: user_uuid})

        provider_oauth_records: List[MemberUserOAuth2] = await self._member_oauth2_repository.get(wrapper=wrapper)

        providers_output_list: List[ListLinkedAccounts] = []
        for record in provider_oauth_records:
            try:
                provider_options: ProviderOAuth2ApiAuthorizer = oauth_providers_mapping[
                    SupportedApiProviders(record.provider)
                ]()
            except NotImplementedError:
                logging.warning(f"provider: {record.provider} does not have an implemented authorizer")
                continue
            except Exception as exc:
                logging.exception(f"Exception {exc} when fetching linked provider {record.provider} account.")

            access_token: str | None = None
            try:
                if record.provider == Provider.FITBIT.value:
                    access_token = await provider_options.get_access_token_if_not_expired(record=record)

                if not access_token:
                    # other than fitbit goes old way
                    access_token = await provider_options.renew_access_token(
                        refresh_token=record.refresh_token,
                        user_uuid=user_uuid,
                        oauth2_repo=self._member_oauth2_repository,
                    )
            except RetryLater:
                ...
            except Exception as error:
                logging.exception(f"Renewal failed for provider: {record.provider}, got error {error}")

            providers_output_list.append(
                ListLinkedAccounts(
                    provider=Provider(record.provider),
                    provider_user_id=record.provider_user_id,
                    scope=record.scope,
                    access_token=access_token,
                )
            )

        return ListLinkedAccountsOutputBoundary(providers=providers_output_list)
