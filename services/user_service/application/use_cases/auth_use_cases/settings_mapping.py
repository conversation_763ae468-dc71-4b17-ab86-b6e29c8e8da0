from typing import Dict

from services.base.domain.enums.provider import Provider
from services.user_service.application.use_cases.auth_use_cases.providers.amazon.settings import AmazonOAuth2Settings
from services.user_service.application.use_cases.auth_use_cases.providers.apple.settings import AppleOAuth2Settings
from services.user_service.application.use_cases.auth_use_cases.providers.fitbit.settings import FitbitOAuth2Settings
from services.user_service.application.use_cases.auth_use_cases.providers.google.settings import GoogleOAuth2Settings
from services.user_service.domain.settings import OAuth2ProviderSettingsWrapper

provider_settings_auth_options: Dict[Provider, OAuth2ProviderSettingsWrapper] = {
    Provider.GOOGLE: GoogleOAuth2Settings(),
    Provider.APPLE: AppleOAuth2Settings(),
    Provider.AMAZON: AmazonOAuth2Settings(),
    Provider.FITBIT: FitbitOAuth2Settings(),
}
