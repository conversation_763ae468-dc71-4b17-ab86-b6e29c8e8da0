import json
from typing import Callable, <PERSON>, <PERSON>ple
from uuid import uuid4

import pytest
from starlette import status

from services.base.api.authentication.token_handling import generate_access_token
from services.base.api.query.boolean_query_api import BooleanQueryType, CompoundBooleanQueryAPI
from services.base.api.responses.common_document_responses import CommonDocumentsIdsResponse, CommonDocumentsResponse
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.messages import InboxMessageStatus
from services.base.domain.repository.inbox_message_repository import InboxMessageRepository
from services.base.domain.schemas.identity import Identity, IdentityType
from services.base.domain.schemas.inbox.inbox_message import InboxMessage, InboxMessageFields
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.api.query.leaf_query_api_helper import LeafQueryAPIHelper
from services.base.tests.domain.builders.inbox_message_builder import InboxMessageBuilder
from services.user_service.api.output_models.inbox_message_api_output import InboxMessageAPIOutput
from services.user_service.api.urls import InboxMessageEndpointUrls
from services.user_service.tests.api.builders.create_inbox_messages_request_builder import (
    CreateInboxMessagesRequestInputBuilder,
    CreateInboxMessagesRequestInputItemBuilder,
)
from services.user_service.tests.api.builders.get_inbox_messages_request_input_builder import (
    ListInboxMessageRequestInputBuilder,
)
from services.user_service.tests.api.builders.update_inbox_message_request_input_builder import (
    UpdateInboxMessageRequestInputBuilder,
)
from services.user_service.tests.api.builders.update_inbox_messages_request_builder import (
    UpdateInboxMessagesRequestBuilder,
)
from services.user_service.tests.common_rest_calls import (
    call_delete_endpoint,
    call_patch_endpoint,
    call_post_endpoint,
)


class TestCRUDInboxMessages:
    @pytest.fixture(scope="function")
    async def messages_and_auth_headers(
        self,
        inbox_message_repository: InboxMessageRepository,
        user_factory: Callable[[], MemberUser],
    ) -> Tuple[List[InboxMessage], dict]:
        # Setup
        owner = await user_factory()
        headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=owner.user_uuid)}"}
        dest_identity = Identity(id=owner.user_uuid, type=IdentityType.USER)
        messages: List[InboxMessage] = InboxMessageBuilder().with_destination(dest_identity).build_n()
        inserted_messages = await inbox_message_repository.insert(messages=messages, force_strong_consistency=True)

        yield inserted_messages, headers

        # Teardown
        ids = [msg.id for msg in inserted_messages]
        await inbox_message_repository.delete_by_id(ids=ids)

    class TestListInboxMessages:

        def assert_inbox_message(self, expected_message: InboxMessage, response_message: InboxMessageAPIOutput):
            assert response_message.title == expected_message.title
            assert response_message.message == expected_message.message
            assert response_message.status == expected_message.status
            assert response_message.system_properties == expected_message.system_properties
            assert response_message.timestamp == expected_message.timestamp
            assert response_message.is_urgent == expected_message.is_urgent

        async def test_list_inbox_message_should_return_expected_results(
            self,
            messages_and_auth_headers: Tuple[List[InboxMessage], dict],
        ):
            # Arrange
            messages, headers = messages_and_auth_headers
            request_builder = ListInboxMessageRequestInputBuilder().with_limit(len(messages))

            # Act
            response = await call_post_endpoint(
                request_url=InboxMessageEndpointUrls.SEARCH,
                body_json=request_builder.build_body_as_dict(),
                params=request_builder.build_params_as_dict(),
                headers=headers,
            )

            # Assert
            assert response.status_code == status.HTTP_200_OK
            response_model = CommonDocumentsResponse[InboxMessageAPIOutput](**response.json())
            assert len(response_model.documents) == len(messages)

            sorted_messages = sorted(messages, key=lambda msg: msg.timestamp, reverse=True)

            for expected_message, output_message in zip(sorted_messages, response_model.documents):
                self.assert_inbox_message(expected_message, output_message)

        async def test_list_inbox_message_with_status_filter_should_return_expected_results(
            self,
            messages_and_auth_headers: Tuple[List[InboxMessage], dict],
        ):
            # Arrange
            messages, headers = messages_and_auth_headers
            status_query = LeafQueryAPIHelper.create_values_query(
                field_name=InboxMessageFields.STATUS, values=[InboxMessageStatus.READ.value]
            )
            request_builder = (
                ListInboxMessageRequestInputBuilder().with_limit(len(messages)).with_query(query=status_query)
            )

            # Act
            response = await call_post_endpoint(
                request_url=InboxMessageEndpointUrls.SEARCH,
                body_json=request_builder.build_body_as_dict(),
                params=request_builder.build_params_as_dict(),
                headers=headers,
            )

            # Assert
            # No content doesn't have body, therefore nothing to serialize
            if response.status_code == status.HTTP_204_NO_CONTENT:
                return

            # We have body, therefore assert values
            assert response.status_code == status.HTTP_200_OK
            response_model = CommonDocumentsResponse[InboxMessageAPIOutput](**response.json())
            msg_with_read_status = [msg for msg in messages if msg.status == InboxMessageStatus.READ]
            sorted_messages = sorted(msg_with_read_status, key=lambda msg: msg.timestamp, reverse=True)
            assert len(response_model.documents) == len(sorted_messages)

            for expected_message, output_message in zip(sorted_messages, response_model.documents):
                self.assert_inbox_message(expected_message, output_message)

        async def test_list_inbox_message_with_sender_filter_should_return_expected_results(
            self, messages_and_auth_headers: Tuple[List[InboxMessage], dict]
        ):
            # Arrange
            messages, headers = messages_and_auth_headers
            number_of_messages = len(messages)
            sender_query = LeafQueryAPIHelper.create_values_query(
                field_name=f"{InboxMessageFields.SENDER}.{DocumentLabels.ID}", values=[str(messages[0].sender.id)]
            )
            request_builder = (
                ListInboxMessageRequestInputBuilder().with_limit(number_of_messages).with_query(query=sender_query)
            )
            # Act
            response = await call_post_endpoint(
                request_url=InboxMessageEndpointUrls.SEARCH,
                body_json=request_builder.build_body_as_dict(),
                params=request_builder.build_params_as_dict(),
                headers=headers,
            )

            # Assert
            assert response.status_code == status.HTTP_200_OK
            response_model = CommonDocumentsResponse[InboxMessageAPIOutput](**response.json())

            assert len(response_model.documents) == 1

            expected_message = messages[0]
            output_message = response_model.documents[0]
            self.assert_inbox_message(expected_message, output_message)

        async def test_list_inbox_message_with_multiple_filters_should_return_expected_results(
            self, messages_and_auth_headers: Tuple[List[InboxMessage], dict]
        ):
            # Arrange
            messages, headers = messages_and_auth_headers
            is_urgent_query = LeafQueryAPIHelper.create_values_query(
                field_name=f"{InboxMessageFields.IS_URGENT}", values=["true"]
            )
            status_query = LeafQueryAPIHelper.create_values_query(
                field_name=InboxMessageFields.STATUS, values=[InboxMessageStatus.READ.value]
            )
            and_query = CompoundBooleanQueryAPI(type=BooleanQueryType.AND, queries=[is_urgent_query, status_query])

            request_builder = ListInboxMessageRequestInputBuilder().with_query(query=and_query)

            # Act
            response = await call_post_endpoint(
                request_url=InboxMessageEndpointUrls.SEARCH,
                body_json=request_builder.build_body_as_dict(),
                params=request_builder.build_params_as_dict(),
                headers=headers,
            )

            # Assert
            # This is valid since the generated messages are random, so it can happen that there are no messages
            if response.status_code == status.HTTP_204_NO_CONTENT:
                return

            assert response.status_code == status.HTTP_200_OK
            filtered_msgs = [msg for msg in messages if msg.status == InboxMessageStatus.READ and msg.is_urgent]
            expected_messages = sorted(filtered_msgs, key=lambda msg: msg.timestamp, reverse=True)
            response_model = CommonDocumentsResponse[InboxMessageAPIOutput](**response.json())
            for expected_message, output_message in zip(expected_messages, response_model.documents):
                self.assert_inbox_message(expected_message, output_message)

        async def test_list_inbox_message_with_or_query_should_return_expected_results(
            self, messages_and_auth_headers: Tuple[List[InboxMessage], dict]
        ):
            # Arrange
            messages, headers = messages_and_auth_headers
            is_urgent_query = LeafQueryAPIHelper.create_values_query(
                field_name=f"{InboxMessageFields.IS_URGENT}", values=["true"]
            )
            filter_title_value = messages[0].title
            title_query = LeafQueryAPIHelper.create_values_query(InboxMessageFields.TITLE, values=[filter_title_value])
            status_query = LeafQueryAPIHelper.create_values_query(
                field_name=InboxMessageFields.STATUS, values=[InboxMessageStatus.UNREAD.value]
            )
            or_query = CompoundBooleanQueryAPI(type=BooleanQueryType.OR, queries=[is_urgent_query, title_query])
            and_query = CompoundBooleanQueryAPI(type=BooleanQueryType.AND, queries=[status_query, or_query])

            request_builder = ListInboxMessageRequestInputBuilder().with_query(query=and_query)

            # Act
            response = await call_post_endpoint(
                request_url=InboxMessageEndpointUrls.SEARCH,
                body_json=request_builder.build_body_as_dict(),
                params=request_builder.build_params_as_dict(),
                headers=headers,
            )

            # Assert
            # This is valid since the generated messages are random, so it can happen that there are no messages
            if response.status_code == status.HTTP_204_NO_CONTENT:
                return

            assert response.status_code == status.HTTP_200_OK
            filtered_msgs = [
                msg
                for msg in messages
                if msg.status == InboxMessageStatus.UNREAD and (msg.is_urgent or msg.title == filter_title_value)
            ]
            expected_messages = sorted(filtered_msgs, key=lambda msg: msg.timestamp, reverse=True)
            response_model = CommonDocumentsResponse[InboxMessageAPIOutput](**response.json())
            for expected_message, output_message in zip(expected_messages, response_model.documents):
                self.assert_inbox_message(expected_message, output_message)

        async def test_list_inbox_message_with_blacklisted_filter_name_should_return_403_code(
            self, messages_and_auth_headers: Tuple[List[InboxMessage], dict]
        ):
            _, headers = messages_and_auth_headers
            # Query object cannot be created since it would trigger the validation
            query = {
                "query": {
                    "type": "values",
                    "field_name": f"{InboxMessageFields.DESTINATION}.{DocumentLabels.ID}",
                    "values": [str(uuid4())],
                }
            }

            response = await call_post_endpoint(
                request_url=InboxMessageEndpointUrls.SEARCH,
                body_json=query,
                headers=headers,
            )

            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
            assert "Invalid query" in response.json()["message"]

    class TestUpdateInboxMessage:
        async def test_update_inbox_messages_should_pass(
            self, messages_and_auth_headers: Tuple[List[InboxMessage], dict]
        ):
            # Arrange
            messages, headers = messages_and_auth_headers
            request = (
                UpdateInboxMessagesRequestBuilder()
                .with_messages(
                    messages=[UpdateInboxMessageRequestInputBuilder().with_id(msg.id).build() for msg in messages]
                )
                .build()
            )
            request_url = InboxMessageEndpointUrls.INBOX
            body_json = json.loads(request.model_dump_json(by_alias=True))

            # Act
            response = await call_patch_endpoint(request_url=request_url, body_json=body_json, headers=headers)

            assert response.status_code == status.HTTP_200_OK
            response_model = CommonDocumentsResponse[InboxMessageAPIOutput](**response.json())
            assert len(response_model.documents) == len(messages)
            for response_msg, request_msg in zip(response_model.documents, request.messages_to_update):
                assert response_msg.status == request_msg.status
                assert response_msg.is_urgent == request_msg.is_urgent

        async def test_update_message_should_return_403_when_messages_do_not_belong_to_user(
            self, messages_and_auth_headers: Tuple[List[InboxMessage], dict], user_factory: Callable[[], MemberUser]
        ):
            # Arrange
            messages = messages_and_auth_headers[0]
            random_user = await user_factory()
            headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=random_user.user_uuid)}"}
            request = (
                UpdateInboxMessagesRequestBuilder()
                .with_messages(
                    messages=[UpdateInboxMessageRequestInputBuilder().with_id(msg.id).build() for msg in messages]
                )
                .build()
            )

            request_url = InboxMessageEndpointUrls.INBOX
            body_json = json.loads(request.model_dump_json(by_alias=True))

            # Act
            response = await call_patch_endpoint(request_url=request_url, body_json=body_json, headers=headers)

            # Assert
            assert response.status_code == status.HTTP_403_FORBIDDEN
            assert response.json()["message"] == "You need to be owner of all those messages"

    class TestCreateInboxMessages:
        async def test_create_message_endpoint_should_pass(
            self,
            inbox_message_repository: InboxMessageRepository,
            user_factory: Callable[[], MemberUser],
        ):
            # Arrange
            sender = await user_factory()
            input_item = CreateInboxMessagesRequestInputItemBuilder().build()
            body_json = CreateInboxMessagesRequestInputBuilder().with_values([input_item]).build()
            headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=sender.user_uuid)}"}

            # Act
            request_url = InboxMessageEndpointUrls.INBOX
            response = await call_post_endpoint(request_url=request_url, body_json=body_json, headers=headers)

            # Assert
            assert response.status_code == status.HTTP_200_OK
            response_model = CommonDocumentsResponse[InboxMessageAPIOutput](**response.json())
            assert len(response_model.documents) == 1
            inbox_message_output = response_model.documents[0]
            assert inbox_message_output.title == input_item[InboxMessageFields.TITLE]
            assert inbox_message_output.message == input_item[InboxMessageFields.MESSAGE]
            assert inbox_message_output.status == InboxMessageStatus.UNREAD
            assert inbox_message_output.is_urgent == input_item[InboxMessageFields.IS_URGENT]

            # Teardown
            await inbox_message_repository.delete_by_id([response_model.documents[0].id])

        async def test_create_message_with_invalid_html_should_return_422(
            self,
            inbox_message_repository: InboxMessageRepository,
            user_factory: Callable[[], MemberUser],
        ):
            # Arrange
            sender = await user_factory()
            input_item = CreateInboxMessagesRequestInputItemBuilder().with_message("<stron> test").build()
            body_json = CreateInboxMessagesRequestInputBuilder().with_values([input_item]).build()
            headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=sender.user_uuid)}"}
            # Act
            response = await call_post_endpoint(
                request_url=InboxMessageEndpointUrls.INBOX, body_json=body_json, headers=headers
            )
            # Assert
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
            assert (
                response.json()["detail"][0]["msg"] == "Value error, HTML is not valid. Remove following problems:\n"
                " line 1 column 1 - Error: <stron> is not recognized!\n"
            )

        async def test_create_message_with_invalid_html_will_be_auto_fixed(
            self,
            inbox_message_repository: InboxMessageRepository,
            user_factory: Callable[[], MemberUser],
        ):
            # Arrange
            sender = await user_factory()
            input_item = CreateInboxMessagesRequestInputItemBuilder().with_message("<p> test").build()
            body_json = CreateInboxMessagesRequestInputBuilder().with_values([input_item]).build()
            headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=sender.user_uuid)}"}
            # Act
            request_url = InboxMessageEndpointUrls.INBOX
            response = await call_post_endpoint(request_url=request_url, body_json=body_json, headers=headers)
            # Assert
            assert response.status_code == status.HTTP_200_OK
            response_model = CommonDocumentsResponse[InboxMessageAPIOutput](**response.json())
            assert len(response_model.documents) == 1
            inbox_message_output = response_model.documents[0]
            assert inbox_message_output.title == input_item[InboxMessageFields.TITLE]
            assert inbox_message_output.message == "<p>test</p>"
            assert inbox_message_output.status == InboxMessageStatus.UNREAD
            assert inbox_message_output.is_urgent == input_item[InboxMessageFields.IS_URGENT]

    class TestDeleteInboxMessages:
        async def test_delete_inbox_message_should_pass(
            self, messages_and_auth_headers: Tuple[List[InboxMessage], dict]
        ):
            # Arrange
            messages, headers = messages_and_auth_headers
            msg_ids = [msg.id for msg in messages]
            query_params = {"message_ids": msg_ids}
            request_url = InboxMessageEndpointUrls.INBOX

            # Act
            response = await call_delete_endpoint(request_url=request_url, params=query_params, headers=headers)

            assert response.status_code == status.HTTP_200_OK
            response_model = CommonDocumentsIdsResponse(**response.json())
            assert len(response_model.document_ids) == len(messages)
            for msg_id in response_model.document_ids:
                assert msg_id in msg_ids

        async def test_delete_inbox_message_should_return_403_when_messages_do_not_belong_to_user(
            self, messages_and_auth_headers: Tuple[List[InboxMessage], dict], user_factory: Callable[[], MemberUser]
        ):
            # Arrange
            messages = messages_and_auth_headers[0]
            random_user = await user_factory()
            query_params = {"message_ids": [msg.id for msg in messages]}
            headers = {"Authorization": f"Bearer {generate_access_token(user_uuid=random_user.user_uuid)}"}
            request_url = InboxMessageEndpointUrls.INBOX

            # Act
            response = await call_delete_endpoint(request_url=request_url, params=query_params, headers=headers)

            # Assert
            assert response.status_code == status.HTTP_403_FORBIDDEN
            assert response.json()["message"] == "You need to be owner of all provided messages"
