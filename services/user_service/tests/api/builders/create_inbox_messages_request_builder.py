from __future__ import annotations

import json
from datetime import datetime, timezone
from typing import List

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.inbox.context_models import GenericContext
from services.base.domain.schemas.inbox.inbox_message import InboxMessageFields


class CreateInboxMessagesRequestInputBuilder:
    def __init__(self):
        self.values: List[dict] | None = None

    def build(self) -> dict:
        as_dict = {"values": self.values or CreateInboxMessagesRequestInputItemBuilder().build_n()}

        return json.loads(json.dumps(as_dict))

    def with_values(self, values: List[dict]) -> CreateInboxMessagesRequestInputBuilder:
        self.values = values
        return self


class CreateInboxMessagesRequestInputItemBuilder:
    def __init__(self):
        self.title: str | None = None
        self.message: str | None = None

    def build(self) -> dict:
        return {
            InboxMessageFields.TITLE: self.title or PrimitiveTypesGenerator.generate_random_string(),
            InboxMessageFields.MESSAGE: self.message or PrimitiveTypesGenerator.generate_random_string(),
            InboxMessageFields.IS_URGENT: PrimitiveTypesGenerator.generate_random_bool(),
            InboxMessageFields.CONTEXT: GenericContext(
                type=PrimitiveTypesGenerator.generate_random_string()
            ).model_dump(by_alias=True),
            DocumentLabels.TIMESTAMP: str(datetime.now(timezone.utc)),
        }

    def build_n(self, n: int | None = None) -> List[dict]:
        return [
            self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=10))
        ]

    def with_title(self, title: str) -> CreateInboxMessagesRequestInputItemBuilder:
        self.title = title
        return self

    def with_message(self, message: str) -> CreateInboxMessagesRequestInputItemBuilder:
        self.message = message
        return self
