from datetime import datetime
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Body, Depends, Query
from fastapi_injector import Injected

from services.base.api.authentication.auth_guard import get_current_uuid
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.notification import NotificationStatus
from services.user_service.api.boundaries.member_user_notification_inbox import (
    NotificatonInboxInputUpdateStatus,
)
from services.user_service.api.boundaries.notification_output_boundary import (
    MemberUserNotificationInboxOutputBoundary,
)
from services.user_service.api.constants import NotificationInboxEndpointRoutes
from services.user_service.api.output_models.notification_inbox_api_output import NotificationInboxApiOutput
from services.user_service.api.request_models.create_notification_request_input import CreateNotificationRequestInput
from services.user_service.api.response_models.create_notification_inbox_response import CreateNotificationInboxResponse
from services.user_service.application.use_cases.notification_inbox.create_notification_use_case import (
    CreateNotificationUseCase,
)
from services.user_service.application.use_cases.notification_inbox.input_boundaries.create_notification_inbox_input_boundary import (
    CreateNotificationInboxBoundary,
)
from services.user_service.application.use_cases.notification_inbox.notification_inbox_get_all_use_case import (
    NotificationInboxGetNotificationsUseCase,
)
from services.user_service.application.use_cases.notification_inbox.notification_inbox_status_use_case import (
    NotificationInboxSetStatusUseCase,
)

notification_router = APIRouter(
    prefix="/api/v0.2/notifications",
    tags=["notifications"],
    responses={
        404: {"description": "Not found"},
        204: {"description": "No valid notifications found."},
    },
)


@notification_router.get(
    NotificationInboxEndpointRoutes.INBOX,
    response_model=List[MemberUserNotificationInboxOutputBoundary],
    response_model_exclude={DocumentLabels.USER_UUID},
)
async def notification_inbox_endpoint(
    notification_status: Optional[NotificationStatus] = None,
    offset_start: int = Query(default=0, ge=0),
    limit: int = Query(default=25, gt=0, le=25),
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    user_uuid: UUID = Depends(get_current_uuid),
    get_notifications_by_date: NotificationInboxGetNotificationsUseCase = Injected(
        NotificationInboxGetNotificationsUseCase
    ),
):
    return await get_notifications_by_date.execute(
        start_date=start_date,
        end_date=end_date,
        offset_start=offset_start,
        limit=limit,
        notification_status=notification_status,
        user_uuid=user_uuid,
    )


@notification_router.patch(
    NotificationInboxEndpointRoutes.SET_STATUS, response_model=List[MemberUserNotificationInboxOutputBoundary]
)
async def set_notification_status_endpoint(
    notification: NotificatonInboxInputUpdateStatus,
    user_uuid: UUID = Depends(get_current_uuid),
    set_status_use_case: NotificationInboxSetStatusUseCase = Injected(NotificationInboxSetStatusUseCase),
):
    return await set_status_use_case.execute(
        notification_uuids=notification.notification_uuid, status=notification.notification_status, user_uuid=user_uuid
    )


@notification_router.post(NotificationInboxEndpointRoutes.INBOX)
async def create_notification_inbox_messages_endpoint(
    user_uuid: UUID = Depends(get_current_uuid),
    create_notification_use_case: CreateNotificationUseCase = Injected(CreateNotificationUseCase),
    request_input: CreateNotificationRequestInput = Body(...),
) -> CreateNotificationInboxResponse:
    input_boundary = CreateNotificationInboxBoundary.map(model=request_input)
    result = await create_notification_use_case.execute_async(user_uuid=user_uuid, input_boundary=input_boundary)

    return CreateNotificationInboxResponse(
        values=[
            NotificationInboxApiOutput(
                id=notification.notification_uuid,
                title=notification.title,
                message=notification.message,
                timestamp=notification.timestamp,
                type=notification.type,
                status=notification.status,
            )
            for notification in result
        ]
    )
