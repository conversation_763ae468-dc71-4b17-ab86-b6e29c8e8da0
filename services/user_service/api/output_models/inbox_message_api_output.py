from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.messages import InboxMessageStatus
from services.base.domain.schemas.events.document_base import Document, SystemPropertiesDocument
from services.base.domain.schemas.inbox.context_models import MessageContext
from services.base.domain.schemas.inbox.inbox_message import InboxMessageFields


class InboxMessageAPIOutput(Document, SystemPropertiesDocument):
    status: InboxMessageStatus = Field(alias=InboxMessageFields.STATUS, default=InboxMessageStatus.UNREAD)
    title: NonEmptyStr = Field(alias=InboxMessageFields.TITLE)
    message: NonEmptyStr = Field(alias=InboxMessageFields.MESSAGE)
    timestamp: SerializableAwareDatetime = Field(alias=DocumentLabels.TIMESTAMP)
    is_urgent: bool = Field(alias=InboxMessageFields.IS_URGENT)
    context: MessageContext = Field(alias=InboxMessageFields.CONTEXT)

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.InboxMessage
