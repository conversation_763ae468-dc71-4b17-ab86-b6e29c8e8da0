from datetime import timedelta

from services.base.api.authentication.token_handling import generate_access_token
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.shared import BaseDataModel
from services.user_service.api.output_models.member_user_api_output import MemberUserOutput
from services.user_service.application.use_cases.auth_use_cases.enums.sign_in_action_type import SignInActionType
from settings.app_config import settings


class MemberUserSignInResponse(BaseDataModel):
    user: MemberUserOutput
    api_access_token: str
    action_type: SignInActionType
    recovery_token: NonEmptyStr | None = None


async def generate_member_user_sign_in_response(
    user: MemberUser, action_type: SignInActionType, settings_repo: MemberUserSettingsRepository
) -> MemberUserSignInResponse:
    # Create response payload with generated api tokens
    login_response = MemberUserSignInResponse(
        api_access_token=generate_access_token(
            user_uuid=user.user_uuid, time_delta=timedelta(minutes=settings.API_ACCESS_TOKEN_EXPIRATION_TIME_MINUTES)
        ),
        user=MemberUserOutput(**user.model_dump(), settings=await settings_repo.get_by_uuid(user_uuid=user.user_uuid)),
        action_type=action_type,
    )
    return login_response
