from enum import StrEnum
from typing import List

from services.base.domain.schemas.shared import BaseDataModel


class GoogleScopes(BaseDataModel):
    fitness_scopes: List[str] = [
        "https://www.googleapis.com/auth/fitness.sleep.read",
        "https://www.googleapis.com/auth/fitness.heart_rate.read",
        "https://www.googleapis.com/auth/fitness.reproductive_health.read",
        "https://www.googleapis.com/auth/fitness.body_temperature.read",
        "https://www.googleapis.com/auth/fitness.oxygen_saturation.read",
        "https://www.googleapis.com/auth/fitness.blood_glucose.read",
        "https://www.googleapis.com/auth/fitness.blood_pressure.read",
        "https://www.googleapis.com/auth/fitness.nutrition.read",
        "https://www.googleapis.com/auth/fitness.body.read",
        "https://www.googleapis.com/auth/fitness.location.read",
        "https://www.googleapis.com/auth/fitness.activity.read",
    ]
    drive_scopes: List[str] = [
        "https://www.googleapis.com/auth/drive",
        "https://www.googleapis.com/auth/drive.activity",
    ]
    game_scopes: List[str] = ["https://www.googleapis.com/auth/games"]
    people_scopes: List[str] = ["https://www.googleapis.com/auth/contacts"]
    shopping_scopes: List[str] = ["https://www.googleapis.com/auth/content"]
    youtube_scopes: List[str] = ["https://www.googleapis.com/auth/youtube"]


class GoogleIdTokenKeys(StrEnum):
    """Keys for Googles id_token (decoded from JWT token)"""

    AT_HASH = "at_hash"
    AUD = "aud"
    AZP = "azp"
    EMAIL = "email"
    EMAIL_VERIFIED = "email_verified"
    EXP = "exp"
    FAMILY_NAME = "family_name"
    GIVEN_NAME = "given_name"
    HD = "hd"
    IAT = "iat"
    ISS = "iss"
    LOCALE = "locale"
    NAME = "name"
    PICTURE = "picture"
    SUB = "sub"


class GoogleOAuthKeys(StrEnum):
    ACCESS_TYPE = "access_type"
    PROMPT = "prompt"
    INCLUDE_GRANTED_SCOPES = "include_granted_scopes"
