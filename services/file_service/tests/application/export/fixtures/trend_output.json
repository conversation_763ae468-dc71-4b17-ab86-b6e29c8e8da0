{"analytic_type": "emotion", "analytic_series": "mood", "series_analysis_results": [{"evaluation_input": {"aggregation_interval": "7d", "time_input": {"interval": "1d", "time_gte": "2024-12-27T00:00:00.000+00:00", "time_lte": "2025-01-24T00:00:00.000+00:00"}}, "evaluation_output": {"trend": {"consecutive_trend": "decreasing", "independent_variables_mean": 0.86, "absolute_difference_from_previous_bucket": -2.29, "absolute_difference_from_aggregated_buckets": -4.29, "percentage_difference_from_aggregated_buckets": -498.837, "percentage_difference_from_previous_bucket": 200.877}, "statistics": {"max": 5.0, "min": -5.0, "mean": -0.214, "std": 3.326, "quartile_upper": 3.0, "quartile_lower": -3.0, "sum": -6.0}}, "result_status": "calculated_statistics_and_trend", "message": "Data analysis successful. Observed decreasing trend in your data.", "averaged_buckets": [{"aggregated_value": -3.43, "time_range": {"timestamp": "2025-01-17T00:00:00.000+00:00", "end_time": "2025-01-24T00:00:00.000+00:00", "duration": 604800}}, {"aggregated_value": -1.14, "time_range": {"timestamp": "2025-01-10T00:00:00.000+00:00", "end_time": "2025-01-17T00:00:00.000+00:00", "duration": 604800}}, {"aggregated_value": 0.29, "time_range": {"timestamp": "2025-01-03T00:00:00.000+00:00", "end_time": "2025-01-10T00:00:00.000+00:00", "duration": 604800}}, {"aggregated_value": 3.43, "time_range": {"timestamp": "2024-12-27T00:00:00.000+00:00", "end_time": "2025-01-03T00:00:00.000+00:00", "duration": 604800}}]}, {"evaluation_input": {"aggregation_interval": "30d", "time_input": {"interval": "1d", "time_gte": "2024-09-15T00:00:00.000+00:00", "time_lte": "2025-01-12T23:59:59.000+00:00"}}, "evaluation_output": null, "result_status": "calculated_statistics", "message": "Statistical analysis successful, but not enough data to calculate trends.", "averaged_buckets": [{"aggregated_value": 4.0, "time_range": {"timestamp": "2024-12-14T00:00:00.000+00:00", "end_time": "2025-01-13T00:00:00.000+00:00", "duration": 2592000}}, {"aggregated_value": null, "time_range": {"timestamp": "2024-11-14T00:00:00.000+00:00", "end_time": "2024-12-14T00:00:00.000+00:00", "duration": 2592000}}, {"aggregated_value": null, "time_range": {"timestamp": "2024-10-15T00:00:00.000+00:00", "end_time": "2024-11-14T00:00:00.000+00:00", "duration": 2592000}}, {"aggregated_value": null, "time_range": {"timestamp": "2024-09-15T00:00:00.000+00:00", "end_time": "2024-10-15T00:00:00.000+00:00", "duration": 2592000}}]}, {"evaluation_input": {"aggregation_interval": "91d", "time_input": {"interval": "1d", "time_gte": "2024-01-15T00:00:00.000+00:00", "time_lte": "2025-01-12T23:59:59.000+00:00"}}, "evaluation_output": {"trend": null, "statistics": {"max": 4.0, "min": 4.0, "mean": 4.0, "std": 0.0, "quartile_upper": 4.0, "quartile_lower": 4.0, "sum": 4.0}}, "result_status": "calculated_statistics", "message": "Statistical analysis successful, but not enough data to calculate trends.", "averaged_buckets": [{"aggregated_value": 4.0, "time_range": {"timestamp": "2024-10-14T00:00:00.000+00:00", "end_time": "2025-01-13T00:00:00.000+00:00", "duration": 7862400}}, {"aggregated_value": null, "time_range": {"timestamp": "2024-07-15T00:00:00.000+00:00", "end_time": "2024-10-14T00:00:00.000+00:00", "duration": 7862400}}, {"aggregated_value": null, "time_range": {"timestamp": "2024-04-15T00:00:00.000+00:00", "end_time": "2024-07-15T00:00:00.000+00:00", "duration": 7862400}}, {"aggregated_value": null, "time_range": {"timestamp": "2024-01-15T00:00:00.000+00:00", "end_time": "2024-04-15T00:00:00.000+00:00", "duration": 7862400}}]}]}