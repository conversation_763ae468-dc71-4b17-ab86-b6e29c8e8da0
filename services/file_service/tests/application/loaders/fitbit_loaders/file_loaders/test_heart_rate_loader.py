from services.base.domain.enums.data_types import DataType
from services.file_service.application.loaders.fitbit_loaders.file_loaders.heart_rate_loader import HeartRateLoader
from services.file_service.tests.application.loaders.conftest import (
    TEST_DATA_SOURCE_DIR_PATH,
    TEST_UUID,
    basic_loader_test,
)
from settings.app_constants import MESSAGE_NO_FILES_FOUND, PATH_WHICH_DOES_NOT_EXIST


async def test_heart_rate_load_files_data(os_client_mock):
    # Arrange
    heart_rate_loader = HeartRateLoader(
        user_uuid=TEST_UUID,
        data_dir_path=TEST_DATA_SOURCE_DIR_PATH,
        data_type=DataType.HeartRate,
        client=os_client_mock,
    )

    # Act & Assert
    await basic_loader_test(loader=heart_rate_loader, processing_method=heart_rate_loader.load_files_data)


def test_heart_rate_missing_file(caplog, os_client_mock):
    # Arrange
    heart_rate_loader = HeartRateLoader(
        user_uuid=TEST_UUID,
        data_dir_path=TEST_DATA_SOURCE_DIR_PATH,
        data_type=DataType.HeartRate,
        client=os_client_mock,
    )
    heart_rate_loader.search_path = PATH_WHICH_DOES_NOT_EXIST
    # Act
    heart_rate_loader.load_files_data()
    # Assert
    assert MESSAGE_NO_FILES_FOUND in caplog.text
