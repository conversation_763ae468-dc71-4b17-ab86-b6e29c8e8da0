from datetime import date, datetime
from typing import Optional
from zoneinfo import ZoneInfo

from fastapi import Query
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError

from services.base.application.database.models.filter_types import (
    TimestampRangeFilter,
)


def get_date_range_input(
    gte: Optional[date] = Query(default=None),
    lte: Optional[date] = Query(default=None),
) -> TimestampRangeFilter | None:
    if not any((lte, gte)):
        return None
    gte = datetime(gte.year, gte.month, gte.day, tzinfo=ZoneInfo("UTC")) if gte else None
    lte = datetime(lte.year, lte.month, lte.day, tzinfo=ZoneInfo("UTC")) if lte else None
    try:
        return TimestampRangeFilter(gte=gte, lte=lte)
    except ValidationError as error:
        raise RequestValidationError(error.errors())
