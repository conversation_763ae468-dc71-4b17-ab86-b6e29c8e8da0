# syntax = docker/dockerfile:1.2
FROM python:3.13-slim-bookworm AS file_service_base

RUN rm -f /etc/apt/apt.conf.d/docker-clean
RUN --mount=type=cache,sharing=locked,target=/var/cache/apt \
    --mount=type=cache,sharing=locked,target=/var/lib/apt \
    apt-get update && \
    apt-get dist-upgrade -yq && \
    apt-get install -yq \
    apt-transport-https \
    apt-utils build-essential \
    jq \
    fuse \
    libcurl4-openssl-dev \
    libfuse-dev \
    libssl-dev \
    libtool \
    libxml++2.6-dev \
    libxml2-dev \
    mime-support \
    python3-pip tar wget python3-dev \
    libpq-dev \
    tidy
RUN rm -rf /var/lib/apt/lists/*

ENV S3FS_VER=1.90
RUN wget https://github.com/s3fs-fuse/s3fs-fuse/archive/v${S3FS_VER}.tar.gz -O /usr/src/v${S3FS_VER}.tar.gz
RUN --mount=type=cache,target=/usr/src/s3fs-fuse-${S3FS_VER} tar xvz -C /usr/src -f /usr/src/v${S3FS_VER}.tar.gz
RUN --mount=type=cache,target=/usr/src/s3fs-fuse-${S3FS_VER} cd /usr/src/s3fs-fuse-${S3FS_VER} && ./autogen.sh && ./configure --prefix=/usr && make -j && make install

ENV PYTHONUNBUFFERED 1
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONPATH /app_code
ENV IS_CONTAINERIZED true
ENV APP_VERSION $APP_VERSION
ENV BUILD_VERSION $BUILD_VERSION

RUN --mount=type=cache,target=/root/.cache/pip pip install --upgrade uv
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system --upgrade awscli
ENV S3_MOUNT_DIRECTORY=/s3
RUN mkdir ${S3_MOUNT_DIRECTORY}

COPY services/base/requirements.txt ./base_requirements.txt
COPY services/file_service/requirements.txt ./file_requirements.txt
# Combine requirements files, removing duplicates
RUN cat base_requirements.txt file_requirements.txt | sort -u > requirements.txt
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements.txt

WORKDIR /app_code

# Remote stage creates user with limited permissions
FROM file_service_base AS server

COPY ./pyproject.toml ./pyproject.toml
COPY ./Makefile ./Makefile
COPY ./seed_data ./seed_data
COPY ./infrastructure ./infrastructure
COPY ./settings ./settings
COPY ./services/base ./services/base
COPY ./services/file_service ./services/file_service
# TODO: remove
COPY ./services/data_service ./services/data_service
COPY ./services/serverless ./services/serverless

RUN groupadd -g 9995 appuser && \
	useradd -mu 1000 -g appuser appuser

RUN chown -R appuser: /home/<USER>
RUN chown -R appuser: ${S3_MOUNT_DIRECTORY}
RUN touch /etc/passwd-s3fs
RUN chown -R appuser: /etc/passwd-s3fs
RUN chown -R appuser: /app_code
USER appuser

# Development stage adds tools necessary for local development and testing
FROM file_service_base AS local

# Copy over dev requirements file
COPY ./requirements-dev.txt .
RUN --mount=type=cache,target=/root/.cache/pip uv pip install --system -r requirements-dev.txt

COPY ./pyproject.toml ./pyproject.toml
COPY ./Makefile ./Makefile
COPY ./seed_data ./seed_data
COPY ./infrastructure ./infrastructure
COPY ./settings ./settings
COPY ./services/base ./services/base
COPY ./services/file_service ./services/file_service
# TODO: remove
COPY ./services/data_service ./services/data_service
COPY ./services/serverless ./services/serverless