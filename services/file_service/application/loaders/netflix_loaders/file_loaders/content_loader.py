# -*- coding: utf-8 -*-
import json
import re
from datetime import datetime, timedelta
from typing import Optional
from uuid import uuid4

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.metadata_v3 import Origin, Service, SourceOS, SourceService
from services.base.domain.schemas.events.content.video import Video, VideoCategory
from services.base.domain.schemas.events.document_base import (
    EventMetadata,
    RBACSchema,
)
from services.base.domain.time import parse_datetime_tz_fallback
from services.base.infrastructure.database.opensearch.query_methods.data_fetchers import get_last_timestamp
from services.file_service.application.loaders.file_loader_single_csv import FileLoaderSingleCsv
from services.file_service.application.loaders.netflix_loaders.netflix_constants import (
    NETFLIX_CONTENT_INTERACTION_PATH,
    NETFLIX_LABEL_NETFLIX_VIEWING_START_TIME,
)


class ContentLoader(FileLoaderSingleCsv):
    """Loader for Netflix ViewingActivity.csv file"""

    FOLDER = NETFLIX_CONTENT_INTERACTION_PATH
    FILENAME = "ViewingActivity.csv"

    # USING PARENT __init__ ONLY ! - YES, IT DOES THE JOB!

    def _last_timestamp(self) -> Optional[datetime]:
        return get_last_timestamp(
            self.user_uuid,
            self.client,
            self.data_type,
        )

    def _process_entry(self, entry: dict, last_timestamp: Optional[datetime]) -> Optional[str]:
        timestamp = parse_datetime_tz_fallback(
            value=entry[NETFLIX_LABEL_NETFLIX_VIEWING_START_TIME]
        )  # 2020-08-20 19:06:04`
        if not timestamp:
            return None

        # Localize
        timestamp = timestamp.astimezone(self.DEFAULT_TIMEZONE)
        h, m, s = entry["Duration"].split(":")
        duration = timedelta(hours=int(h), minutes=int(m), seconds=int(s))
        end_time = timestamp + duration

        title = entry["Title"]
        name = self._extract_track_name_from_title(title)
        content_category = (
            VideoCategory.TV_SHOW if "season" in title.lower() or "episode" in title else VideoCategory.MOVIE
        )
        video = Video(
            type=DataType.Video,
            id=uuid4(),
            submission_id=uuid4(),
            timestamp=timestamp,
            end_time=end_time,
            category=content_category,
            name=name,
            title=title,
            url=None,
            rating=None,
            note=None,
            template_id=None,
            metadata=EventMetadata(
                organization=Organization.NETFLIX,
                origin=Origin.NETFLIX,
                service=Service.FILE,
                origin_device=None,
                source_os=SourceOS.UNKNOWN,
                source_service=SourceService.TAKEOUT,
            ),
            rbac=RBACSchema(
                owner_id=self.user_uuid,
            ),
            asset_references=[],
            tags=[str(entry["Profile Name"]).lower(), "netflix"],
            plan_extension=None,
            group_id=None,
        )

        # Due to tags being represented differently on the persistence layer,
        # we have to map list[str] to list[dict] so that ["hello", "world"] => [{"tag": "hello"}, {"tag": "world"}]
        model_dict = json.loads(video.model_dump_json())
        model_dict["tags"] = [{"tag": t} for t in video.tags] if video.tags else []
        return json.dumps(model_dict)

    def _extract_track_name_from_title(self, title: str) -> str:
        # Split title into parts
        parts = title.split(":", 1)
        first_part = parts[0].strip()
        rest = parts[1].strip() if len(parts) > 1 else ""

        # Metadata indicators
        metadata_keywords = {
            "season",
            "episode",
            "clip",
            "trailer",
            "teaser",
            "hook",
            "cinemagraph",
            "series",
            "limited",
            "motion",
            "billboard",
            "character",
            "intro",
            "plot",
            "genre",
            "fundamentals",
            "blended",
            "behind the scenes",
            "recap",
            "highlight",
        }

        # Check for metadata patterns
        has_metadata = any(keyword in first_part.lower() for keyword in metadata_keywords) or any(
            c.isdigit() for c in first_part
        )

        # Choose candidate portion
        candidate = rest if (has_metadata and rest) else first_part

        # Clean up the candidate
        base = candidate.split("_", 1)[0].split(" (", 1)[0].strip()

        # Final exclusion patterns using regex
        metadata_pattern = re.compile(
            r"^(?:Season|Clip|Teaser|Trailer|Episode|Main Character|Character Intro|Plot|Genre)\b.*\d", re.IGNORECASE
        )

        if metadata_pattern.match(base):
            return title

        if any(keyword in base.lower() for keyword in metadata_keywords) and any(c.isdigit() for c in base):
            return title

        return base
