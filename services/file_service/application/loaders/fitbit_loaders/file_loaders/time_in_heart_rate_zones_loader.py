# -*- coding: utf-8 -*-
import json
import logging
import os
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch

from services.base.application.utils.encoders import json_serializer
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.enums.data_types import DataType
from services.base.infrastructure.database.opensearch.query_methods.data_fetchers import get_last_timestamp
from services.base.infrastructure.database.opensearch.query_methods.utils import is_datetime_not_newer_than_datetime
from services.file_service.application.converters import partial_timestamp_parser
from services.file_service.application.file_handlers import find_file_paths_matching_regex_format
from services.file_service.application.loaders.file_loader_base import FileLoaderBase
from services.file_service.application.loaders.fitbit_loaders.fitbit_constants import (
    FITBIT_PHYSICAL_ACTIVITIES,
    FITBIT_ROOT_PATH,
)
from services.file_service.application.loaders.fitbit_loaders.utils import get_person_folder
from settings.app_constants import MESSAGE_NO_FILES_FOUND, MESSAGE_UNABLE_TO_LOAD


class TimeInHeartRateZonesLoader(FileLoaderBase):
    """Loader for Fit Bit fitbit_time_in_heart_rate_zones*.json files"""

    def __init__(
        self,
        user_uuid: UUID,
        data_dir_path: str,
        data_type: DataType,
        client: OpenSearch = None,
        fallback_timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC,
    ):
        super().__init__(user_uuid=user_uuid, data_type=data_type, client=client, fallback_timezone=fallback_timezone)

        self.file_paths = []
        fitbit_physical_activities_path = os.path.join(
            get_person_folder(os.path.join(data_dir_path, FITBIT_ROOT_PATH)), FITBIT_PHYSICAL_ACTIVITIES
        )
        self.search_path = os.path.join(data_dir_path, fitbit_physical_activities_path)

    def load_files_data(self) -> None:
        # Example Heart Rate file: time_in_heart_rate_zones-2018-04-22.json
        regex_format = r"^time_in_heart_rate_zones.*json$"
        self.file_paths = list(find_file_paths_matching_regex_format(self.search_path, regex_format))
        if not self.file_paths:
            logging.exception("%s! Folder: %s", MESSAGE_NO_FILES_FOUND, self.search_path)

        return self.process_data()

    def process_data(self) -> None:
        entries = []
        last_timestamp = get_last_timestamp(
            self.user_uuid,
            self.client,
            self.data_type,
        )
        for file_path in self.file_paths:
            try:
                with open(file_path, encoding="utf8") as json_file:
                    data = json.load(json_file)

                    # time_in_heart_rate_zones always contain only 1 record, but still wrapped in a list/array
                    data = data[0]

                    data[DocumentLabels.USER_UUID] = str(self.user_uuid)
                    data["dateTime"] = self._parse_tz_datetime(partial_timestamp_parser(data.get("dateTime")))
                    data[DocumentLabels.TIMESTAMP] = self._parse_tz_datetime(data["dateTime"])

                    if is_datetime_not_newer_than_datetime(data[DocumentLabels.TIMESTAMP], last_timestamp):
                        continue
                    entries.append(json.dumps(data, default=json_serializer))
                    self._commit_if_limit_reached(entries)

            except FileNotFoundError:
                logging.exception("%s! File path: %s", MESSAGE_UNABLE_TO_LOAD, file_path)

        self._commit(entries=entries)
