"""FACEBOOK FILE LOADERS RUNNER"""

from uuid import UUID
from zoneinfo import ZoneInfo

from services.base.domain.enums.provider import SupportedDataProviders
from services.file_service.application.loaders.file_loader_runner_base import FileLoadersRunnerBase


class FacebookFileLoadersRunner(FileLoadersRunnerBase):
    """Runs all current Facebook file loaders"""

    provider: SupportedDataProviders = SupportedDataProviders.FACEBOOK

    def __init__(self, user_uuid: UUID, data_dir_path: str, fallback_timezone: ZoneInfo):
        super().__init__(user_uuid=user_uuid, data_dir_path=data_dir_path, fallback_timezone=fallback_timezone)

        self.run_loaders([])
