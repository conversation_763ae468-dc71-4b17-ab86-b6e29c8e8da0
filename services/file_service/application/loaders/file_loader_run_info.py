from dataclasses import dataclass
from typing import Dict, List, Type, Union

from services.base.domain.enums.data_types import DataType
from services.file_service.application.loaders.file_loader_base import FileLoaderBase


@dataclass(frozen=True)
class FileLoaderRunInfo:
    """Type struct for information needed for a single loader run
    Args:
        indexes: List[T]
            T: str: if loader works with 1 index per run
            T: Dict[str, str]: USE KEYS FROM THAT LOADER so loader can differentiate which index is for what
                               for cases when loader works with 2 or more indexes per run
    """

    loader: Type[FileLoaderBase]
    data_types: List[Union[DataType, Dict[str, str]]]
