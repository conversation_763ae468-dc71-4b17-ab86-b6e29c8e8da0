import os

#  File Paths
from services.base.domain.enums.provider import Provider

GOOGLE_TAKEOUT_ROOT_PATH = os.path.join(Provider.GOOGLE.value, "Takeout")
GOOGLE_TAKEOUT_FIT_PATH = os.path.join(GOO<PERSON><PERSON>_TAKEOUT_ROOT_PATH, "Fit")
GOOGLE_TAKEOUT_FIT_METRICS = os.path.join("Daily activity metrics")
GOOGLE_TAKEOUT_FIT_ACTIVITIES = os.path.join("Activities")
GOOGLE_TAKEOUT_CHROME_PATH = os.path.join(GOOGLE_TAKEOUT_ROOT_PATH, "Chrome")
GOOGLE_TAKEOUT_LOCATION_PATH = os.path.join(GOOGLE_TAKEOUT_ROOT_PATH, "Location History")
GOOGLE_TAKEOUT_MYACTIVITY_CHROME_PATH = os.path.join(GOOGLE_TAKEOUT_ROOT_PATH, "My Activity", "Chrome")
GOOGLE_TAKEOUT_MYACTIVITY_DISCOVER_PATH = os.path.join(GOOGLE_TAKEOUT_ROOT_PATH, "My Activity", "Discover")
GOOGLE_TAKEOUT_MYACTIVITY_GOOGLE_NEWS_PATH = os.path.join(GOOGLE_TAKEOUT_ROOT_PATH, "My Activity", "Google News")
GOOGLE_TAKEOUT_MYACTIVITY_NEWS_PATH = os.path.join(GOOGLE_TAKEOUT_ROOT_PATH, "My Activity", "News")
GOOGLE_TAKEOUT_MYACTIVITY_SEARCH_PATH = os.path.join(GOOGLE_TAKEOUT_ROOT_PATH, "My Activity", "Search")
GOOGLE_TAKEOUT_MYACTIVITY_VIDEO_SEARCH_PATH = os.path.join(GOOGLE_TAKEOUT_ROOT_PATH, "My Activity", "Video Search")
GOOGLE_TAKEOUT_MYACTIVITY_YOUTUBE_PATH = os.path.join(GOOGLE_TAKEOUT_ROOT_PATH, "My Activity", "YouTube")
GOOGLE_TAKEOUT_SEMANTIC_LOCATION_PATH = os.path.join(
    GOOGLE_TAKEOUT_ROOT_PATH, "Location History", "Semantic Location History"
)

GOOGLE_TAKEOUT_YOUTUBE_PATH = os.path.join(GOOGLE_TAKEOUT_ROOT_PATH, "YouTube and YouTube Music", "playlists")

GOOGLE_LABEL_GOOGLE_STEP_COUNT = "Step count"
GOOGLE_LABEL_GOOGLE_SLEEP_DURATION = "Sleep duration (ms)"
GOOGLE_LABEL_GOOGLE_MAX_HEART_RATE = "Max heart rate (bpm)"
GOOGLE_LABEL_GOOGLE_MIN_HEART_RATE = "Min heart rate (bpm)"
GOOGLE_LABEL_GOOGLE_MAX_ALTITUDE = "Max Altitude"
GOOGLE_LABEL_GOOGLE_MIN_ALTITUDE = "Min Altitude"
GOOGLE_LABEL_GOOGLE_AVERAGE_HEART_RATE = "Average heart rate (bpm)"
GOOGLE_LABEL_GOOGLE_ACTIVITY_NOTE = "Activity Note"
GOOGLE_LABEL_GOOGLE_ACTIVITY_TYPE = "Activity Type"
GOOGLE_LABEL_GOOGLE_ACTIVITY_START_TIME = "Start Time"
GOOGLE_LABEL_GOOGLE_ACTIVITY_END_TIME = "End Time"
GOOGLE_LABEL_GOOGLE_ACTIVITY_TRACK = "Track"
GOOGLE_LABEL_GOOGLE_ACTIVITY_LAP = "Lap"
GOOGLE_LABEL_GOOGLE_ACTIVITY_TIME = "Time"
GOOGLE_LABEL_GOOGLE_ACTIVITY_INTENSITY = "Intensity"
GOOGLE_LABEL_GOOGLE_ACTIVITY_DISTANCE = "DistanceMeters"
GOOGLE_LABEL_GOOGLE_ACTIVITY_ALTITUDE = "AltitudeMeters"
GOOGLE_LABEL_GOOGLE_ACTIVITY_TRIGGER_METHOD = "TriggerMethod"
GOOGLE_LABEL_GOOGLE_ACTIVITY_AVERAGE_HEART_RATE = "AverageHeartRateBpm"
GOOGLE_LABEL_GOOGLE_ACTIVITY_MAXIMUM_HEART_RATE = "MaximumHeartRateBpm"
GOOGLE_LABEL_GOOGLE_ACTIVITY_HEART_RATE = "HeartRateBpm"
