"""GOOGLE FILE LOADERS RUNNER"""

from uuid import UUID
from zoneinfo import ZoneInfo

from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.provider import SupportedDataProviders
from services.file_service.application.loaders.file_loader_run_info import FileLoaderRunInfo
from services.file_service.application.loaders.file_loader_runner_base import FileLoadersRunnerBase
from services.file_service.application.loaders.google_loaders.file_loaders.fit_activity_metrics_loader import (
    FitActivityMetricsLoader,
)

from .file_loaders.myactivity.json_loaders.myactivity_chrome_json_loader import MyActivityChromeJsonLoader
from .file_loaders.myactivity.json_loaders.myactivity_google_news_json_loader import MyActivityGoogleNewsJsonLoader
from .file_loaders.myactivity.json_loaders.myactivity_news_json_loader import MyActivityNewsJsonLoader
from .file_loaders.myactivity.json_loaders.myactivity_search_json_loader import (
    MyActivitySearchJsonLoader,
)
from .file_loaders.myactivity.json_loaders.myactivity_video_search_json_loader import MyActivityVideoSearchJsonLoader
from .file_loaders.myactivity.json_loaders.myactivity_youtube_json_loader import MyActivityYoutubeJsonLoader
from .file_loaders.semantic_location_loader import SemanticLocationLoader


class GoogleFileLoadersRunner(FileLoadersRunnerBase):
    """Runs all current GOOGLE file loaders"""

    provider: SupportedDataProviders = SupportedDataProviders.GOOGLE

    def __init__(
        self,
        user_uuid: UUID,
        data_dir_path: str,
        fallback_timezone: ZoneInfo,
    ):
        super().__init__(user_uuid=user_uuid, data_dir_path=data_dir_path, fallback_timezone=fallback_timezone)

        self.run_loaders(
            [
                FileLoaderRunInfo(loader=SemanticLocationLoader, data_types=[DataType.Location]),
                FileLoaderRunInfo(loader=MyActivityChromeJsonLoader, data_types=[DataType.Content]),
                FileLoaderRunInfo(loader=MyActivityGoogleNewsJsonLoader, data_types=[DataType.Content]),
                FileLoaderRunInfo(loader=MyActivityNewsJsonLoader, data_types=[DataType.Content]),
                FileLoaderRunInfo(loader=MyActivitySearchJsonLoader, data_types=[DataType.Content]),
                FileLoaderRunInfo(loader=MyActivityVideoSearchJsonLoader, data_types=[DataType.Content]),
                FileLoaderRunInfo(loader=MyActivityYoutubeJsonLoader, data_types=[DataType.Content]),
            ]
        )

        FitActivityMetricsLoader(
            user_uuid=self.user_uuid, data_dir_path=self.data_dir_path, fallback_timezone=fallback_timezone
        ).load_files_data()
