from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.extension_labels.single_correlation_labels import SingleCorrelationLabels
from services.base.domain.constants.extension_labels.trend_insights_labels import TrendInsightsLabels
from settings.extension_constants import SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID, TREND_INSIGHTS_EXTENSION_ID

CommonExtensionFields = [DocumentLabels.TIMESTAMP]

ExtensionSpecificCSVMappings = {
    TREND_INSIGHTS_EXTENSION_ID: [
        TrendInsightsLabels.ANALYTIC_TYPE,
        TrendInsightsLabels.ANALYTIC_SERIES,
        TrendInsightsLabels.AGGREGATION_INTERVAL,
        DocumentLabels.TIME_GTE,
        DocumentLabels.TIME_LTE,
        TrendInsightsLabels.CONSECUTIVE_TREND,
        TrendInsightsLabels.INDEPENDENT_VARIABLES_MEAN,
        TrendInsightsLabels.ABSOLUTE_DIFFERENCE_FROM_PREVIOUS_BUCKET,
        TrendInsightsLabels.ABSOLUTE_DIFFERENCE_FROM_AGGREGATED_BUCKETS,
        TrendInsightsLabels.PERCENTAGE_DIFFERENCE_FROM_PREVIOUS_BUCKET,
        TrendInsightsLabels.PERCENTAGE_DIFFERENCE_FROM_AGGREGATED_BUCKETS,
        TrendInsightsLabels.MAX,
        TrendInsightsLabels.MIN,
        TrendInsightsLabels.MEAN,
        TrendInsightsLabels.STD,
        TrendInsightsLabels.QUARTILE_UPPER,
        TrendInsightsLabels.QUARTILE_LOWER,
        TrendInsightsLabels.SUM,
    ],
    SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID: [
        SingleCorrelationLabels.OUTCOME_NAME,
        SingleCorrelationLabels.TRIGGER_NAME,
        SingleCorrelationLabels.OUTCOME_DOCUMENT_COUNT,
        SingleCorrelationLabels.TRIGGER_DOCUMENT_COUNT,
        SingleCorrelationLabels.IMMEDIATE_TERM,
        SingleCorrelationLabels.SHORT_TERM,
        SingleCorrelationLabels.MEDIUM_TERM,
        SingleCorrelationLabels.LONG_TERM,
        SingleCorrelationLabels.MAX_CORRELATION,
        SingleCorrelationLabels.CONFIDENCE,
        SingleCorrelationLabels.RESULT_IMPLICATION,
    ],
}
