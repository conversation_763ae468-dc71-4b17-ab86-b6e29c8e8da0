from dataclasses import fields

from services.base.domain.schemas.events.nutrition.nutrients import NutrientsFields
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionFields
from services.file_service.application.enums.exportable_data_type import ExportableType
from services.file_service.application.mappings.csv_mappings.v3_documents.v3_non_collection_mappings import (
    V3CSVEventBaseMapping,
)


def create_csv_field_string(*args):
    return ".".join(args)


NutritionCollectionCSVMappings = {
    ExportableType.Drink: [
        *V3CSVEventBaseMapping,
        # Nutrition Fields
        NutritionFields.NAME,
        NutritionFields.RATING,
        NutritionFields.NOTE,
        NutritionFields.UNIT,
        NutritionFields.CONSUMED_AMOUNT,
        NutritionFields.CONSUMED_TYPE,
        NutritionFields.CONSUMED_AMOUNT,
        NutritionFields.CALORIES,
        *[create_csv_field_string(NutritionFields.NUTRIENTS, field.default) for field in fields(NutrientsFields)],
    ],
    ExportableType.Food: [
        *V3CSVEventBaseMapping,
        # Nutrition Fields
        NutritionFields.NAME,
        NutritionFields.RATING,
        NutritionFields.NOTE,
        NutritionFields.UNIT,
        NutritionFields.CONSUMED_AMOUNT,
        NutritionFields.CONSUMED_TYPE,
        NutritionFields.CONSUMED_AMOUNT,
        NutritionFields.CALORIES,
        *[create_csv_field_string(NutritionFields.NUTRIENTS, field.default) for field in fields(NutrientsFields)],
    ],
    ExportableType.Supplement: [
        *V3CSVEventBaseMapping,
        # Nutrition Fields
        NutritionFields.NAME,
        NutritionFields.RATING,
        NutritionFields.NOTE,
        NutritionFields.UNIT,
        NutritionFields.CONSUMED_AMOUNT,
        NutritionFields.CONSUMED_TYPE,
        NutritionFields.CONSUMED_AMOUNT,
        NutritionFields.CALORIES,
        *[create_csv_field_string(NutritionFields.NUTRIENTS, field.default) for field in fields(NutrientsFields)],
    ],
}
