from typing import List

from services.base.application.event_models.upload_event_model import UploadEventModel
from services.base.domain.constants.messaging import (
    MessageTopics,
)
from services.base.message_queue.message_handler_base import UseCaseHandlerBase
from services.base.telemetry.telemetry_instrumentor import TelemetryInstrumentor
from services.file_service.application.use_cases.upload_use_case.log_on_upload_use_case import LogOnUploadUseCase
from settings.app_config import settings
from settings.app_secrets import secrets


class LogOnUploadHandler(UseCaseHandlerBase):
    @classmethod
    def listen_to(cls) -> List[MessageTopics]:
        return [MessageTopics.TOPIC_UPLOAD_FINISHED]

    def execute(self, message_body: dict, *args, **kwargs):
        super()._do_execute_sync(triggering_event_model=UploadEventModel(**message_body), use_case=LogOnUploadUseCase())

    def publish(self, triggering_event: UploadEventModel) -> None:
        return None

    @staticmethod
    def initialize_and_execute(*args, **kwargs):
        TelemetryInstrumentor.initialize(service_name="file_service_worker", settings=settings, secrets=secrets)
        return LogOnUploadHandler().execute(*args, **kwargs)
