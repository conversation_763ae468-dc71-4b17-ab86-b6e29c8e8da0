from __future__ import annotations

from enum import StrEnum
from typing import Sequence

from pydantic import Field

from services.base.api.query.leaf_query_api import LeafQueryAPIAnnotated
from services.base.domain.schemas.shared import BaseDataModel


class BooleanQueryType(StrEnum):
    AND = "and"
    OR = "or"
    NOT = "not"


class CompoundBooleanQueryAPI(BaseDataModel):
    type: BooleanQueryType = Field(...)
    queries: Sequence[LeafQueryAPIAnnotated | CompoundBooleanQueryAPI] = Field(min_length=1)
