from abc import ABC, abstractmethod
from typing import Optional, Sequence
from uuid import UUID

from services.base.application.database.models.sorts import Sort
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery


class EventRepository(ABC):
    @abstractmethod
    async def delete_by_id(self, ids: Sequence[UUID]) -> Sequence[UUID]:
        pass

    @abstractmethod
    async def insert(self, events: Sequence[Event], force_strong_consistency: bool = False) -> Sequence[Event]:
        pass

    @abstractmethod
    async def search_by_id(self, ids: Sequence[UUID]) -> Sequence[Event]:
        pass

    @abstractmethod
    async def update(self, events: Sequence[Event], force_strong_consistency: bool = False) -> Sequence[Event]:
        pass

    @abstractmethod
    async def search_by_single_query[T: Event](
        self,
        size: int,
        query: SingleDocumentTypeQuery[T],
        sorts: Optional[Sequence[Sort]] = None,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[T]:
        pass
