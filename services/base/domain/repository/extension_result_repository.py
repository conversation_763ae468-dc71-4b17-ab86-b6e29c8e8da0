from abc import ABC, abstractmethod
from typing import Sequence
from uuid import UUID

from services.base.application.boundaries.documents import ExtensionResultOutputBoundary
from services.base.domain.schemas.extension_output import ExtensionResult


class ExtensionResultRepository(ABC):
    @abstractmethod
    async def insert(
        self,
        extension_results: Sequence[ExtensionResult],
        parent_id: UUID,
        force_strong_consistency: bool = True,
    ) -> Sequence[ExtensionResult]:
        pass

    @abstractmethod
    async def search_by_id(self, ids: Sequence[UUID]) -> Sequence[ExtensionResult]:
        pass

    @abstractmethod
    async def delete_by_id(self, ids: Sequence[UUID]) -> Sequence[UUID]:
        pass

    @abstractmethod
    async def get_all_children(self, parent_id: UUID, size: int = 1000) -> Sequence[ExtensionResultOutputBoundary]:
        pass
