from abc import ABC, abstractmethod
from datetime import timedel<PERSON>
from typing import AsyncIterator, Optional, Sequence
from uuid import UUID

from services.base.domain.repository.wrappers import (
    ReadFromDatabaseWrapper,
)
from services.base.domain.schemas.member_user.member_user import MemberUser


class MemberUserRepository(ABC):
    @abstractmethod
    async def get(self, wrapper: ReadFromDatabaseWrapper) -> Sequence[MemberUser]:
        pass

    @abstractmethod
    async def get_by_uuid(self, user_uuid: UUID) -> Optional[MemberUser]:
        pass

    @abstractmethod
    async def insert_or_update(self, user: MemberUser) -> Optional[MemberUser]:
        pass

    @abstractmethod
    def yield_results(self, wrapper: ReadFromDatabaseWrapper, size: int = 1000) -> AsyncIterator[Sequence[MemberUser]]:
        pass

    @abstractmethod
    async def delete(self, user: MemberUser) -> None:
        pass

    @abstractmethod
    async def count(self, wrapper: ReadFromDatabaseWrapper) -> int:
        pass

    @abstractmethod
    def yield_active_users(
        self, delta: timedelta = timedelta(days=30), batch_size: int = 50
    ) -> AsyncIterator[Sequence[MemberUser]]:
        pass
