from tidylib import tidy_fragment

from services.base.domain.validations.validate_non_empty_length import validate_non_empty_length


def validate_html(html_as_str: str) -> str:
    validate_non_empty_length(s=html_as_str)
    html, errors = tidy_fragment(
        text=html_as_str,
        options={"indent": False, "wrap": 0, "show-warnings": 0},
    )
    if len(errors) > 0:
        raise ValueError(f"HTML is not valid. Remove following problems:\n {errors}")
    return html
