from dataclasses import dataclass
from datetime import datetime

from pydantic import Field

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.usage_statistics_labels import (
    UsageStatisticsLabels,
)
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_v3_type import EventV3Type
from services.base.domain.enums.usage_statistics import ReportTimeframe
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.shared import BaseDataModel, TimestampModel


@dataclass(frozen=True)
class UsageStatisticsOutputFields:
    ID: str = DocumentLabels.ID
    USAGE_STATISTICS_RESULTS = UsageStatisticsLabels.LABEL_USAGE_STATISTICS_RESULTS
    REPORT_TIMEFRAME = UsageStatisticsLabels.LABEL_REPORT_TIMEFRAME
    AGGREGATION_INTERVAL_START = UsageStatisticsLabels.LABEL_AGGREGATION_INTERVAL_START
    TIME_OF_EXECUTION = UsageStatisticsLabels.LABEL_TIME_OF_EXECUTION


@dataclass(frozen=True)
class UsageStatisticsResultOutputFields:
    NEW_USERS = UsageStatisticsLabels.LABEL_NEW_USERS
    NEW_ANONYMOUS_USERS = UsageStatisticsLabels.LABEL_NEW_ANONYMOUS_USERS
    TOTAL_USERS = UsageStatisticsLabels.LABEL_TOTAL_USERS
    TOTAL_ANONYMOUS_USERS = UsageStatisticsLabels.LABEL_TOTAL_ANONYMOUS_USERS
    ACTIVE_USERS = UsageStatisticsLabels.LABEL_ACTIVE_USERS
    ACTIVE_ANONYMOUS_USERS = UsageStatisticsLabels.LABEL_ACTIVE_ANONYMOUS_USERS
    STALE_USERS = UsageStatisticsLabels.LABEL_STALE_USERS
    MULTIPLE_DEVICE_USERS = UsageStatisticsLabels.LABEL_MULTIPLE_DEVICE_USERS
    USERS_WITH_NOTES = UsageStatisticsLabels.LABEL_USERS_WITH_NOTES
    USERS_WITH_LOCATION = UsageStatisticsLabels.LABEL_USERS_WITH_LOCATION
    USERS_WITH_HEARTRATE = UsageStatisticsLabels.LABEL_USERS_WITH_HEARTRATE
    USERS_WITH_RATING = UsageStatisticsLabels.LABEL_USERS_WITH_RATING
    USERS_WITH_EVENTS = UsageStatisticsLabels.LABEL_USERS_WITH_EVENTS
    DOCUMENT_COUNT_CSV_STRING = UsageStatisticsLabels.LABEL_DOCUMENT_COUNT_CSV_STRING


class UsageStatisticsResultEntity(BaseDataModel):
    new_users: int = Field(
        alias=UsageStatisticsResultOutputFields.NEW_USERS, ge=0, description="number of new users registered"
    )
    new_anonymous_users: int = Field(
        alias=UsageStatisticsResultOutputFields.NEW_ANONYMOUS_USERS,
        ge=0,
        description="number of new anonymous users registered",
    )
    total_users: int = Field(
        alias=UsageStatisticsResultOutputFields.TOTAL_USERS,
        ge=0,
        description="number of total users registered on the app",
    )
    total_anonymous_users: int = Field(
        alias=UsageStatisticsResultOutputFields.TOTAL_ANONYMOUS_USERS,
        ge=0,
        description="number of total anonymous users registered on the app",
    )
    active_users: int = Field(
        alias=UsageStatisticsResultOutputFields.ACTIVE_USERS, ge=0, description="number of active users in timeframe"
    )
    active_anonymous_users: int = Field(
        alias=UsageStatisticsResultOutputFields.ACTIVE_ANONYMOUS_USERS,
        ge=0,
        description="number of active anonymous users in timeframe",
    )
    stale_users: int = Field(
        alias=UsageStatisticsResultOutputFields.STALE_USERS,
        ge=0,
        description="number of users that have not logged in for 6 months",
    )
    multiple_device_users: int = Field(
        alias=UsageStatisticsResultOutputFields.MULTIPLE_DEVICE_USERS,
        ge=0,
        description="users with multiple devices registered",
    )
    users_with_events: dict[EventV3Type, int] = Field(
        alias=UsageStatisticsResultOutputFields.USERS_WITH_EVENTS,
        description="object field which stores detailed information about diary events",
    )
    users_with_location: int = Field(
        alias=UsageStatisticsResultOutputFields.USERS_WITH_LOCATION,
        ge=0,
        description="number of unique users that have location posted",
    )
    users_with_heartrate: int = Field(
        alias=UsageStatisticsResultOutputFields.USERS_WITH_HEARTRATE,
        ge=0,
        description="number of unique users that have heartrate documents",
    )
    document_count_csv_string: str = Field(
        alias=UsageStatisticsResultOutputFields.DOCUMENT_COUNT_CSV_STRING,
        description="stores a string representation of csv table with document counts",
    )


class UsageStatisticsIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.UsageStatistics


class UsageStatistics(TimestampModel, Document, UsageStatisticsIdentifier):
    usage_statistics_results: UsageStatisticsResultEntity = Field(
        alias=UsageStatisticsLabels.LABEL_USAGE_STATISTICS_RESULTS,
        description="object field that contains the usage statistics results",
    )
    report_timeframe: ReportTimeframe = Field(
        alias=UsageStatisticsOutputFields.REPORT_TIMEFRAME,
        description="keyword field that specifies the report type (Daily, Weekly, Monthly, Quarterly, Yearly)",
    )
    aggregation_interval_start: datetime = Field(
        alias=UsageStatisticsOutputFields.AGGREGATION_INTERVAL_START,
        description="datetime field that stores the start of the inspected timeframe",
    )
