from __future__ import annotations

from typing import Annotated, Literal

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.event_type import EventType
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.templates.payload.activity_template_payload import ActivityTemplatePayload
from services.base.domain.schemas.templates.payload.body_metric_template_payloads import (
    BloodGlucoseTemplatePayload,
    BloodPressureTemplatePayload,
    BodyMetricTemplatePayload,
)
from services.base.domain.schemas.templates.payload.content_template_payloads import (
    AudioTemplatePayload,
    ContentTemplatePayload,
    ImageTemplatePayload,
    InteractiveTemplatePayload,
    TextTemplatePayload,
    VideoTemplatePayload,
)
from services.base.domain.schemas.templates.payload.exercise_template_payloads import (
    CardioTemplatePayload,
    ExerciseTemplatePayload,
    StrengthTemplatePayload,
)
from services.base.domain.schemas.templates.payload.feeling_template_payloads import (
    EmotionTemplatePayload,
    StressTemplatePayload,
)
from services.base.domain.schemas.templates.payload.medication_template_payload import MedicationTemplatePayload
from services.base.domain.schemas.templates.payload.note_template_payload import NoteTemplatePayload
from services.base.domain.schemas.templates.payload.nutrition_collection_template_payloads import (
    DrinkTemplatePayload,
    FoodTemplatePayload,
    SupplementTemplatePayload,
)
from services.base.domain.schemas.templates.payload.person_template_payload import PersonTemplatePayload
from services.base.domain.schemas.templates.payload.sleep_v3_template_payload import SleepV3TemplatePayload
from services.base.domain.schemas.templates.payload.symptom_template_payload import SymptomTemplatePayload
from services.base.domain.schemas.templates.template import Template, TemplateFields


class EventTemplateIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.EventTemplate


class EventTemplate(Template, EventTemplateIdentifier):
    type: Literal[DataType.EventTemplate] = Field(alias=TemplateFields.TYPE)
    document: TypedTemplatePayloads = Field(alias=TemplateFields.DOCUMENT)
    document_name: NonEmptyStr = Field(alias=TemplateFields.DOCUMENT_NAME)
    document_type: EventType = Field(alias=TemplateFields.DOCUMENT_TYPE)


TemplatePayloads = (
    # Content
    AudioTemplatePayload
    | ContentTemplatePayload
    | ImageTemplatePayload
    | InteractiveTemplatePayload
    | TextTemplatePayload
    | VideoTemplatePayload
    # Body Metrics
    | BloodGlucoseTemplatePayload
    | BloodPressureTemplatePayload
    | BodyMetricTemplatePayload
    # Exercise
    | ExerciseTemplatePayload
    | CardioTemplatePayload
    | StrengthTemplatePayload
    # Feeling
    | EmotionTemplatePayload
    | StressTemplatePayload
    # Nutrition
    | DrinkTemplatePayload
    | FoodTemplatePayload
    | SupplementTemplatePayload
    # Other Events
    | ActivityTemplatePayload
    | NoteTemplatePayload
    | SymptomTemplatePayload
    | MedicationTemplatePayload
    | SleepV3TemplatePayload
    | PersonTemplatePayload
)
TypedTemplatePayloads = Annotated[TemplatePayloads, Field(discriminator=DocumentLabels.TYPE)]
