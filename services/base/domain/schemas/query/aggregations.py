from __future__ import annotations

from enum import StrEnum
from typing import Sequence
from zoneinfo import ZoneInfo

from pydantic import Field

from services.base.application.boundaries.time_input import TimeIntervalInput
from services.base.domain.annotated_types import NonEmptyStr
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.schemas.shared import BaseDataModel


class SimpleAggregationMethod(StrEnum):
    SUM = "sum"
    MIN = "min"
    MAX = "max"
    AVG = "avg"
    COUNT = "count"


class AggregationMethod(StrEnum):
    STATS = "stats"
    SUM = "sum"
    MIN = "min"
    MAX = "max"
    AVG = "avg"
    COUNT = "count"


class BucketAggregationMethod(StrEnum):
    DERIVATIVE = "derivative"
    MOVING_AVG = "moving_avg"
    CUMULATIVE_SUM = "cumulative_sum"


class BucketFieldAggregation(BaseDataModel):
    aggregation_method: BucketAggregationMethod
    window: int = Field(ge=1, le=100, default=10, description="window size for moving average")


class FieldAggregation(BaseDataModel):
    field_name: NonEmptyStr = Field(min_length=1)
    aggregation_method: AggregationMethod | None = None
    bucket_aggregation: Sequence[BucketFieldAggregation] = Field(default_factory=list)


class DateHistogramAggregation(TimeIntervalInput):
    default_aggregation_method: AggregationMethod
    histogram_field_aggregations: Sequence[FieldAggregation]
    timezone: ZoneInfo = DEFAULT_TIMEZONE_UTC


class CalendarAggregationType(StrEnum):
    MONTH_NAMES = "month_names"
    HOURS_IN_DAY = "hours_in_day"
    PARTS_OF_MONTH = "parts_of_month"
    DAYS_OF_MONTH = "days_of_month"
    WEEKDAYS = "weekdays"
    LUNAR_PHASES = "lunar_phases"
    TIME_BETWEEN = "time_between"
