from typing import List, Optional
from uuid import UUID

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.metadata import Organization
from services.base.domain.schemas.metadata import MetadataFields
from services.base.domain.schemas.query.leaf_query import RangeQuery, ValuesQuery


class CommonLeafQueries:

    @staticmethod
    def owner_id_value_query(user_uuid: UUID) -> ValuesQuery:
        return ValuesQuery(field_name=f"{DocumentLabels.RBAC}.{DocumentLabels.OWNER_ID}", values=[str(user_uuid)])

    @staticmethod
    def metadata_user_uuid_value_query(user_uuid: UUID) -> ValuesQuery:
        return ValuesQuery(field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.USER_UUID}", values=[str(user_uuid)])

    @staticmethod
    def timestamp_range_query(
        lte: Optional[SerializableAwareDatetime], gte: Optional[SerializableAwareDatetime]
    ) -> RangeQuery:
        return RangeQuery(field_name=f"{DocumentLabels.TIMESTAMP}", lte=lte, gte=gte)

    @staticmethod
    def organization_values_query(organizations: List[Organization]):
        field_name = f"{DocumentLabels.METADATA}.{MetadataFields.ORGANIZATION}"
        values = [organization.value for organization in organizations]
        return ValuesQuery(field_name=field_name, values=values)

    @staticmethod
    def tag_values_query(tags: List[str]):
        return ValuesQuery(field_name=DocumentLabels.TAGS, values=tags)
