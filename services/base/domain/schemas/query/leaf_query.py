from enum import StrEnum
from typing import Literal, Optional, Sequence

from pydantic import Field, model_validator

from services.base.domain.annotated_types import NonEmptyStr, RoundedFloat, SerializableAwareDatetime
from services.base.domain.schemas.query.query_operator import QueryOperator
from services.base.domain.schemas.shared import BaseDataModel


class ExistsQuery(BaseDataModel):
    type: Literal["exists"] = Field(default="exists")
    field_name: str = Field(min_length=1)


class ValuesQuery(BaseDataModel):
    type: Literal["values"] = Field(default="values")
    field_name: str = Field(min_length=1)
    values: Sequence[str] = Field(min_length=1)


class RadiusQuery(BaseDataModel):
    type: Literal["radius"] = Field(default="radius")
    field_name: str = Field(min_length=1)
    radius: str = Field(min_length=1, pattern=r"^\d+(m|km|ft|yd|mi)$")
    latitude: RoundedFloat
    longitude: RoundedFloat


class MatchType(StrEnum):
    FUZZY = "fuzzy"
    EXACT = "exact"
    DEFAULT = "default"


class PatternQuery(BaseDataModel):
    type: Literal["pattern"] = Field(default="pattern")
    field_names: Sequence[NonEmptyStr] = Field(min_length=1)
    pattern: NonEmptyStr
    match_type: MatchType = Field(default=MatchType.DEFAULT)
    operator: QueryOperator = Field(default=QueryOperator.OR)


class RangeQuery[T: (int, float, SerializableAwareDatetime)](BaseDataModel):
    type: Literal["range"] = Field(default="range")
    field_name: NonEmptyStr
    gte: Optional[T] = Field(default=None)
    lte: Optional[T] = Field(default=None)

    @model_validator(mode="after")
    def validate_range_values(self):
        if self.gte is None and self.lte is None:
            raise ValueError("lte or gte has to be set")
        if self.gte is not None and self.lte is not None:
            if self.gte > self.lte:
                raise ValueError("gte has to be less than lte")
        return self


LeafQuery = ValuesQuery | PatternQuery | RangeQuery | RadiusQuery | ExistsQuery
