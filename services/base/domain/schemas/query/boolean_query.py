from __future__ import annotations

from typing import Literal, Sequence

from pydantic import Field

from services.base.domain.schemas.query.leaf_query import LeafQuery
from services.base.domain.schemas.query.query_operator import QueryOperator
from services.base.domain.schemas.shared import BaseDataModel


class AndQuery(BaseDataModel):
    type: Literal["and"] = Field(default="and")
    queries: Sequence[LeafQuery | BooleanQuery] = Field(min_length=1)


class OrQuery(BaseDataModel):
    type: Literal["or"] = Field(default="or")
    queries: Sequence[LeafQuery | BooleanQuery] = Field(min_length=1)


class NotQuery(BaseDataModel):
    type: Literal["not"] = Field(default="not")
    queries: Sequence[LeafQuery | BooleanQuery] = Field(min_length=1)
    operator: QueryOperator = QueryOperator.OR


BooleanQuery = AndQuery | OrQuery | NotQuery
