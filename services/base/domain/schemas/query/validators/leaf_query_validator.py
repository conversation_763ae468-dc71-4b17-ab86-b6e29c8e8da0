from typing import Sequence

from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.leaf_query import (
    Exists<PERSON>uery,
    <PERSON><PERSON>uer<PERSON>,
    <PERSON><PERSON>uery,
    <PERSON>dius<PERSON>uery,
    RangeQuery,
    ValuesQuery,
)
from services.base.domain.schemas.query.validators.field_validator import FieldValidator
from services.base.domain.schemas.query.validators.query_validation_exception import QueryValidationException


class LeafQueryValidator:

    @staticmethod
    def validate(leaf_query: LeafQuery, domain_types: Sequence[type[Document]]):
        for domain_type in domain_types:
            if isinstance(leaf_query, (ValuesQuery, RangeQuery, ExistsQuery, RadiusQuery)):
                FieldValidator.validate_fields(field_names=[leaf_query.field_name], domain_type=domain_type)
            elif isinstance(leaf_query, PatternQuery):
                FieldValidator.validate_fields(field_names=leaf_query.field_names, domain_type=domain_type)
            else:
                raise QueryValidationException(f"Unsupported leaf query type: {type(leaf_query)}")
