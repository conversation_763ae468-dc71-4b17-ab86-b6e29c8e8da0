from dataclasses import dataclass
from typing import List, Optional

from pydantic import Field

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.shopping_labels import (
    ASIN_ISBN,
    CARRIER_NAME,
    CAR<PERSON>ER_TRACKING_NO,
    <PERSON><PERSON><PERSON><PERSON>,
    CURRENCY,
    ITEM_DETAIL,
    ITEM_SUBTOTAL,
    ITEM_SUBTOTAL_TAX,
    ITEM_TOTAL,
    LPPU,
    ORDER_ID,
    ORDER_STATUS,
    ORDERING_CUSTOMER_EMAIL,
    PAY_TYPE,
    PO_LINE_NUMBER,
    PPPU,
    PURCHASE_ORDER_NO,
    QTY,
    RELEASE_DATE,
    SELL<PERSON>,
    SHIP_ADDRESS_CITY,
    SHIP_ADDRESS_NAME,
    SHIP_ADDRESS_STATE,
    SHIP_ADDRESS_STREET1,
    SHIP_ADDRESS_STREET2,
    SHIP_ADDRESS_ZIP,
    SHIP_DATE,
    UNSPSC_CODE,
)
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.shared import BaseDataModel, TimestampModel
from services.base.domain.schemas.shared_v2 import DeprEventModel


@dataclass(frozen=True)
class ShoppingActivityFields:
    CATEGORY = DocumentLabels.CATEGORY
    ASIN_ISBN = ASIN_ISBN
    UNSPSC_CODE = UNSPSC_CODE
    RELEASE_DATE = RELEASE_DATE
    CONDITION = CONDITION
    SELLER = SELLER
    LPPU = LPPU
    PPPU = PPPU
    QTY = QTY
    PAY_TYPE = PAY_TYPE
    PURCHASE_ORDER_NO = PURCHASE_ORDER_NO
    PO_LINE_NUMBER = PO_LINE_NUMBER
    ORDERING_CUSTOMER_EMAIL = ORDERING_CUSTOMER_EMAIL
    SHIP_DATE = SHIP_DATE
    SHIP_ADDRESS_NAME = SHIP_ADDRESS_NAME
    SHIP_ADDRESS_STREET1 = SHIP_ADDRESS_STREET1
    SHIP_ADDRESS_STREET2 = SHIP_ADDRESS_STREET2
    SHIP_ADDRESS_CITY = SHIP_ADDRESS_CITY
    SHIP_ADDRESS_STATE = SHIP_ADDRESS_STATE
    SHIP_ADDRESS_ZIP = SHIP_ADDRESS_ZIP
    ORDER_STATUS = ORDER_STATUS
    CARRIER_NAME = CARRIER_NAME
    CARRIER_TRACKING_NO = CARRIER_TRACKING_NO
    ITEM_SUBTOTAL = ITEM_SUBTOTAL
    ITEM_SUBTOTAL_TAX = ITEM_SUBTOTAL_TAX
    ITEM_TOTAL = ITEM_TOTAL
    CURRENCY = CURRENCY
    ORDER_ID = ORDER_ID
    ITEM_DETAIL = ITEM_DETAIL


class ShoppingActivityItemDetail(BaseDataModel):
    category: Optional[str] = Field(alias=ShoppingActivityFields.CATEGORY, default=None)
    asin_isbn: Optional[str] = Field(alias=ShoppingActivityFields.ASIN_ISBN, default=None)
    unspsc_code: Optional[str] = Field(alias=ShoppingActivityFields.UNSPSC_CODE, default=None)
    release_date: Optional[SerializableAwareDatetime] = Field(alias=ShoppingActivityFields.RELEASE_DATE, default=None)
    condition: Optional[str] = Field(alias=ShoppingActivityFields.CONDITION, default=None)
    seller: Optional[str] = Field(alias=ShoppingActivityFields.SELLER, default=None)
    lppu: Optional[float] = Field(alias=ShoppingActivityFields.LPPU, default=None)
    pppu: Optional[float] = Field(alias=ShoppingActivityFields.PPPU, default=None)
    qty: Optional[int] = Field(alias=ShoppingActivityFields.QTY, default=None)
    pay_type: Optional[str] = Field(alias=ShoppingActivityFields.PAY_TYPE, default=None)
    purchase_order_no: Optional[str] = Field(alias=ShoppingActivityFields.PURCHASE_ORDER_NO, default=None)
    po_line_number: Optional[str] = Field(alias=ShoppingActivityFields.PO_LINE_NUMBER, default=None)
    ordering_customer_email: Optional[str] = Field(alias=ShoppingActivityFields.ORDERING_CUSTOMER_EMAIL, default=None)
    ship_date: Optional[SerializableAwareDatetime] = Field(alias=ShoppingActivityFields.SHIP_DATE, default=None)
    ship_address_name: Optional[str] = Field(alias=ShoppingActivityFields.SHIP_ADDRESS_NAME, default=None)
    ship_address_street_1: Optional[str] = Field(alias=ShoppingActivityFields.SHIP_ADDRESS_STREET1, default=None)
    ship_address_street_2: Optional[str] = Field(alias=ShoppingActivityFields.SHIP_ADDRESS_STREET2, default=None)
    ship_address_city: Optional[str] = Field(alias=ShoppingActivityFields.SHIP_ADDRESS_CITY, default=None)
    ship_address_state: Optional[str] = Field(alias=ShoppingActivityFields.SHIP_ADDRESS_STATE, default=None)
    ship_address_zip: Optional[str] = Field(alias=ShoppingActivityFields.SHIP_ADDRESS_ZIP, default=None)
    order_status: Optional[str] = Field(alias=ShoppingActivityFields.ORDER_STATUS, default=None)
    carrier_name: Optional[str] = Field(alias=ShoppingActivityFields.CARRIER_NAME, default=None)
    carrier_tracking_no: Optional[str] = Field(alias=ShoppingActivityFields.CARRIER_TRACKING_NO, default=None)
    item_subtotal: Optional[float] = Field(alias=ShoppingActivityFields.ITEM_SUBTOTAL, default=None)
    item_subtotal_tax: Optional[float] = Field(alias=ShoppingActivityFields.ITEM_SUBTOTAL_TAX, default=None)
    item_total: Optional[float] = Field(alias=ShoppingActivityFields.ITEM_TOTAL, default=None)
    currency: Optional[str] = Field(alias=ShoppingActivityFields.CURRENCY, default=None)


class ShoppingActivitySchema(TimestampModel):
    order_id: str = Field(alias=ShoppingActivityFields.ORDER_ID)
    item_detail: List[ShoppingActivityItemDetail] = Field(alias=ShoppingActivityFields.ITEM_DETAIL)


class ShoppingActivityIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.ShoppingActivity


class ShoppingActivity(ShoppingActivitySchema, DeprEventModel, ShoppingActivityIdentifier): ...
