from dataclasses import dataclass
from typing import List, Optional

from pydantic import Field, model_validator

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import SECONDS_IN_365_DAYS
from services.base.domain.constants.value_limits import LocationPlaceAddressValueLimit, LocationPlaceNameValueLimit
from services.base.domain.enums.activity_type import ActivityType
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.location_source import LocationSource
from services.base.domain.enums.os_platform import OSPlatform
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.shared import BaseDataModel, CoordinatesModel, TimeIntervalModel
from services.base.domain.schemas.shared_v2 import DeprEventModel


@dataclass(frozen=True)
class PlaceVisitDetailsFields:
    NAME = "name"
    ID = "id"
    ADDRESS = "address"


@dataclass(frozen=True)
class WaypointDetailsFields:
    ALTITUDE = "altitude"
    VELOCITY = "velocity"
    HORIZONTAL_ACCURACY = "hor_acc"
    VERTICAL_ACCURACY = "ver_acc"
    HEADING = "heading"
    SOURCE = "source"
    PLATFORM = "platform_type"


@dataclass(frozen=True)
class LocationFields:
    TIMESTAMP = DocumentLabels.TIMESTAMP
    AVERAGE_COORDINATES = "average_coordinates"
    START_COORDINATES = "start_coordinates"
    END_COORDINATES = "end_coordinates"
    AVERAGE_ALTITUDE = "average_altitude"
    START_ALTITUDE = "start_altitude"
    END_ALTITUDE = "end_altitude"
    PLACE_VISIT_DETAILS = "place_visit_details"
    WAYPOINT_DETAILS = "waypoint_details"
    ACTIVITY_TYPE = "activity_type"
    ACTIVITY_TYPE_PROBABILITY = "activity_type_probability"


class PlaceVisitDetails(BaseDataModel):
    name: str = Field(
        alias=PlaceVisitDetailsFields.NAME,
        min_length=LocationPlaceNameValueLimit.MINIMUM,
        max_length=LocationPlaceNameValueLimit.MAXIMUM,
    )
    address: Optional[str] = Field(
        alias=PlaceVisitDetailsFields.ADDRESS,
        min_length=LocationPlaceAddressValueLimit.MINIMUM,
        max_length=LocationPlaceAddressValueLimit.MAXIMUM,
        default=None,
    )
    id: Optional[str] = Field(alias=PlaceVisitDetailsFields.ID, default=None)
    confidence: Optional[float] = Field(alias=DocumentLabels.CONFIDENCE, default=None)


class WaypointDetails(BaseDataModel):
    coordinates: CoordinatesModel | str = Field(alias=DocumentLabels.COORDINATES)
    altitude: Optional[float] = Field(alias=WaypointDetailsFields.ALTITUDE, default=None)
    velocity: Optional[float] = Field(alias=WaypointDetailsFields.VELOCITY, default=None)
    horizontal_accuracy: Optional[float] = Field(alias=WaypointDetailsFields.HORIZONTAL_ACCURACY, default=None)
    vertical_accuracy: Optional[float] = Field(alias=WaypointDetailsFields.VERTICAL_ACCURACY, default=None)
    heading: Optional[float] = Field(alias=WaypointDetailsFields.HEADING, default=None)
    source: Optional[LocationSource] = Field(alias=WaypointDetailsFields.SOURCE, default=None)
    platform: Optional[OSPlatform] = Field(alias=WaypointDetailsFields.PLATFORM, default=None)
    timestamp: Optional[SerializableAwareDatetime] = Field(alias=DocumentLabels.TIMESTAMP, default=None)
    end_time: Optional[SerializableAwareDatetime] = Field(alias=DocumentLabels.END_TIME, default=None)
    duration: Optional[int] = Field(alias=DocumentLabels.DURATION, default=None, ge=0, le=SECONDS_IN_365_DAYS)

    @model_validator(mode="after")
    def validate_time_interval_range(self):
        if self.timestamp and self.end_time:
            if self.timestamp > self.end_time:
                raise ValueError("timestamp has to be less than end_time")
        return self


class LocationSchema(TimeIntervalModel):
    start_coordinates: CoordinatesModel = Field(alias=LocationFields.START_COORDINATES)
    end_coordinates: Optional[CoordinatesModel] = Field(alias=LocationFields.END_COORDINATES, default=None)
    average_coordinates: Optional[CoordinatesModel] = Field(alias=LocationFields.AVERAGE_COORDINATES, default=None)
    start_altitude: Optional[float] = Field(alias=LocationFields.START_ALTITUDE, default=None)
    place_visit_details: Optional[PlaceVisitDetails] = Field(alias=LocationFields.PLACE_VISIT_DETAILS, default=None)
    waypoint_details: Optional[List[WaypointDetails]] = Field(alias=LocationFields.WAYPOINT_DETAILS, default=None)
    end_altitude: Optional[float] = Field(alias=LocationFields.END_ALTITUDE, default=None)
    average_altitude: Optional[float] = Field(alias=LocationFields.AVERAGE_ALTITUDE, default=None)
    distance: Optional[float] = Field(alias=DocumentLabels.DISTANCE, ge=0, default=None)
    activity_type: Optional[ActivityType] = Field(alias=LocationFields.ACTIVITY_TYPE, default=None)
    activity_type_probability: Optional[float] = Field(
        alias=LocationFields.ACTIVITY_TYPE_PROBABILITY, ge=0, le=1, default=None
    )


class LocationIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Location


class Location(DeprEventModel, LocationSchema, LocationIdentifier): ...
