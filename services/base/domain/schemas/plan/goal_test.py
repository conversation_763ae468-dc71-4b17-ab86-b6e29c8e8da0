import pytest

from services.base.domain.schemas.plan.goal import Goal


class TestGoal:
    @pytest.mark.parametrize(
        "gte, lte, current_value, expected",
        [
            (None, 10, 0, True),
            (None, 10, 1, True),
            (None, 10, 10, True),
            (None, 10, 11, False),
            (10, None, 0, False),
            (10, None, 1, False),
            (10, None, 10, True),
            (10, None, 11, True),
            (10, 20, 0, False),
            (10, 20, 10, True),
            (10, 20, 11, True),
            (10, 20, 20, True),
            (10, 20, 21, False),
        ],
    )
    def test_is_achieved(self, gte, lte, current_value, expected):
        assert Goal._is_achieved(gte=gte, lte=lte, current_value=current_value) == expected
