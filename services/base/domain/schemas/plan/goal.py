from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Literal

from pydantic import model_validator
from pydantic.fields import Field, computed_field

from services.base.domain.annotated_types import NonEmptyStr, RoundedFloat
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.plan.plan_base import PlanBase, PlanBaseFields
from services.base.domain.schemas.query.aggregations import SimpleAggregationMethod
from services.base.domain.schemas.shared import BaseDataModel


@dataclass(frozen=True)
class GoalFields(PlanBaseFields):
    CURRENT_VALUE = "current_value"
    CONDITION = "condition"
    IS_ACHIEVED = "is_achieved"
    TYPE = DocumentLabels.TYPE


@dataclass(frozen=True)
class GoalConditionFields:
    GTE = "gte"
    LTE = "lte"
    FIELD_NAME = "field_name"
    AGG_METHOD = "agg_method"
    QUERY = "query"


class GoalIdentifier(TypeIdentifier):
    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Goal


class GoalCondition(BaseDataModel):
    gte: RoundedFloat | None = Field(alias=GoalConditionFields.GTE, ge=0)
    lte: RoundedFloat | None = Field(alias=GoalConditionFields.LTE, ge=0)
    field_name: NonEmptyStr = Field(alias=GoalConditionFields.FIELD_NAME)
    agg_method: SimpleAggregationMethod = Field(alias=GoalConditionFields.AGG_METHOD)
    query: NonEmptyStr = Field(alias=GoalConditionFields.QUERY)

    @model_validator(mode="after")
    def validate_gte_lte(self):
        if self.gte is None and self.lte is None:
            raise ValueError("lte or gte has to be set")
        if self.gte is not None and self.lte is not None:
            if self.gte > self.lte:
                raise ValueError("gte has to be less than lte")
        return self


class Goal(PlanBase, GoalIdentifier):
    type: Literal[DataType.Goal] = Field(alias=GoalFields.TYPE)
    condition: GoalCondition = Field(alias=GoalFields.CONDITION)
    current_value: float = Field(alias=GoalFields.CURRENT_VALUE, ge=0)

    @staticmethod
    def _is_achieved(gte: float | None, lte: float | None, current_value: float) -> bool:
        if gte is not None and lte is not None:
            return gte <= current_value <= lte
        if gte is not None:
            return current_value >= gte
        if lte is not None:
            return current_value <= lte
        return False

    @computed_field()
    def is_achieved(self) -> bool:
        return self._is_achieved(
            gte=self.condition.gte,
            lte=self.condition.lte,
            current_value=self.current_value,
        )

    @computed_field
    def total_cycles(self) -> int:
        if not self.recurrence:
            return 1
        return self.calculate_total_plan_cycles(
            recurrence=self.recurrence,
            first_completed_at=None,
            now=datetime.now(timezone.utc),
        )
