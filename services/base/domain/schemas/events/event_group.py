from dataclasses import dataclass
from enum import StrEnum, auto
from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import UniqueSequenceUUID
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class EventGroupFields(EventFields):
    CHILD_IDS = "child_ids"


class EventGroupCategory(StrEnum):
    EVENT_GROUP = auto()
    MEAL = auto()
    WORKOUT = auto()


class EventGroupIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.EventGroup


class EventGroup(Event, EventGroupIdentifier):
    category: EventGroupCategory = Field(alias=EventGroupFields.CATEGORY)
    type: Literal[DataType.EventGroup] = Field(alias=EventGroupFields.TYPE)
    child_ids: UniqueSequenceUUID = Field(alias=EventGroupFields.CHILD_IDS)
