from dataclasses import dataclass
from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import RoundedFloat
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class BloodGlucoseValueLimits:
    MINIMUM_VALUE = 0
    MAXIMUM_VALUE = 4000


@dataclass(frozen=True)
class BloodGlucoseFields(EventFields):
    VALUE = "value"
    SPECIMEN_SOURCE = "specimen_source"


class BloodGlucoseCategory(StrEnum):
    BLOOD_GLUCOSE = "blood_glucose"


class BloodGlucoseSpecimenSource(StrEnum):
    INTERSTITIAL_FLUID = "interstitial_fluid"
    CAPILLARY_BLOOD = "capillary_blood"
    PLASMA = "plasma"
    SERUM = "serum"
    TEARS = "tears"
    WHOLE_BLOOD = "whole_blood"
    UNKNOWN = "unknown"


class BloodGlucoseIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.BloodGlucose


class BloodGlucose(Event, BloodGlucoseIdentifier):
    type: Literal[DataType.BloodGlucose] = Field(alias=BloodGlucoseFields.TYPE)
    category: BloodGlucoseCategory = Field(
        alias=BloodGlucoseFields.CATEGORY, default=BloodGlucoseCategory.BLOOD_GLUCOSE
    )
    value: RoundedFloat = Field(
        alias=BloodGlucoseFields.VALUE,
        ge=BloodGlucoseValueLimits.MINIMUM_VALUE,
        le=BloodGlucoseValueLimits.MAXIMUM_VALUE,
    )
    specimen_source: BloodGlucoseSpecimenSource = Field(
        alias=BloodGlucoseFields.SPECIMEN_SOURCE, default=BloodGlucoseSpecimenSource.UNKNOWN
    )
