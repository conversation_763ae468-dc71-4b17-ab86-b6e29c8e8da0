from dataclasses import dataclass
from typing import Literal

from pydantic.fields import Field

from services.base.domain.annotated_types import (
    NonEmptyStr,
    SerializableAwareDatetime,
)
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import (
    Document,
    RBACDocument,
    SystemPropertiesDocument,
    TagsDocument,
)
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass
class UseCaseFields:
    ID = DocumentLabels.ID
    TYPE = DocumentLabels.TYPE
    NAME = "name"
    ARCHIVED_AT = "archived_at"
    RBAC = DocumentLabels.RBAC


class UseCaseIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.UseCase


class UseCase(Document, UseCaseIdentifier, SystemPropertiesDocument, RBACDocument, TagsDocument):
    type: Literal[DataType.UseCase] = Field(alias=UseCaseFields.TYPE)
    name: NonEmptyStr = Field(alias=UseCaseFields.NAME)
    archived_at: SerializableAwareDatetime | None = Field(alias=UseCaseFields.ARCHIVED_AT)
