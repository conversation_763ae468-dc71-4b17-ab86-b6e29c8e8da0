from dataclasses import dataclass
from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import RoundedFloat
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class StrengthFields(EventFields):
    COUNT = "count"
    WEIGHT = "weight"
    RATING = "rating"


class StrengthCategory(StrEnum):
    BODYWEIGHT = "bodyweight"
    FREE_WEIGHTS = "free_weights"
    RESISTANCE_BANDS = "resistance_bands"
    SUSPENSION_TRAINING = "suspension_training"
    STRENGTH = "strength"


class StrengthIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Strength


class Strength(Event, StrengthIdentifier):
    type: Literal[DataType.Strength] = Field(alias=StrengthFields.TYPE)
    category: StrengthCategory = Field(alias=EventFields.CATEGORY)
    count: int = Field(alias=StrengthFields.COUNT, ge=0, le=1000)
    weight: RoundedFloat | None = Field(alias=StrengthFields.WEIGHT, ge=0, le=500)
    rating: int | None = Field(
        alias=StrengthFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
