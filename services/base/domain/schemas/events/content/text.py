from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.content_collection import ContentCollection, ContentFields
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


class TextCategory(StrEnum):
    TEXT = DataType.Text
    BOOK = "book"
    BLOG = "blog"
    ARTICLE = "article"


class TextIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Text


class Text(ContentCollection, Event, TextIdentifier):
    type: Literal[DataType.Text] = Field(alias=ContentFields.TYPE)
    category: TextCategory = Field(alias=ContentFields.CATEGORY, default=TextCategory.TEXT)
