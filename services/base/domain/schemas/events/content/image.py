from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.content_collection import ContentCollection, ContentFields
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


class ImageCategory(StrEnum):
    IMAGE = DataType.Image


class ImageIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Image


class Image(ContentCollection, Event, ImageIdentifier):
    type: Literal[DataType.Image] = Field(alias=ContentFields.TYPE)
    category: ImageCategory = Field(alias=ContentFields.CATEGORY, default=ImageCategory.IMAGE)
