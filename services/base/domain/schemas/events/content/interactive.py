from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.content_collection import ContentCollection, ContentFields
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


class InteractiveCategory(StrEnum):
    INTERACTIVE = DataType.Interactive
    GAME = "game"
    VIRTUAL_REALITY = "virtual_reality"
    AUGMENTED_REALITY = "augmented_reality"
    BOARD_GAME = "board_game"
    CARD_GAME = "card_game"
    PUZZLE = "puzzle"
    QUIZ = "quiz"
    APP = "app"


class InteractiveIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Interactive


class Interactive(ContentCollection, Event, InteractiveIdentifier):
    type: Literal[DataType.Interactive] = Field(alias=ContentFields.TYPE)
    category: InteractiveCategory = Field(alias=ContentFields.CATEGORY, default=InteractiveCategory.INTERACTIVE)
