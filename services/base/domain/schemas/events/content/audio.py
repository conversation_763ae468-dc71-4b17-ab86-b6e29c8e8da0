from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.content_collection import ContentCollection, ContentFields
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


class AudioCategory(StrEnum):
    AUDIO = DataType.Audio
    PODCAST = "podcast"
    AUDIOBOOK = "audiobook"
    MUSIC = "music"


class AudioIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Audio


class Audio(ContentCollection, Event, AudioIdentifier):
    type: Literal[DataType.Audio] = Field(alias=ContentFields.TYPE)
    category: AudioCategory = Field(alias=ContentFields.CATEGORY, default=AudioCategory.AUDIO)
