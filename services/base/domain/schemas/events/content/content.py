from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.content.content_collection import ContentCollection, ContentFields
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


class ContentCategory(StrEnum):
    CONTENT = DataType.Content
    SPORT = "sport"
    SOCIAL_MEDIA = "social_media"
    NEWS = "news"


class ContentIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Content


class Content(ContentCollection, Event, ContentIdentifier):
    type: Literal[DataType.Content] = Field(alias=ContentFields.TYPE)
    category: ContentCategory = Field(alias=ContentFields.CATEGORY, default=ContentCategory.CONTENT)
