from dataclasses import dataclass
from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class NoteFields(EventFields): ...


class NoteCategory(StrEnum):
    NOTE = DataType.Note
    JOURNAL = "journal"
    GRATITUDE = "gratitude"


class NoteIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Note


class Note(Event, NoteIdentifier):
    type: Literal[DataType.Note] = Field(alias=NoteFields.TYPE)
    category: NoteCategory = Field(alias=NoteFields.CATEGORY, default=NoteCategory.NOTE)
