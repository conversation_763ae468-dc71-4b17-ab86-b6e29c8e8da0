from dataclasses import dataclass
from enum import StrEnum
from typing import Literal

from pydantic import Field

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event, EventFields, EventValueLimits
from services.base.domain.schemas.events.type_identifier import TypeIdentifier


@dataclass(frozen=True)
class EmotionFields(EventFields):
    RATING = "rating"


class EmotionCategory(StrEnum):
    EMOTION = DataType.Emotion
    JOY = "joy"
    SAD = "sad"
    FEAR = "fear"
    DISGUST = "disgust"
    ANGER = "anger"
    SURPRISE = "surprise"
    ANTICIPATION = "anticipation"
    TRUST = "trust"
    ANXIETY = "anxiety"
    MOOD = "mood"


class EmotionIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Emotion


class Emotion(Event, EmotionIdentifier):
    type: Literal[DataType.Emotion] = Field(alias=EmotionFields.TYPE)
    category: EmotionCategory = Field(alias=EmotionFields.CATEGORY, default=EmotionCategory.EMOTION)
    rating: int = Field(
        alias=EmotionFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
