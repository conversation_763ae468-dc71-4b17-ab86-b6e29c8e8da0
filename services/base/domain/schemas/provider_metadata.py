from dataclasses import dataclass
from typing import Optional

from pydantic import Field

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.acquisition import AcquisitionType
from services.base.domain.enums.application import Application
from services.base.domain.enums.provider import Provider
from services.base.domain.schemas.shared import BaseDataModel


@dataclass(frozen=True)
class ProviderFields:
    ENTITY = DocumentLabels.ENTITY
    DATA_QUALITY = "data_quality"
    DATA_INTEGRITY = "data_integrity"
    DEVICE = "device"
    PRODUCT = "product"
    APPLICATION = "application"
    ACQUISITION_METHOD = "acquisition_method"


class ProviderMetadata(BaseDataModel):
    entity: Provider = Field(alias=ProviderFields.ENTITY)
    product: str = Field(alias=ProviderFields.PRODUCT)
    acquisition_method: AcquisitionType = Field(alias=ProviderFields.ACQUISITION_METHOD)
    data_integrity: float = Field(alias=ProviderFields.DATA_INTEGRITY, ge=0)
    data_quality: Optional[float] = Field(alias=ProviderFields.DATA_QUALITY, default=None, ge=0)
    device: Optional[str] = Field(alias=ProviderFields.DEVICE, default=None)
    application: Optional[Application] = Field(alias=ProviderFields.APPLICATION, default=None)
