from dataclasses import dataclass

from pydantic import Field

from services.base.domain.annotated_types import HTML, NonEmptyStr
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.messages import InboxMessageStatus
from services.base.domain.schemas.events.document_base import Document, SystemPropertiesDocument
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.identity import Identity
from services.base.domain.schemas.inbox.context_models import MessageContext
from services.base.domain.schemas.shared import TimestampModel


@dataclass(frozen=True)
class InboxMessageFields:
    DESTINATION = "destination"
    SENDER = "sender"
    STATUS = "status"
    TITLE = "title"
    MESSAGE = "message"
    CONTEXT = "context"
    IS_URGENT = "is_urgent"


class InboxMessageIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.InboxMessage


class InboxMessage(Document, TimestampModel, SystemPropertiesDocument, InboxMessageIdentifier):
    destination: Identity = Field(alias=InboxMessageFields.DESTINATION)
    sender: Identity = Field(alias=InboxMessageFields.SENDER)
    status: InboxMessageStatus = Field(alias=InboxMessageFields.STATUS, default=InboxMessageStatus.UNREAD)
    title: NonEmptyStr = Field(alias=InboxMessageFields.TITLE)
    message: HTML = Field(alias=InboxMessageFields.MESSAGE)
    is_urgent: bool = Field(alias=InboxMessageFields.IS_URGENT)
    context: MessageContext = Field(alias=InboxMessageFields.CONTEXT, union_mode="left_to_right")
