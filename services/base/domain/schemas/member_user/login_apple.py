import json
from typing import Optional

from pydantic import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, field_validator

from services.base.domain.schemas.shared import BaseDataModel


class LoginApple(BaseDataModel):
    user_uuid: UUID4
    apple_id: str
    user_data: Optional[Json] = None

    @field_validator("user_data", mode="before")
    def validate_user_data(cls, user_data):
        return json.dumps(user_data) if not isinstance(user_data, str) else user_data
