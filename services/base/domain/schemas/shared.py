from __future__ import annotations

from typing import Optional, Sequence
from uuid import UUID, uuid4

from pydantic import BaseModel as PydanticBaseModel
from pydantic import ConfigDict, Field, field_validator, model_validator

from services.base.domain.annotated_types import (
    AssetId,
    SerializableAwareDatetime,
)
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import SECONDS_IN_365_DAYS
from services.base.domain.constants.value_limits import CoordinatesValueLimit
from services.base.domain.enums.assets_enums import AssetType


class BaseDataModel(PydanticBaseModel):
    model_config = ConfigDict(populate_by_name=True, from_attributes=True)

    @classmethod
    def map[T: BaseDataModel](cls: type[T], model: BaseDataModel, fields: dict | None = None) -> T:
        return cls(**model.model_dump(by_alias=True) | fields) if fields else cls.model_validate(obj=model)

    @classmethod
    def multi_map[T: BaseDataModel](cls: type[T], models: Sequence[BaseDataModel]) -> Sequence[T]:
        return [cls.map(model=model) for model in models]


class IdentifiableModel(BaseDataModel):
    doc_id: UUID = Field(alias=DocumentLabels.DOC_ID, default_factory=uuid4)


class TimestampModel(BaseDataModel):
    timestamp: SerializableAwareDatetime = Field(alias=DocumentLabels.TIMESTAMP)


class TimeIntervalModel(TimestampModel):
    end_time: Optional[SerializableAwareDatetime] = Field(alias=DocumentLabels.END_TIME, default=None)
    duration: Optional[int] = Field(alias=DocumentLabels.DURATION, default=None, ge=0, le=SECONDS_IN_365_DAYS)

    @model_validator(mode="after")
    def validate_time_interval_range(self):
        if self.end_time:
            if self.timestamp > self.end_time:
                raise ValueError("timestamp has to be less than end_time")
        return self


class CoordinatesModel(BaseDataModel):
    latitude: float = Field(
        alias=DocumentLabels.LATITUDE,
        ge=CoordinatesValueLimit.LAT_MIN,
        le=CoordinatesValueLimit.LAT_MAX,
    )
    longitude: float = Field(
        alias=DocumentLabels.LONGITUDE,
        ge=CoordinatesValueLimit.LON_MIN,
        le=CoordinatesValueLimit.LON_MAX,
    )

    @field_validator("latitude", "longitude")
    @classmethod
    def result_check(cls, value):
        return round(value, 7)


class AssetReferenceModel(BaseDataModel):
    asset_type: AssetType
    asset_id: AssetId
