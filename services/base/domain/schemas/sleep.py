from dataclasses import dataclass
from typing import List, Optional, Sequence

from pydantic import Field, field_validator, model_validator

from services.base.domain.annotated_types import SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import SECONDS_IN_DAY
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.sleep_stages import SleepStage
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.shared import BaseDataModel, TimeIntervalModel, TimestampModel
from services.base.domain.schemas.shared_v2 import DeprEventModel


@dataclass(frozen=True)
class SleepDetailFields:
    SLEEP_DETAIL = "sleep_detail"
    STAGE = "stage"


@dataclass(frozen=True)
class SleepSummaryFields:
    FIELD_NAME = "sleep_summary"
    EFFICIENCY = "efficiency"
    IS_MAIN_SLEEP = "is_main_sleep"
    EVENTS_COUNT = "events_count"
    FALL_ASLEEP_SECONDS = "fall_asleep_seconds"
    AFTER_WAKEUP_SECONDS = "after_wakeup_seconds"
    AWAKE_SECONDS = "awake_seconds"
    ASLEEP_SECONDS = "asleep_seconds"
    IN_BED_SECONDS = "in_bed_seconds"
    DEEP_SECONDS = "deep_seconds"
    LIGHT_SECONDS = "light_seconds"
    REM_SECONDS = "rem_seconds"
    RESTLESS_SECONDS = "restless_seconds"


@dataclass(frozen=True)
class SleepFields:
    SLEEP_EVENTS = "sleep_events"
    SLEEP_DETAIL = "sleep_detail"
    SLEEP_SUMMARY = "sleep_summary"


class SleepDetail(TimeIntervalModel):
    stage: SleepStage = Field(alias=SleepDetailFields.STAGE)


class SleepSummary(BaseDataModel):
    efficiency: Optional[float] = Field(alias=SleepSummaryFields.EFFICIENCY, default=None, ge=0)
    is_main_sleep: Optional[bool] = Field(alias=SleepSummaryFields.IS_MAIN_SLEEP, default=None)
    events_count: Optional[int] = Field(alias=SleepSummaryFields.EVENTS_COUNT, default=None, ge=0)
    fall_asleep_seconds: Optional[int] = Field(alias=SleepSummaryFields.FALL_ASLEEP_SECONDS, default=None, ge=0)
    after_wakeup_seconds: Optional[int] = Field(alias=SleepSummaryFields.AFTER_WAKEUP_SECONDS, default=None, ge=0)
    awake_seconds: Optional[int] = Field(alias=SleepSummaryFields.AWAKE_SECONDS, default=None, ge=0)
    asleep_seconds: Optional[int] = Field(alias=SleepSummaryFields.ASLEEP_SECONDS, default=None, ge=0)
    # awake_seconds + asleep_seconds:
    in_bed_seconds: Optional[int] = Field(alias=SleepSummaryFields.IN_BED_SECONDS, default=None, ge=0)
    deep_seconds: Optional[int] = Field(alias=SleepSummaryFields.DEEP_SECONDS, default=None, ge=0)
    light_seconds: Optional[int] = Field(alias=SleepSummaryFields.LIGHT_SECONDS, default=None, ge=0)
    rem_seconds: Optional[int] = Field(alias=SleepSummaryFields.REM_SECONDS, default=None, ge=0)
    restless_seconds: Optional[int] = Field(alias=SleepSummaryFields.RESTLESS_SECONDS, default=None, ge=0)

    @field_validator("efficiency")
    @classmethod
    def round_check(cls, value):  # pylint: disable=no-self-argument
        return round(value, 2) if value is not None else None


class SleepEvent(TimestampModel):
    sleep_detail: Optional[Sequence[SleepDetail]] = Field(alias=SleepFields.SLEEP_DETAIL, default=None)
    sleep_summary: SleepSummary = Field(alias=SleepFields.SLEEP_SUMMARY)
    end_time: SerializableAwareDatetime = Field(alias=DocumentLabels.END_TIME)
    duration: Optional[int] = Field(alias=DocumentLabels.DURATION, ge=0, le=SECONDS_IN_DAY, default=None)

    @model_validator(mode="after")
    def validate_time_interval_range(self):
        if self.timestamp > self.end_time:
            raise ValueError("timestamp has to be less than end_time")
        return self


class SleepSchema(TimestampModel):
    sleep_events: List[SleepEvent] = Field(alias=SleepFields.SLEEP_EVENTS)
    end_time: SerializableAwareDatetime = Field(alias=DocumentLabels.END_TIME)
    duration: Optional[int] = Field(alias=DocumentLabels.DURATION, ge=0, le=SECONDS_IN_DAY, default=None)

    @model_validator(mode="after")
    def validate_time_interval_range(self):
        if self.timestamp > self.end_time:
            raise ValueError("timestamp has to be less than end_time")
        return self


class SleepIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.Sleep


class Sleep(SleepSchema, DeprEventModel, SleepIdentifier): ...
