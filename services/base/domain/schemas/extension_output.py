from dataclasses import dataclass
from typing import Literal, Optional
from uuid import UUID

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, SerializableAwareDatetime
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.extension_labels.extension_labels import ExtensionLabels
from services.base.domain.enums.analytics.extension_output import ExtensionStatus
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import Document, SystemPropertiesDocument
from services.base.domain.schemas.events.type_identifier import TypeIdentifier
from services.base.domain.schemas.shared import BaseDataModel, TimestampModel


@dataclass(frozen=True)
class AnalyticMetadataTagsFields:
    TAG = DocumentLabels.TAG


@dataclass(frozen=True)
class AnalyticMetadataFields:
    USER_UUID = DocumentLabels.USER_UUID
    EXTENSION_ID = ExtensionLabels.EXTENSION_ID
    PROVIDER_ID = ExtensionLabels.PROVIDER_ID
    IMPORTANT = DocumentLabels.IMPORTANT
    URGENT = DocumentLabels.URGENT
    FAVORITED_AT = DocumentLabels.FAVORITED_AT


@dataclass(frozen=True)
class AnalyticResultFields:
    TYPE = ExtensionLabels.TYPE
    EXTENSION_INPUT = ExtensionLabels.EXTENSION_INPUT
    TIMESTAMP = DocumentLabels.TIMESTAMP
    METADATA = DocumentLabels.METADATA
    MESSAGE = ExtensionLabels.MESSAGE
    SUMMARY = ExtensionLabels.SUMMARY
    OUTPUT = ExtensionLabels.OUTPUT
    RESULT_STATUS = ExtensionLabels.RESULT_STATUS
    EXTENSION_STATUS = ExtensionLabels.EXTENSION_STATUS
    RUN_SUMMARY = ExtensionLabels.RUN_SUMMARY


class AnalyticMetadata(BaseDataModel):
    user_uuid: UUID = Field(alias=DocumentLabels.USER_UUID)
    extension_id: UUID = Field(alias=ExtensionLabels.EXTENSION_ID)
    provider_id: UUID = Field(alias=ExtensionLabels.PROVIDER_ID)
    important: bool = Field(alias=AnalyticMetadataFields.IMPORTANT, default=False)
    urgent: bool = Field(alias=AnalyticMetadataFields.URGENT, default=False)
    favorited_at: Optional[SerializableAwareDatetime] = Field(alias=AnalyticMetadataFields.FAVORITED_AT, default=None)


class ExtensionRunIdentifier(TypeIdentifier):
    @classmethod
    def type_id(cls) -> DataType:
        return DataType.ExtensionRun


class ExtensionRun(Document, TimestampModel, ExtensionRunIdentifier, SystemPropertiesDocument):
    type: Literal[DataType.ExtensionRun] = Field(
        alias=ExtensionLabels.TYPE,
        description="Type of the extension's run, used as a parent type relationship join field.",
    )

    result_status: bool = Field(
        alias=AnalyticResultFields.RESULT_STATUS,
        description="Boolean that indicates if the extension or output has relevant results",
    )

    run_summary: Optional[NonEmptyStr] = Field(
        default=None,
        alias=AnalyticResultFields.RUN_SUMMARY,
        description="Field used to store summary data of the run as a JSON string of a Pydantic model",
    )
    extension_status: ExtensionStatus = Field(
        alias=AnalyticResultFields.EXTENSION_STATUS, description="The current status of the extension's run."
    )
    extension_input: dict = Field(alias=ExtensionLabels.EXTENSION_INPUT)
    message: NonEmptyStr = Field(
        alias=AnalyticResultFields.MESSAGE, description="Message that appears in the message inbox"
    )
    summary: NonEmptyStr = Field(alias=AnalyticResultFields.SUMMARY, description="Notification body text")
    metadata: AnalyticMetadata = Field(alias=DocumentLabels.METADATA)


class ExtensionResultIdentifier(TypeIdentifier):

    @classmethod
    def type_id(cls) -> DataType:
        return DataType.ExtensionResult


class ExtensionResult(Document, TimestampModel, SystemPropertiesDocument, ExtensionResultIdentifier):
    type: Literal[DataType.ExtensionResult] = Field(
        alias=ExtensionLabels.TYPE,
        description="Type of the extension's run, used as a parent type relationship join field.",
    )

    result_status: bool = Field(
        alias=AnalyticResultFields.RESULT_STATUS,
        description="Boolean that indicates if the extension or output has relevant results",
    )
    output: NonEmptyStr = Field(
        alias=AnalyticResultFields.OUTPUT,
        description="The output of the extension in string format. When retrieved, it will be mapped back into the "
        "output model.",
    )
    metadata: AnalyticMetadata = Field(alias=DocumentLabels.METADATA)
