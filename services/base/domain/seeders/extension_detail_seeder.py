from typing import Sequence

from services.base.domain.schemas.extensions.extension_detail import ExtensionDetail
from services.base.domain.seeders.seeder_base import SeederBase
from settings.extension_constants import (
    LLIF_EXTENSION_PROVIDER_UUID,
    SINGLE_CORRELATION_EVALUATOR_EXTENSION_DESCRIPTION,
    SINGLE_CORRELATION_EVALUATOR_EXTENSION_DOMAIN,
    SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID,
    SINGLE_CORRELATION_EVALUATOR_EXTENSION_NAME,
    TREND_INSIGHTS_EXTENSION_DESCRIPTION,
    TREND_INSIGHTS_EXTENSION_DOMAIN,
    TREND_INSIGHTS_EXTENSION_ID,
    TREND_INSIGHTS_EXTENSION_NAME,
)


class ExtensionDetailSeeder(SeederBase):
    @staticmethod
    def seed_data() -> Sequence[ExtensionDetail]:
        return [
            ExtensionDetail(
                extension_id=TREND_INSIGHTS_EXTENSION_ID,
                provider_id=LLIF_EXTENSION_PROVIDER_UUID,
                description=TREND_INSIGHTS_EXTENSION_DESCRIPTION,
                name=TREND_INSIGHTS_EXTENSION_NAME,
                domain=TREND_INSIGHTS_EXTENSION_DOMAIN,
                version="1.0.0",
                tags=["health", "statistics", "trends", "analytic"],
            ),
            ExtensionDetail(
                extension_id=SINGLE_CORRELATION_EVALUATOR_EXTENSION_ID,
                provider_id=LLIF_EXTENSION_PROVIDER_UUID,
                description=SINGLE_CORRELATION_EVALUATOR_EXTENSION_DESCRIPTION,
                name=SINGLE_CORRELATION_EVALUATOR_EXTENSION_NAME,
                domain=SINGLE_CORRELATION_EVALUATOR_EXTENSION_DOMAIN,
                version="1.0.0",
                tags=["health", "statistics", "correlations", "analytic"],
            ),
        ]
