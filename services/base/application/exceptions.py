class DefaultException(Exception):
    """Default exception class for internal errors"""

    def __init__(self, message: str):
        super().__init__(message)
        self.message = message


class NoContentException(DefaultException):
    pass


class RuntimeException(DefaultException):
    pass


class BadRequestException(DefaultException):
    pass


class IncorrectOperationException(DefaultException):
    pass


class DuplicateDocumentsFound(DefaultException):
    pass


class InternalServerErrorException(DefaultException):
    pass


class ConflictingActionInProgress(BadRequestException):
    pass


class NoUploadRunning(BadRequestException):
    pass


class RetryLater(DefaultException):
    def __init__(self, message: str, retry_later: int):
        self.retry_later = retry_later
        super().__init__(message)


class InvalidPrivilegesException(DefaultException):
    def __init__(self, message: str = "Invalid privileges for taking this action."):
        super().__init__(message)


class ActionCancelledException(DefaultException):
    pass


class InvalidDatabaseRequestException(DefaultException):
    pass


class RetryException(DefaultException):
    pass


class ReFetchException(DefaultException):
    pass


class PartialDataException(DefaultException):
    pass
