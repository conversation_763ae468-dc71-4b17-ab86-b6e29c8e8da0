import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional
from uuid import UUID

from services.base.application.event_models.location_loading_finished import LocationLoadingFinishedEventModel
from services.base.application.message_broker_client import MessageBrokerClient
from services.base.domain.constants.messaging import ATT_NAME_LOADING_EVENT, MessageTopics
from services.base.domain.enums.location_source import LocationSource
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.shared import BaseDataModel
from services.base.message_queue.utils import create_string_message_attribute


class LocationStatisticsResponse(BaseDataModel):
    latitude_sum: float = 0
    longitude_sum: float = 0
    altitude_sum: float = 0
    latitude_count: int = 0
    longitude_count: int = 0
    altitude_count: int = 0
    longitude_max: float = 0
    longitude_min: float = 0
    latitude_max: float = 0
    latitude_min: float = 0


class LocationLoaderUtils:
    DAYS_TO_BACK_FILL = 90

    @staticmethod
    def should_details_be_included(statistics: LocationStatisticsResponse) -> bool:
        # TODO: sane threshold
        if (statistics.latitude_max - statistics.latitude_min) >= 0.1 or (
            statistics.longitude_max - statistics.longitude_min
        ) >= 0.1:
            return True
        return False

    @staticmethod
    def calculate_statistics(
        location_response: LocationStatisticsResponse, latitude: float, longitude: float, altitude: Optional[float]
    ) -> LocationStatisticsResponse:
        location_response.latitude_sum += latitude
        location_response.latitude_count += 1
        location_response.longitude_sum += longitude
        location_response.longitude_count += 1
        if altitude:
            location_response.altitude_sum += altitude
            location_response.altitude_count += 1

        if latitude < location_response.latitude_min or not location_response.latitude_min:
            location_response.latitude_min = latitude
        if latitude > location_response.latitude_max or not location_response.latitude_max:
            location_response.latitude_max = latitude

        if longitude < location_response.longitude_min or not location_response.longitude_min:
            location_response.longitude_min = longitude
        if longitude > location_response.longitude_max or not location_response.longitude_max:
            location_response.longitude_max = longitude

        return location_response

    @staticmethod
    def get_location_source(
        location_source: str, aliases: Optional[Dict[str, LocationSource]] = None
    ) -> LocationSource:
        """Maps given key to LocationSource enumeration. Falls back to unknown if the key is not recognized."""
        try:
            return LocationSource(location_source.lower())
        except ValueError as e:
            # it may be alias => check for alias first before failing&fallback
            if location_source and aliases and location_source in aliases:
                return aliases[location_source]
            logging.info("Trying to use unsupported LocationSource => falling back to UNKNOWN: %s", repr(e))
            return LocationSource.UNKNOWN

    @staticmethod
    def notify_location_loaded(
        user_uuid: UUID,
        entries: List[Location],
        message_broker_client: MessageBrokerClient,
    ):
        dt_limit = datetime.now(timezone.utc) - timedelta(days=LocationLoaderUtils.DAYS_TO_BACK_FILL)

        for entry in entries:
            if not entry.timestamp >= dt_limit:
                continue

            message_broker_client.publish_topic(
                topic_name=MessageTopics.TOPIC_LOCATION_LOADING_FINISHED.value,
                message_body=LocationLoadingFinishedEventModel(
                    user_uuid=user_uuid,
                    time_from=entry.timestamp,
                    lat=entry.start_coordinates.latitude,
                    long=entry.start_coordinates.longitude,
                    time_to=entry.end_time,
                    type="location",
                ).model_dump_json(),
                message_attributes=create_string_message_attribute(
                    ATT_NAME_LOADING_EVENT, MessageTopics.TOPIC_LOCATION_LOADING_FINISHED.value
                ),
            )
