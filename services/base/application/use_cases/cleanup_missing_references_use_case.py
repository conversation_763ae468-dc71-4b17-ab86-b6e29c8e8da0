import logging
from uuid import UUID

from opensearchpy import Async<PERSON>penSearch

from services.base.application.database.aggregation_service import AggregationService
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.domain.repository.plan_repository import PlanRepository
from services.base.domain.repository.template_repository import TemplateRepository
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.plan.plan import Plan
from services.base.domain.schemas.query.leaf_query import ExistsQuery, ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.infrastructure.database.opensearch.opensearch_index_constants import EVENT_PREFIX, PLAN_INDEX
from services.base.infrastructure.database.opensearch.query_methods.insert_or_update import (
    update_by_query_by_index_pattern,
)


class CleanupMissingReferencesUseCase:
    def __init__(
        self,
        client: AsyncOpenSearch,
        search_service: DocumentSearchService,
        agg_service: AggregationService,
        template_repo: TemplateRepository,
        plan_repo: PlanRepository,
    ):
        self._client = client
        self._search_service = search_service
        self._agg_service = agg_service
        self._template_repo = template_repo
        self._plan_repo = plan_repo

    async def execute(self, dry_run: bool):
        count = 0
        count += await self.cleanup_missing_template_references(dry_run=dry_run)
        count += await self.cleanup_missing_plan_references(dry_run=dry_run)
        return count

    async def cleanup_missing_template_references(self, dry_run: bool) -> int:
        async def find_corrupted_template_ids() -> set[UUID]:
            aggs = await self._agg_service.frequency_distribution_by_query(
                query=Query(
                    type_queries=[TypeQuery(domain_types=[Event, Plan], query=ExistsQuery(field_name="template_id"))]
                ),
                field_name="template_id",
                size=1_000,
            )
            unique_template_ids = {UUID(a.aggregation_key) for a in aggs}  # pyright: ignore
            found_templates = await self._template_repo.search_by_id(ids=list(unique_template_ids))
            found_template_ids = {p.id for p in found_templates}

            return unique_template_ids - found_template_ids

        corrupted_template_ids = await find_corrupted_template_ids()
        if corrupted_template_ids:
            q = ValuesQuery(field_name="template_id", values=[str(p) for p in corrupted_template_ids])
            count = await self._search_service.count_by_query(
                query=Query(type_queries=[TypeQuery(domain_types=[Event, Plan], query=q)])
            )
        else:
            count = 0

        logging.info(
            f"Number of entries to be migrated via {self.cleanup_missing_template_references.__name__}: at least {count}"
        )
        if dry_run:
            return count

        while corrupted_template_ids:
            q = {"bool": {"filter": [{"terms": {"template_id": [str(p) for p in corrupted_template_ids]}}]}}
            query = {
                "query": q,
                "script": {
                    "source": """
                            ctx._source.template_id = null;
                        """,
                    "lang": "painless",
                },
            }

            await update_by_query_by_index_pattern(client=self._client, index_pattern=EVENT_PREFIX, query=query)
            await update_by_query_by_index_pattern(client=self._client, index_pattern=PLAN_INDEX, query=query)
            corrupted_template_ids = await find_corrupted_template_ids()
        return count

    async def cleanup_missing_plan_references(self, dry_run: bool) -> int:
        async def find_corrupted_plan_ids() -> set[UUID]:
            aggs = await self._agg_service.frequency_distribution_by_query(
                query=Query(
                    type_queries=[TypeQuery(domain_types=[Event], query=ExistsQuery(field_name="plan_extension"))]
                ),
                field_name="plan_extension.plan_id",
                size=1_000,
            )
            unique_plan_ids = {UUID(a.aggregation_key) for a in aggs}  # pyright: ignore
            found_plans = await self._plan_repo.search_by_id(ids=list(unique_plan_ids))
            found_plan_ids = {p.id for p in found_plans}

            return unique_plan_ids - found_plan_ids

        corrupted_plan_ids = await find_corrupted_plan_ids()
        if corrupted_plan_ids:
            q = ValuesQuery(field_name="plan_extension.plan_id", values=[str(p) for p in corrupted_plan_ids])
            count = await self._search_service.count_by_query(
                query=Query(type_queries=[TypeQuery(domain_types=[Event], query=q)])
            )
        else:
            count = 0

        logging.info(
            f"Number of entries to be migrated via {self.cleanup_missing_plan_references.__name__}: at least {count}"
        )
        if dry_run:
            return count

        while corrupted_plan_ids:
            q = {"bool": {"filter": [{"terms": {"plan_extension.plan_id": [str(p) for p in corrupted_plan_ids]}}]}}
            query = {
                "query": q,
                "script": {
                    "source": """
                            ctx._source.plan_extension = null;
                        """,
                    "lang": "painless",
                },
            }

            await update_by_query_by_index_pattern(client=self._client, index_pattern=EVENT_PREFIX, query=query)
            corrupted_plan_ids = await find_corrupted_plan_ids()
        return count
