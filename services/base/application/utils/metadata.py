from datetime import datetime
from typing import Optional, Union
from uuid import UUID

from services.base.domain.enums.application import Application
from services.base.domain.enums.metadata import (
    DataIntegrity,
    DataProxy,
    DataQuality,
    Organization,
    Service,
)
from services.base.domain.schemas.metadata import <PERSON>adata


def create_metadata(
    user_uuid: UUID,
    organization: Organization = Organization.LLIF,
    data_integrity: DataIntegrity = DataIntegrity.LOW,
    important: bool = False,
    urgent: bool = False,
    data_quality: Optional[DataQuality] = None,
    service: Optional[Union[Service, str]] = None,
    sync_software: Optional[Application] = None,
    sync_device: Optional[str] = None,
    sensor: Optional[str] = None,
    data_proxy: Optional[DataProxy] = None,
    favorited_at: Optional[datetime] = None,
    # Left for compatibility reasons, remove with mobile_service
    tags: Optional[list[str]] = None,
) -> Metadata:
    return Metadata(
        user_uuid=user_uuid,
        organization=organization,
        data_integrity=data_integrity,
        data_quality=data_quality,
        service=service,
        important=important,
        urgent=urgent,
        sync_software=sync_software,
        sync_device=sync_device,
        sensor=sensor,
        data_proxy=data_proxy,
        favorited_at=favorited_at,
    )
