import logging
from typing import Sequence

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.sorts import Sort, SortOrder
from services.base.application.exceptions import DuplicateDocumentsFound
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.contact import Contact, ContactFields
from services.base.domain.schemas.events.activity import Activity, ActivityFields
from services.base.domain.schemas.events.body_metric.blood_glucose import BloodGlucose
from services.base.domain.schemas.events.body_metric.blood_pressure import BloodPressure, BloodPressureFields
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetric, BodyMetricFields
from services.base.domain.schemas.events.content.audio import Audio
from services.base.domain.schemas.events.content.content import Content
from services.base.domain.schemas.events.content.content_collection import ContentFields
from services.base.domain.schemas.events.content.image import Image
from services.base.domain.schemas.events.content.interactive import Interactive
from services.base.domain.schemas.events.content.text import Text
from services.base.domain.schemas.events.content.video import Video
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.events.event_group import EventGroup
from services.base.domain.schemas.events.exercise.cardio import Cardio, CardioFields
from services.base.domain.schemas.events.exercise.exercise import Exercise, ExerciseFields
from services.base.domain.schemas.events.exercise.strength import Strength, StrengthFields
from services.base.domain.schemas.events.feeling.emotion import Emotion, EmotionFields
from services.base.domain.schemas.events.feeling.stress import Stress
from services.base.domain.schemas.events.medication.medication import Medication, MedicationFields
from services.base.domain.schemas.events.note import Note, NoteFields
from services.base.domain.schemas.events.nutrition.drink import Drink
from services.base.domain.schemas.events.nutrition.food import Food
from services.base.domain.schemas.events.nutrition.nutrition_collection import NutritionFields
from services.base.domain.schemas.events.nutrition.supplement import Supplement
from services.base.domain.schemas.events.person import Person, PersonFields
from services.base.domain.schemas.events.sleep_v3 import SleepV3
from services.base.domain.schemas.events.symptom import Symptom, SymptomFields
from services.base.domain.schemas.events.use_case import UseCase
from services.base.domain.schemas.plan.plan import Plan, PlanFields
from services.base.domain.schemas.query.boolean_query import AndQuery, NotQuery, OrQuery
from services.base.domain.schemas.query.builders.boolean_query_builder import BooleanQueryBuilder
from services.base.domain.schemas.query.leaf_query import ExistsQuery, MatchType, PatternQuery, RangeQuery, ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.templates.event_template import EventTemplate
from services.base.domain.schemas.templates.group_template import GroupTemplate
from services.base.domain.schemas.templates.template import TemplateFields
from services.base.type_resolver import TypeResolver


class DuplicateCheckService:
    def __init__(self, search_service: DocumentSearchService):
        self._search_service = search_service

    def _events_duplicates_query[T: Event](self, documents: Sequence[T], domain_type: type[T]) -> TypeQuery:
        queries: list = []
        for d in documents:
            common_q = [
                ValuesQuery(
                    field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.ORIGIN}",
                    values=[d.metadata.origin],
                ),
                ValuesQuery(
                    field_name=f"{DocumentLabels.RBAC}.{DocumentLabels.OWNER_ID}", values=[str(d.rbac.owner_id)]
                ),
                RangeQuery(field_name=ExerciseFields.TIMESTAMP, lte=d.timestamp, gte=d.timestamp),
                ValuesQuery(field_name=ExerciseFields.NAME, values=[d.name]),
            ]
            builder = BooleanQueryBuilder()
            type_q = []

            if isinstance(d, (BloodGlucose, BodyMetric)):
                type_q.append(ValuesQuery(field_name=BodyMetricFields.VALUE, values=[str(d.value)]))

            elif isinstance(d, BloodPressure):
                type_q.append(ValuesQuery(field_name=BloodPressureFields.DIASTOLIC, values=[str(d.diastolic)]))
                type_q.append(ValuesQuery(field_name=BloodPressureFields.SYSTOLIC, values=[str(d.systolic)]))

            elif isinstance(d, (Audio, Content, Interactive, Text, Video, Image)):
                if d.title:
                    type_q.append(ValuesQuery(field_name=ContentFields.TITLE, values=[d.title]))

            elif isinstance(d, Exercise):
                if d.rating:
                    type_q.append(ValuesQuery(field_name=ExerciseFields.RATING, values=[str(d.rating)]))

            elif isinstance(d, Cardio):
                if d.distance:
                    type_q.append(ValuesQuery(field_name=CardioFields.DISTANCE, values=[str(d.distance)]))

            elif isinstance(d, Person):
                if not d.contact_id:
                    type_q.append(NotQuery(queries=[ExistsQuery(field_name=PersonFields.CONTACT_ID)]))
                else:
                    type_q.append(ValuesQuery(values=[str(d.contact_id)], field_name=PersonFields.CONTACT_ID))
            elif isinstance(d, Strength):
                if d.count:
                    type_q.append(ValuesQuery(field_name=StrengthFields.COUNT, values=[str(d.count)]))

            elif isinstance(d, (Emotion, Stress)):
                if d.rating:
                    type_q.append(ValuesQuery(field_name=EmotionFields.RATING, values=[str(d.rating)]))

            elif isinstance(d, (Drink, Food, Supplement)):
                if d.rating:
                    type_q.append(ValuesQuery(field_name=NutritionFields.RATING, values=[str(d.rating)]))

            elif isinstance(d, Activity):
                if d.rating:
                    type_q.append(ValuesQuery(field_name=ActivityFields.RATING, values=[str(d.rating)]))

            elif isinstance(d, Note):
                if not d.note:
                    type_q.append(NotQuery(queries=[ExistsQuery(field_name=NoteFields.NOTE)]))
                else:
                    type_q.append(
                        PatternQuery(match_type=MatchType.EXACT, pattern=d.note, field_names=[NoteFields.NOTE])
                    )

            elif isinstance(d, Symptom):
                if d.rating:
                    type_q.append(ValuesQuery(field_name=SymptomFields.RATING, values=[str(d.rating)]))

            elif isinstance(d, SleepV3):
                ...

            elif isinstance(d, EventGroup):
                # TODO(groups): When group have child references, add it here
                ...
            elif isinstance(d, Medication):
                type_q.append(
                    ValuesQuery(
                        field_name=f"{MedicationFields.CONSUMED_AMOUNT}",
                        values=[str(d.consumed_amount)],
                    )
                )
            else:
                raise ShouldNotReachHereException(f"Unexpected type {type(d)}")

            q = common_q + type_q
            builder.add_queries(queries=q)
            queries.append(builder.build_and_query())
        return TypeQuery(query=OrQuery(queries=queries), domain_types=[domain_type])

    def _plans_duplicates_query(self, documents: Sequence[Plan]) -> TypeQuery:
        queries: list = []
        exist_query = ExistsQuery(field_name=f"{DocumentLabels.ARCHIVED_AT}")
        for d in documents:
            bool_query_builder = BooleanQueryBuilder()
            bool_query_builder.add_queries(
                queries=[
                    ValuesQuery(
                        # TODO: the owner query could technically be appended only once (if we do not mix multiple users)
                        field_name=f"{DocumentLabels.RBAC}.{DocumentLabels.OWNER_ID}",
                        values=[str(d.rbac.owner_id)],
                    ),
                    ValuesQuery(field_name=PlanFields.NAME, values=[d.name]),
                    RangeQuery(
                        field_name=PlanFields.NEXT_SCHEDULED_AT, lte=d.next_scheduled_at, gte=d.next_scheduled_at
                    ),
                    ValuesQuery(field_name=PlanFields.TEMPLATE_ID, values=[str(d.template_id)]),
                ]
            )
            if d.recurrence:
                bool_query_builder.add_query(
                    ValuesQuery(
                        field_name=PlanFields.RECURRENCE,
                        values=[str(d.recurrence)],
                    )
                )
            else:
                bool_query_builder.add_query(NotQuery(queries=[ExistsQuery(field_name=PlanFields.RECURRENCE)]))
            queries.append(bool_query_builder.build_and_query())

        return TypeQuery(
            query=AndQuery(queries=[NotQuery(queries=[exist_query]), OrQuery(queries=queries)]),
            domain_types=[Plan],
        )

    def _use_case_duplicates_query(self, documents: Sequence[UseCase]) -> TypeQuery:
        queries: list = []
        exist_query = ExistsQuery(field_name=f"{DocumentLabels.ARCHIVED_AT}")
        for d in documents:
            bool_query_builder = BooleanQueryBuilder()
            bool_query_builder.add_queries(
                queries=[
                    ValuesQuery(
                        # TODO: the owner query could technically be appended only once (if we do not mix multiple users)
                        field_name=f"{DocumentLabels.RBAC}.{DocumentLabels.OWNER_ID}",
                        values=[str(d.rbac.owner_id)],
                    ),
                    ValuesQuery(field_name=PlanFields.NAME, values=[d.name]),
                ]
            )
            queries.append(bool_query_builder.build_and_query())

        return TypeQuery(
            query=AndQuery(queries=[NotQuery(queries=[exist_query]), OrQuery(queries=queries)]),
            domain_types=[UseCase],
        )

    def _event_template_duplicates_query(self, documents: Sequence[EventTemplate]) -> TypeQuery:
        queries: list = []
        exist_query = ExistsQuery(field_name=f"{DocumentLabels.ARCHIVED_AT}")
        for d in documents:
            bool_query_builder = BooleanQueryBuilder()
            bool_query_builder.add_queries(
                queries=[
                    ValuesQuery(
                        # TODO: the owner query could technically be appended only once (if we do not mix multiple users)
                        field_name=f"{DocumentLabels.RBAC}.{DocumentLabels.OWNER_ID}",
                        values=[str(d.rbac.owner_id)],
                    ),
                    AndQuery(
                        queries=[
                            ValuesQuery(field_name=TemplateFields.NAME, values=[d.name]),
                            ValuesQuery(field_name=TemplateFields.DOCUMENT_NAME, values=[d.document_name]),
                            ValuesQuery(field_name=TemplateFields.DOCUMENT_TYPE, values=[d.document_type]),
                        ]
                    ),
                ]
            )
            queries.append(bool_query_builder.build_and_query())

        return TypeQuery(
            query=AndQuery(queries=[NotQuery(queries=[exist_query]), OrQuery(queries=queries)]),
            domain_types=[EventTemplate],
        )

    def _group_template_duplicates_query(self, documents: Sequence[GroupTemplate]) -> TypeQuery:
        queries: list = []
        exist_query = ExistsQuery(field_name=f"{DocumentLabels.ARCHIVED_AT}")
        for d in documents:
            bool_query_builder = BooleanQueryBuilder()
            bool_query_builder.add_queries(
                queries=[
                    ValuesQuery(
                        # TODO: the owner query could technically be appended only once (if we do not mix multiple users)
                        field_name=f"{DocumentLabels.RBAC}.{DocumentLabels.OWNER_ID}",
                        values=[str(d.rbac.owner_id)],
                    ),
                    ValuesQuery(field_name=TemplateFields.NAME, values=[d.name]),
                ]
            )
            queries.append(bool_query_builder.build_and_query())

        return TypeQuery(
            query=AndQuery(queries=[NotQuery(queries=[exist_query]), OrQuery(queries=queries)]),
            domain_types=[GroupTemplate],
        )

    def _contacts_duplicates_query(self, documents: Sequence[Contact]) -> TypeQuery:
        queries: list = []
        exist_query = ExistsQuery(field_name=f"{DocumentLabels.ARCHIVED_AT}")
        for d in documents:
            bool_query_builder = BooleanQueryBuilder()
            bool_query_builder.add_queries(
                queries=[
                    ValuesQuery(
                        field_name=f"{DocumentLabels.RBAC}.{DocumentLabels.OWNER_ID}",
                        values=[str(d.rbac.owner_id)],
                    ),
                    ValuesQuery(field_name=ContactFields.FIRST_NAME, values=[d.first_name]),
                    ValuesQuery(field_name=ContactFields.LAST_NAME, values=[d.last_name]) if d.last_name else None,
                    NotQuery(queries=[ExistsQuery(field_name=ContactFields.LAST_NAME)]) if not d.last_name else None,
                ]
            )
            queries.append(bool_query_builder.build_and_query())

        return TypeQuery(
            query=AndQuery(queries=[NotQuery(queries=[exist_query]), OrQuery(queries=queries)]),
            domain_types=[Contact],
        )

    def _build_validation_query[T: Document](self, documents: Sequence[T]) -> Query:
        type_queries = []

        for t in TypeResolver.EVENTS_V3:
            events = [t.map(e) for e in documents if isinstance(e, t)]
            if events:
                type_queries.append(self._events_duplicates_query(documents=events, domain_type=t))

        plans = [Plan.map(t) for t in documents if isinstance(t, Plan)]
        if plans:
            type_queries.append(self._plans_duplicates_query(documents=plans))
        uc = [UseCase.map(t) for t in documents if isinstance(t, UseCase)]
        if uc:
            type_queries.append(self._use_case_duplicates_query(documents=uc))
        event_templates = [EventTemplate.map(t) for t in documents if isinstance(t, EventTemplate)]
        if event_templates:
            type_queries.append(self._event_template_duplicates_query(documents=event_templates))
        group_templates = [GroupTemplate.map(t) for t in documents if isinstance(t, GroupTemplate)]
        if group_templates:
            type_queries.append(self._group_template_duplicates_query(documents=group_templates))
        contacts = [Contact.map(t) for t in documents if isinstance(t, Contact)]
        if contacts:
            type_queries.append(self._contacts_duplicates_query(documents=contacts))

        return Query(type_queries=type_queries)

    async def validate_no_document_duplicates[T: Document](self, documents: Sequence[T]):
        chunk = 100
        for i in range(0, len(documents), chunk):
            docs = documents[i : i + chunk]
            query = self._build_validation_query(documents=docs)
            sorts = [
                Sort(name=f"{DocumentLabels.SYSTEM_PROPERTIES}.{DocumentLabels.CREATED_AT}", order=SortOrder.DESCENDING)
            ]

            response = await self._search_service.search_documents_by_query(query=query, size=len(docs), sorts=sorts)
            if response.documents:
                logging.info(f"duplicate documents found, query: {query}")
                raise DuplicateDocumentsFound(message="duplicate documents found in database")
