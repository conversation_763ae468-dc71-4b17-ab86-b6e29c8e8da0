from abc import ABC, abstractmethod
from typing import Optional, Sequence, Type
from uuid import UUID

from services.base.application.boundaries.documents import (
    DeleteDocumentByIdOutputBoundary,
    SearchByIDDocumentsOutputBoundary,
)
from services.base.application.database.enums.bulk_operation import BulkOperation
from services.base.application.database.models.filters import Filters
from services.base.domain.schemas.shared_v2 import DeprEventModel


class DeprEventRepository(ABC):
    @abstractmethod
    def __init__(self, **kwargs):
        pass

    @abstractmethod
    async def close(self):
        pass

    @abstractmethod
    async def insert[T: DeprEventModel](
        self,
        models: Sequence[T],
        bulk_type: BulkOperation = BulkOperation.Create,
        force_strong_consistency: bool = False,
    ) -> Sequence[T]:
        pass

    @abstractmethod
    async def delete_by_id(self, doc_id: str, entry: DeprEventModel) -> DeleteDocumentByIdOutputBoundary:
        pass

    @abstractmethod
    async def delete_by_query(self, user_uuid: UUID, data_schema: Type[DeprEventModel] | str, filters: Filters) -> str:
        """Returns delete task id"""
        pass

    @abstractmethod
    async def search_by_id[T: DeprEventModel](
        self, data_schema: Type[T], doc_ids: Sequence[UUID], user_uuid: Optional[UUID]
    ) -> SearchByIDDocumentsOutputBoundary[T]:
        pass

    @abstractmethod
    async def update_by_id[T: DeprEventModel](self, doc_id: str, original_entry: T, updated_entry: T) -> T:
        pass
