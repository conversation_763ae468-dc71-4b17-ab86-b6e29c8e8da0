import random
from datetime import datetime

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.constants.value_limits import CoordinatesValueLimit
from services.base.domain.schemas.events.nutrition.nutrients import Nutrients, NutrientsValueLimits
from services.base.domain.schemas.shared import CoordinatesModel, TimeIntervalModel


class CustomModelsGenerator:
    @staticmethod
    def generate_random_coordinates() -> CoordinatesModel:
        return CoordinatesModel(
            lat=PrimitiveTypesGenerator.generate_random_int(
                min_value=CoordinatesValueLimit.LAT_MIN, max_value=CoordinatesValueLimit.LAT_MAX
            ),
            lon=PrimitiveTypesGenerator.generate_random_int(
                min_value=CoordinatesValueLimit.LON_MIN, max_value=CoordinatesValueLimit.LON_MAX
            ),
        )

    @staticmethod
    def generate_random_time_interval(
        timestamp: datetime | None = None, end_time: datetime | None = None
    ) -> TimeIntervalModel:
        if not timestamp:
            if end_time:
                timestamp = end_time - PrimitiveTypesGenerator.generate_random_timedelta()
            else:
                timestamp = PrimitiveTypesGenerator.generate_random_aware_datetime()
        if not end_time:
            end_time = timestamp + PrimitiveTypesGenerator.generate_random_timedelta()

        return TimeIntervalModel(
            timestamp=timestamp,
            end_time=end_time,
        )

    @staticmethod
    def generate_random_nutrients(allow_none: bool = False) -> Nutrients | None:
        if allow_none and random.choice([True, False]):
            return None

        return Nutrients(
            fat=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_FAT,
                max_value=NutrientsValueLimits.MAX_FAT,
            ),
            saturated_fat=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_SATURATED_FAT,
                max_value=NutrientsValueLimits.MAX_SATURATED_FAT,
            ),
            polyunsaturated_fat=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_POLYUNSATURATED_FAT,
                max_value=NutrientsValueLimits.MAX_POLYUNSATURATED_FAT,
            ),
            monounsaturated_fat=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_MONOUNSATURATED_FAT,
                max_value=NutrientsValueLimits.MAX_MONOUNSATURATED_FAT,
            ),
            trans_fat=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_TRANS_FAT,
                max_value=NutrientsValueLimits.MAX_TRANS_FAT,
            ),
            cholesterol=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_CHOLESTEROL,
                max_value=NutrientsValueLimits.MAX_CHOLESTEROL,
            ),
            carbohydrates=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_CARBOHYDRATES,
                max_value=NutrientsValueLimits.MAX_CARBOHYDRATES,
            ),
            fiber=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_FIBER,
                max_value=NutrientsValueLimits.MAX_FIBER,
            ),
            sugar=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_SUGAR,
                max_value=NutrientsValueLimits.MAX_SUGAR,
            ),
            protein=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_PROTEIN,
                max_value=NutrientsValueLimits.MAX_PROTEIN,
            ),
            sodium=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_SODIUM,
                max_value=NutrientsValueLimits.MAX_SODIUM,
            ),
            potassium=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_POTASSIUM,
                max_value=NutrientsValueLimits.MAX_POTASSIUM,
            ),
            vitamin_a=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_VITAMIN_A,
                max_value=NutrientsValueLimits.MAX_VITAMIN_A,
            ),
            vitamin_c=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_VITAMIN_C,
                max_value=NutrientsValueLimits.MAX_VITAMIN_C,
            ),
            iron=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_IRON,
                max_value=NutrientsValueLimits.MAX_IRON,
            ),
            calcium=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_CALCIUM,
                max_value=NutrientsValueLimits.MAX_CALCIUM,
            ),
            biotin=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_BIOTIN,
                max_value=NutrientsValueLimits.MAX_BIOTIN,
            ),
            caffeine=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_CAFFEINE,
                max_value=NutrientsValueLimits.MAX_CAFFEINE,
            ),
            chloride=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_CHLORIDE,
                max_value=NutrientsValueLimits.MAX_CHLORIDE,
            ),
            chromium=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_CHROMIUM,
                max_value=NutrientsValueLimits.MAX_CHROMIUM,
            ),
            copper=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_COPPER,
                max_value=NutrientsValueLimits.MAX_COPPER,
            ),
            folate=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_FOLATE,
                max_value=NutrientsValueLimits.MAX_FOLATE,
            ),
            iodine=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_IODINE,
                max_value=NutrientsValueLimits.MAX_IODINE,
            ),
            magnesium=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_MAGNESIUM,
                max_value=NutrientsValueLimits.MAX_MAGNESIUM,
            ),
            manganese=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_MANGANESE,
                max_value=NutrientsValueLimits.MAX_MANGANESE,
            ),
            molybdenum=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_MOLYBDENUM,
                max_value=NutrientsValueLimits.MAX_MOLYBDENUM,
            ),
            niacin=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_NIACIN,
                max_value=NutrientsValueLimits.MAX_NIACIN,
            ),
            pantothenic_acid=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_PANTOTHENIC_ACID,
                max_value=NutrientsValueLimits.MAX_PANTOTHENIC_ACID,
            ),
            phosphorus=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_PHOSPHORUS,
                max_value=NutrientsValueLimits.MAX_PHOSPHORUS,
            ),
            riboflavin=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_RIBOFLAVIN,
                max_value=NutrientsValueLimits.MAX_RIBOFLAVIN,
            ),
            selenium=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_SELENIUM,
                max_value=NutrientsValueLimits.MAX_SELENIUM,
            ),
            thiamin=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_THIAMIN,
                max_value=NutrientsValueLimits.MAX_THIAMIN,
            ),
            vitamin_b6=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_VITAMIN_B6,
                max_value=NutrientsValueLimits.MAX_VITAMIN_B6,
            ),
            vitamin_b12=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_VITAMIN_B12,
                max_value=NutrientsValueLimits.MAX_VITAMIN_B12,
            ),
            vitamin_d=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_VITAMIN_D,
                max_value=NutrientsValueLimits.MAX_VITAMIN_D,
            ),
            vitamin_e=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_VITAMIN_E,
                max_value=NutrientsValueLimits.MAX_VITAMIN_E,
            ),
            vitamin_k=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_VITAMIN_K,
                max_value=NutrientsValueLimits.MAX_VITAMIN_K,
            ),
            zinc=PrimitiveTypesGenerator.generate_random_float(
                min_value=NutrientsValueLimits.MIN_ZINC,
                max_value=NutrientsValueLimits.MAX_ZINC,
            ),
        )
