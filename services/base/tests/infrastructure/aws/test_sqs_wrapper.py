import json
from unittest.mock import <PERSON><PERSON>ock

import pytest
from mypy_boto3_sqs import SQSServiceResource
from mypy_boto3_sqs.service_resource import Queue

from services.base.infrastructure.aws.sqs_wrapper import SQSWrapper


@pytest.fixture(scope="module")
def sqs_service_mock():
    return MagicMock(spec=SQSServiceResource)


def test_get_queue_by_name(sqs_service_mock):
    # Arrange
    queue = "DummyQueue"
    sqs_wrapper = SQSWrapper(sqs_service=sqs_service_mock)
    # Act
    sqs_wrapper._get_queue_by_name(queue_name=queue)
    # Assert
    sqs_service_mock.get_queue_by_name.assert_called_with(QueueName=queue)


def test_publish_to_queue(sqs_service_mock):
    # Arrange
    queue = MagicMock(spec=Queue)
    sqs_service_mock.get_queue_by_name.return_value = queue
    sqs_wrapper = SQSWrapper(sqs_service=sqs_service_mock)
    queue_name = "DummyQueue"
    message = {"message": "DummyMessage"}
    message_attributes = {"DummyAttribute": "DummyValue"}
    # Act
    sqs_wrapper.publish_to_queue(queue_name=queue_name, message_body=message, message_attributes=message_attributes)
    # Assert
    queue.send_message.assert_called_with(MessageBody=json.dumps(message), MessageAttributes=message_attributes)


def test_consume_messages_from_sns(sqs_service_mock):
    # Arrange
    queue_name = "DummyQueue"
    message_attributes = ["DummyAttribute"]
    max_number_of_messages = 1
    polling_time = 1

    queue = MagicMock(spec=Queue)
    sqs_service_mock.get_queue_by_name.return_value = queue
    sqs_wrapper = SQSWrapper(sqs_service=sqs_service_mock)

    # Act
    sqs_wrapper.consume_messages_from_topics(
        queue_name=queue_name,
        max_number_of_messages=max_number_of_messages,
        polling_time=polling_time,
        message_handler=MagicMock(),
        message_attributes=message_attributes,
    )
    # Assert
    queue.receive_messages.assert_called_with(
        MaxNumberOfMessages=max_number_of_messages,
        WaitTimeSeconds=polling_time,
        MessageAttributeNames=message_attributes,
    )


def test_upsert_queue_access_policy_no_original_policy_should_pass(sqs_service_mock):
    # Arrange
    queue_arn = "DummyQueueArn"
    queue_name = "DummyQueue"
    arn = "DummyArn"
    queue = MagicMock(spec=Queue)
    queue.attributes = {"QueueArn": queue_arn}

    sqs_service_mock.get_queue_by_name.return_value = queue
    sqs_wrapper = SQSWrapper(sqs_service=sqs_service_mock)

    expected_policy = {
        "Policy": json.dumps(
            {
                "Version": "2008-10-17",
                "Id": "__default_policy_ID",
                "Statement": [
                    {
                        "Sid": f"topic-subscription-{queue_arn}",
                        "Effect": "Allow",
                        "Principal": {"AWS": "*"},
                        "Action": "SQS:SendMessage",
                        "Resource": queue_arn,
                        "Condition": {"ArnLike": {"aws:SourceArn": arn}},
                    }
                ],
            }
        )
    }
    # Act
    sqs_wrapper.upsert_queue_access_policy(queue_name=queue_name, resource_identifier=arn)
    # Assert
    queue.set_attributes.assert_called_with(Attributes=expected_policy)


def test_upsert_queue_access_policy_existing_original_policy_should_pass(sqs_service_mock):
    # Arrange
    queue_arn = "DummyQueueArn"
    queue_name = "DummyQueue"
    arn = "DummyArn"
    test_statement = {
        "Sid": "test-subscription",
        "Effect": "Allow",
        "Principal": {"AWS": "*"},
        "Action": "Test:TestAction",
        "Resource": "test_resource",
        "Condition": {"ArnLike": {"aws:SourceArn": "TestArn"}},
    }
    original_policy = {
        "Version": "2008-10-17",
        "Id": "__default_policy_ID",
        "Statement": [test_statement],
    }

    queue = MagicMock(spec=Queue)
    queue.attributes = {"QueueArn": queue_arn, "Policy": json.dumps(original_policy)}

    sqs_service_mock.get_queue_by_name.return_value = queue
    sqs_wrapper = SQSWrapper(sqs_service=sqs_service_mock)

    expected_policy = {
        "Policy": json.dumps(
            {
                "Version": "2008-10-17",
                "Id": "__default_policy_ID",
                "Statement": [
                    test_statement,
                    {
                        "Sid": f"topic-subscription-{queue_arn}",
                        "Effect": "Allow",
                        "Principal": {"AWS": "*"},
                        "Action": "SQS:SendMessage",
                        "Resource": queue_arn,
                        "Condition": {"ArnLike": {"aws:SourceArn": arn}},
                    },
                ],
            }
        )
    }
    # Act
    sqs_wrapper.upsert_queue_access_policy(queue_name=queue_name, resource_identifier=arn)
    # Assert
    queue.set_attributes.assert_called_with(Attributes=expected_policy)
