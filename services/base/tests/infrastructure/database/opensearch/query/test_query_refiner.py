from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.schemas.events.activity import Activity
from services.base.domain.schemas.events.document_base import EventMetadataFields
from services.base.domain.schemas.events.note import Note
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.infrastructure.database.opensearch.query_translator.query_refiner import QueryRefiner


class TestQueryRefiner:
    def test_query_optimization_same_queries_should_refine(self):
        # Arrange
        heart_rate_value_query = ValuesQuery(
            field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.ORGANIZATION}",
            values=[Organization.LLIF, Organization.AMAZON, Organization.FACEBOOK],
        )
        exercise_value_query = ValuesQuery(
            field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.ORGANIZATION}",
            values=[Organization.LLIF, Organization.AMAZON, Organization.FACEBOOK],
        )

        heart_rate_type_query = TypeQuery(domain_types=[HeartRate], query=heart_rate_value_query)
        note_type_query = TypeQuery(domain_types=[Note], query=exercise_value_query)

        type_queries = [heart_rate_type_query, note_type_query]

        # Act
        query = Query(type_queries=type_queries)
        refined_query = QueryRefiner.refine(query=query)

        # Assert
        expected_value_query = ValuesQuery(
            field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.ORGANIZATION}",
            values=[Organization.LLIF, Organization.AMAZON, Organization.FACEBOOK],
        )
        expected_type_query = TypeQuery(domain_types=[HeartRate, Note], query=expected_value_query)

        assert len(refined_query.type_queries) == 1
        assert refined_query.type_queries[0] == expected_type_query

    def test_query_optimization_different_queries_should_not_refine(self):
        # Arrange
        note_value_query = ValuesQuery(
            field_name=f"{DocumentLabels.METADATA}.{EventMetadataFields.ORIGIN}",
            values=[Origin.LLIF, Origin.AMAZON, Origin.GOOGLE],
        )
        core_value_query = ValuesQuery(
            field_name=f"{DocumentLabels.METADATA}.{EventMetadataFields.ORIGIN}",
            values=[Origin.LLIF, Origin.GOOGLE],
        )

        heart_rate_type_query = TypeQuery(domain_types=[Note], query=note_value_query)
        exercise_type_query = TypeQuery(domain_types=[Activity], query=core_value_query)

        type_queries = [heart_rate_type_query, exercise_type_query]

        # Act
        query = Query(type_queries=type_queries)
        refined_query = QueryRefiner.refine(query=query)

        # Assert
        assert refined_query.type_queries == query.type_queries
