from datetime import datetime, timezone
from zoneinfo import ZoneInfo

import pytest

from services.base.domain.schemas.shared import TimestampModel
from services.base.infrastructure.database.opensearch.opensearch_utils import OpenSearchUtils


class TestOpenSearchUtils:
    @pytest.mark.parametrize(
        "timestamp,index_name,template,expected_output",
        [
            (
                datetime(day=30, month=11, year=2023, hour=23, tzinfo=ZoneInfo("EST")),
                "test",
                "%Y-%m",
                "test-2023-12",
            ),
            (
                datetime(day=1, month=12, year=2023, hour=0, tzinfo=ZoneInfo("CET")),
                "test",
                "%Y-%m",
                "test-2023-11",
            ),
            (
                datetime(day=1, month=12, year=2023, hour=0, tzinfo=ZoneInfo("UTC")),
                "test",
                "%Y-%m",
                "test-2023-12",
            ),
            (
                datetime(day=31, month=12, year=2023, hour=23, tzinfo=ZoneInfo("EST")),
                "test",
                "%Y",
                "test-2024",
            ),
            (
                datetime(day=1, month=1, year=2024, hour=0, tzinfo=ZoneInfo("CET")),
                "test",
                "%Y",
                "test-2023",
            ),
            (
                datetime(day=1, month=12, year=2023, hour=0, tzinfo=ZoneInfo("UTC")),
                "test",
                "%Y-%m",
                "test-2023-12",
            ),
        ],
    )
    def test_get_time_based_index_name_from_timestamp(
        self, timestamp: datetime, index_name: str, template: str, expected_output: str
    ):
        assert (
            OpenSearchUtils.get_time_based_index_name_from_timestamp(
                index_name=index_name, timestamp=timestamp, template=template
            )
            == expected_output
        )

    @pytest.mark.parametrize(
        "index_name,expected_output",
        [
            ("heart_rate-2023-12-01", "heart_rate"),
            ("heart_rate-2023", "heart_rate"),
            ("extension_output-2023", "extension_output"),
        ],
    )
    def test_get_index_name_without_postfix(self, index_name: str, expected_output: str):
        assert OpenSearchUtils.get_index_name_without_postfix(index_name=index_name) == expected_output

    @pytest.mark.parametrize(
        "index_name,is_splittable,entry,template,expected_output",
        [
            (
                "heart_rate",
                True,
                TimestampModel(timestamp=datetime(year=2023, month=11, day=1, tzinfo=timezone.utc)),
                "%Y-%m",
                "heart_rate-2023-11",
            ),
            (
                "heart_rate",
                True,
                TimestampModel(timestamp=datetime(year=2023, month=11, day=1, tzinfo=timezone.utc)),
                "%Y",
                "heart_rate-2023",
            ),
            (
                "heart_rate",
                False,
                TimestampModel(timestamp=datetime(year=2023, month=11, day=1, tzinfo=timezone.utc)),
                "%Y",
                "heart_rate",
            ),
            (
                "extension_output",
                False,
                TimestampModel(timestamp=datetime(year=2023, month=11, day=1, tzinfo=timezone.utc)),
                "%Y",
                "extension_output",
            ),
            (
                "extension_output",
                True,
                TimestampModel(timestamp=datetime(year=2020, month=1, day=1, tzinfo=timezone.utc)),
                "%Y-%m",
                "extension_output-2020-01",
            ),
            (
                "extension_output",
                True,
                TimestampModel(timestamp=datetime(year=2020, month=11, day=1, tzinfo=timezone.utc)),
                "%Y",
                "extension_output-2020",
            ),
        ],
    )
    def test_get_index_name(
        self, index_name: str, is_splittable: bool, entry: TimestampModel, template: str, expected_output: str
    ):
        assert (
            OpenSearchUtils.get_index_name(
                index_name=index_name, is_splittable=is_splittable, template=template, entry=entry
            )
            == expected_output
        )
