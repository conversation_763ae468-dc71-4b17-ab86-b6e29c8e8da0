import pytest

from services.base.domain.validations.validate_non_empty_length import validate_non_empty_length


class TestValidateNonEmptyLength:
    @pytest.mark.parametrize(
        "input_value, expected_output",
        [
            ("hello", "hello"),
            ("  hello  ", "hello"),
            (None, None),
            (b"hello", b"hello"),
            (b"  hello  ", b"hello"),
        ],
    )
    def test_validate_non_empty_length_passes(self, input_value, expected_output):
        assert validate_non_empty_length(input_value) == expected_output

    @pytest.mark.parametrize(
        "input_value",
        [
            (""),
            ("    "),
            (b""),
            (b"   "),
        ],
    )
    def test_validate_non_empty_length_raises(self, input_value):
        with pytest.raises(ValueError):
            validate_non_empty_length(input_value)
