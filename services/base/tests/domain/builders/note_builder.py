from typing import Self, Sequence
from uuid import uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.note import Note, NoteIdentifier
from services.base.tests.domain.builders.event_builder_base import EventBuilderBase
from services.base.tests.domain.builders.event_metadata_builder import EventMetadataBuilder


class NoteBuilder(EventBuilderBase, NoteIdentifier):
    def __init__(self):
        self._note: str | None = None
        super().__init__()

    def build(self) -> Note:
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        end_time = self._end_time or PrimitiveTypesGenerator.generate_random_aware_datetime(gte=timestamp)
        return Note(
            type=DataType.Note,
            template_id=None,
            name=self._name or PrimitiveTypesGenerator.generate_random_string(max_length=32),
            timestamp=timestamp,
            end_time=end_time,
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            metadata=EventMetadataBuilder()
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build(),
            submission_id=self._submission_id or uuid4(),
            note=self._note or PrimitiveTypesGenerator.generate_random_string(),
            id=uuid4(),
            group_id=self._group_id,
            asset_references=self._asset_references,
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
            plan_extension=None,
        )

    def build_n(self, n: int | None = None) -> Sequence[Note]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_note(self, note: str) -> Self:
        self._note = note
        return self
