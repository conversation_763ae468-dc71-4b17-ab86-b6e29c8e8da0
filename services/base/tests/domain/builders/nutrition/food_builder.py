from typing import Self, Sequence
from uuid import uuid4

from services.base.application.generators.custom_models_generators import CustomModelsGenerator
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.events.nutrition.food import Food, FoodCategory, FoodIdentifier
from services.base.tests.domain.builders.event_builder_base import EventBuilderBase
from services.base.tests.domain.builders.event_metadata_builder import EventMetadataBuilder


class FoodBuilder(EventBuilderBase, FoodIdentifier):
    def __init__(self):
        self._calories: float | None = None
        super().__init__()

    def build(self) -> Food:
        return Food(
            type=DataType.Food,
            template_id=None,
            id=uuid4(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=FoodCategory),
            submission_id=self._submission_id or uuid4(),
            group_id=self._group_id,
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            metadata=EventMetadataBuilder()
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build(),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=self._name or PrimitiveTypesGenerator.generate_random_string(max_length=32),
            rating=PrimitiveTypesGenerator.generate_random_int(max_value=10),
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
            asset_references=self._asset_references,
            nutrients=CustomModelsGenerator.generate_random_nutrients(allow_none=True),
            consumed_type=PrimitiveTypesGenerator.generate_random_enum(enum_type=WeightUnit),
            consumed_amount=100,
            calories=self._calories
            or PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=1000, allow_none=True),
            brand=None,
            flavor=PrimitiveTypesGenerator.generate_random_string(min_length=1, max_length=128),
            plan_extension=None,
        )

    def build_n(self, n: int | None = None) -> Sequence[Food]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_calories(self, calories: float) -> Self:
        self._calories = calories
        return self
