import random
from typing import Self, Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.schemas.events.event_group import EventGroup
from services.base.type_resolver import TypeResolver


class GroupBuilder:
    def __init__(self):
        self._owner_id: UUID = uuid4()
        self._origin: Origin | None = None
        self._group_id: UUID | None = None

    def build(self) -> TypeResolver.GROUP_UNION:
        builder = random.choice(TypeResolver.GROUP_BUILDERS)
        return (
            builder()
            .with_owner_id(owner_id=self._owner_id)
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .with_group_id(group_id=self._group_id)
            .build()
        )

    def build_n(self, n: int | None = None) -> Sequence[EventGroup]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(max_value=5, min_value=1))]

    def build_all(self, n: int | None = None) -> Sequence[EventGroup]:
        out = []
        for builder_type in TypeResolver.GROUP_BUILDERS:
            out.extend(
                [
                    builder_type()
                    .with_owner_id(owner_id=self._owner_id)
                    .with_group_id(group_id=self._group_id)
                    .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
                    .build()
                    for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))
                ]
            )
        return out

    def with_owner_id(self, owner_id: UUID) -> Self:
        self._owner_id = owner_id
        return self

    def with_origin(self, origin: Origin) -> Self:
        self._origin = origin
        return self

    def with_group_id(self, group_id: UUID | None) -> Self:
        self._group_id = group_id
        return self
