from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import InsertableOrigin, Origin
from services.base.domain.schemas.events.body_metric.blood_glucose import BloodGlucoseIdentifier
from services.base.domain.schemas.events.body_metric.blood_pressure import BloodPressureIdentifier
from services.base.domain.schemas.events.body_metric.body_metric import BodyMetricIdentifier
from services.base.domain.schemas.templates.payload.body_metric_template_payloads import (
    BloodGlucoseTemplatePayload,
    BloodPressureTemplatePayload,
    BodyMetricTemplatePayload,
)
from services.base.tests.domain.builders.blood_glucose_builder import BloodGlucoseBuilder
from services.base.tests.domain.builders.blood_pressure_builder import BloodPressureBuilder
from services.base.tests.domain.builders.body_metric_builder import BodyMetricBuilder
from services.base.tests.domain.builders.template.payload.event_payload_base import EventPayloadBuilderBase


class BloodGlucosePayloadBuilder(EventPayloadBuilderBase, BloodGlucoseIdentifier):

    def build(self) -> BloodGlucoseTemplatePayload:
        return BloodGlucoseTemplatePayload.map(
            model=BloodGlucoseBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )


class BloodPressurePayloadBuilder(EventPayloadBuilderBase, BloodPressureIdentifier):

    def build(self) -> BloodPressureTemplatePayload:
        return BloodPressureTemplatePayload.map(
            model=BloodPressureBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )


class BodyMetricPayloadBuilder(EventPayloadBuilderBase, BodyMetricIdentifier):

    def build(self) -> BodyMetricTemplatePayload:
        return BodyMetricTemplatePayload.map(
            model=BodyMetricBuilder()
            .with_origin(
                origin=Origin(
                    (self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=InsertableOrigin)).value
                )
            )
            .build()
        )
