from typing import Self, Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata_v3 import Origin
from services.base.domain.schemas.events.content.interactive import (
    Interactive,
    InteractiveCategory,
    InteractiveIdentifier,
)
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.tests.domain.builders.event_builder_base import Event<PERSON><PERSON>erBase
from services.base.tests.domain.builders.event_metadata_builder import EventMetadataBuilder


class InteractiveBuilder(EventBuilderBase, InteractiveIdentifier):
    def __init__(self):
        super().__init__()

    def build(self) -> Interactive:
        return Interactive(
            type=DataType.Interactive,
            template_id=None,
            id=uuid4(),
            category=PrimitiveTypesGenerator.generate_random_enum(enum_type=InteractiveCategory),
            submission_id=self._submission_id or uuid4(),
            group_id=self._group_id,
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            metadata=EventMetadataBuilder()
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build(),
            timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime(),
            end_time=self._end_time,
            name=self._name or PrimitiveTypesGenerator.generate_random_string(max_length=32),
            title=PrimitiveTypesGenerator.generate_random_string(max_length=128),
            rating=PrimitiveTypesGenerator.generate_random_int(max_value=10),
            note=PrimitiveTypesGenerator.generate_random_string(),
            tags=self._tags or PrimitiveTypesGenerator.generate_random_tags(),
            asset_references=self._asset_references,
            url=PrimitiveTypesGenerator.generate_https_url(),
            plan_extension=None,
        )

    def build_n(self, n: int | None = None) -> Sequence[Interactive]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_submission_id(self, submission_id: UUID) -> Self:
        self._submission_id = submission_id
        return self
