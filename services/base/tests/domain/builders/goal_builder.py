import json
from datetime import datetime
from typing import Self, Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.custom_rrule import CustomRRule
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.metadata import Organization
from services.base.domain.schemas.events.document_base import RBACSchema
from services.base.domain.schemas.plan.goal import Goal, GoalCondition
from services.base.domain.schemas.plan.plan_base import PlanBaseMetadata
from services.base.domain.schemas.query.aggregations import SimpleAggregationMethod
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.tests.domain.builders.plan_builder import PlanStreakBuilder
from services.data_service.api.serializers.query_marshaller import QueryMarshaller


class GoalBuilder:
    def __init__(self):
        self._current_value: float | None = None
        self._condition: GoalCondition | None = None
        self._recurrence: CustomRRule | None = None
        self._owner_id: UUID | None = None
        self._archived_at: datetime | bool | None = None
        self._name: str | None = None
        self._template_id: UUID | None = None
        self._next_scheduled_at: datetime | None = None
        self._rrule_started_at: datetime | None = None

    def build(self):
        if not self._condition:
            raise ValueError("condition is required")
        started_at = self._rrule_started_at or PrimitiveTypesGenerator.generate_random_aware_datetime()
        recurrence = self._recurrence or PrimitiveTypesGenerator.generate_random_rrule(
            started_at=started_at, allow_none=True
        )
        next_scheduled_at = self._next_scheduled_at or PrimitiveTypesGenerator.generate_random_aware_datetime(
            gte=started_at
        )

        archived_at = (
            self._archived_at
            if isinstance(self._archived_at, datetime)
            else (
                None
                if self._archived_at is False
                else PrimitiveTypesGenerator.generate_random_aware_datetime(allow_none=True)
            )
        )
        max_completed = PrimitiveTypesGenerator.generate_random_int(
            min_value=1, max_value=10, allow_none=bool(recurrence)
        )
        condition = self._condition

        return Goal(
            type=DataType.Goal,
            id=uuid4(),
            metadata=PlanBaseMetadata(organization=Organization.LLIF),
            name=self._name or PrimitiveTypesGenerator.generate_random_string(),
            recurrence=recurrence,
            next_scheduled_at=next_scheduled_at,
            note=PrimitiveTypesGenerator.generate_random_string(allow_none=True),
            streak=PlanStreakBuilder().build(),
            rbac=RBACSchema(owner_id=self._owner_id or uuid4()),
            archived_at=archived_at,
            current_completed=(
                PrimitiveTypesGenerator.generate_random_int(min_value=0, max_value=(max_completed or 11) - 1)
                if recurrence
                else max_completed - 1
            ),
            max_completed=max_completed,
            tags=PrimitiveTypesGenerator.generate_random_tags(),
            current_value=(
                self._current_value
                if self._current_value is not None
                else PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=100)
            ),
            condition=condition,
        )

    def build_n(self, n: int | None = None) -> Sequence[Goal]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))]

    def with_recurrence(self, recurrence: CustomRRule | None) -> Self:
        self._recurrence = recurrence
        return self

    def with_owner_id(self, owner_id: UUID) -> Self:
        self._owner_id = owner_id
        return self

    def with_archived_at(self, archived_at: datetime | bool) -> Self:
        self._archived_at = archived_at
        return self

    def with_name(self, name: str) -> Self:
        self._name = name
        return self

    def with_next_scheduled_at(self, dt: datetime) -> Self:
        self._next_scheduled_at = dt
        return self

    def with_rrule_started_at(self, dt: datetime) -> Self:
        self._rrule_started_at = dt
        return self

    def with_condition(self, condition: GoalCondition) -> Self:
        self._condition = condition
        return self

    def with_current_value(self, current_value: float) -> Self:
        self._current_value = current_value
        return self


class GoalConditionBuilder:
    def __init__(self):
        self._gte: float | None = None
        self._lte: float | None = None
        self._field_name: str | None = None
        self._agg_method: SimpleAggregationMethod | None = None
        self._query: TypeQuery | None = None

    def build(self):
        if not self._field_name:
            raise ValueError("field_name is required")
        if not self._query:
            raise ValueError("query is required")
        return GoalCondition(
            gte=self._gte or PrimitiveTypesGenerator.generate_random_float(min_value=0, max_value=100),
            lte=self._lte or PrimitiveTypesGenerator.generate_random_float(min_value=100, max_value=200),
            field_name=self._field_name,
            agg_method=self._agg_method
            or PrimitiveTypesGenerator.generate_random_enum(enum_type=SimpleAggregationMethod),
            query=json.dumps(QueryMarshaller.serialize_type_query(self._query)),
        )

    def with_field_name(self, field_name: str) -> Self:
        self._field_name = field_name
        return self

    def with_gte(self, gte: float) -> Self:
        self._gte = gte
        return self

    def with_lte(self, lte: float) -> Self:
        self._lte = lte
        return self

    def with_agg_method(self, agg_method: SimpleAggregationMethod) -> Self:
        self._agg_method = agg_method
        return self

    def with_query(self, query: TypeQuery) -> Self:
        self._query = query
        return self
