from __future__ import annotations

from datetime import datetime
from uuid import UUID

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.schemas.shopping_activity import ShoppingActivity, ShoppingActivityItemDetail
from services.base.tests.domain.builders.metadata_builder import MetadataBuilder


class ShoppingActivityBuilder:
    def __init__(self):
        self._metadata: MetadataBuilder = MetadataBuilder()
        self._timestamp: datetime | None = None
        self._order_id: str | None = None
        self._item_details: list[ShoppingActivityItemDetail] | None = None

    def build(self) -> ShoppingActivity:
        order_id = self._order_id or PrimitiveTypesGenerator.generate_random_string(max_length=10)
        item_details = self._item_details or [
            ShoppingActivityItemDetail(
                category=PrimitiveTypesGenerator.generate_random_string(max_length=10),
                asin_isbn=PrimitiveTypesGenerator.generate_random_string(max_length=10),
                qty=PrimitiveTypesGenerator.generate_random_int(1, 5),
                lppu=PrimitiveTypesGenerator.generate_random_float(5, 100),
                pppu=PrimitiveTypesGenerator.generate_random_float(5, 100),
            )
        ]
        metadata = self._metadata.build()
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        return ShoppingActivity(
            order_id=order_id,
            item_detail=item_details,
            timestamp=timestamp,
            metadata=metadata,
        )

    def with_order_id(self, order_id: str) -> ShoppingActivityBuilder:
        self._order_id = order_id
        return self

    def with_item_details(self, item_details: list[ShoppingActivityItemDetail]) -> ShoppingActivityBuilder:
        self._item_details = item_details
        return self

    def with_user_uuid(self, user_uuid: UUID) -> ShoppingActivityBuilder:
        self._metadata = self._metadata.with_user_uuid(user_uuid)
        return self

    def with_timestamp(self, timestamp: datetime) -> ShoppingActivityBuilder:
        self._timestamp = timestamp
        return self
