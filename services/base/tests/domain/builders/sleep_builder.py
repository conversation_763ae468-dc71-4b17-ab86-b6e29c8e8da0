from __future__ import annotations

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata import Organization
from services.base.domain.enums.sleep_stages import SleepStage
from services.base.domain.schemas.sleep import Sleep, SleepDetail, SleepEvent, SleepSummary
from services.base.tests.domain.builders.metadata_builder import MetadataBuilder


class SleepBuilder:
    def __init__(self):
        self._metadata: MetadataBuilder = MetadataBuilder()
        self._timestamp: datetime | None = None

    def build(self) -> Sleep:
        metadata = self._metadata.build()
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        end_time = timestamp
        sleep_events = [SleepEventBuilder().with_timestamp(timestamp=timestamp).build()]
        return Sleep(
            sleep_events=sleep_events,
            timestamp=timestamp,
            metadata=metadata,
            end_time=end_time,
        )

    def with_user_uuid(self, user_uuid: UUID) -> SleepBuilder:
        self._metadata = self._metadata.with_user_uuid(user_uuid)
        return self

    def with_organization(self, organization: Organization) -> SleepBuilder:
        self._metadata = self._metadata.with_organization(organization)
        return self

    def with_timestamp(self, timestamp: datetime) -> SleepBuilder:
        self._timestamp = timestamp
        return self


class SleepEventBuilder:
    def __init__(self):
        self._timestamp: datetime | None = None

    def build(self) -> SleepEvent:
        timestamp: datetime = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        sleep_detail: Optional[List[SleepDetail]] = [SleepDetailBuilder().with_timestamp(timestamp=timestamp).build()]
        sleep_summary: SleepSummary = SleepSummaryBuilder().build()
        end_time = timestamp
        return SleepEvent(
            sleep_detail=sleep_detail, sleep_summary=sleep_summary, timestamp=timestamp, end_time=end_time
        )

    def with_timestamp(self, timestamp: datetime) -> SleepEventBuilder:
        self._timestamp = timestamp
        return self


class SleepDetailBuilder:
    def __init__(self):
        self._timestamp: datetime | None = None

    def build(self) -> SleepDetail:
        timestamp = self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
        return SleepDetail(timestamp=timestamp, stage=SleepStage.ASLEEP)

    def with_timestamp(self, timestamp: datetime) -> SleepDetailBuilder:
        self._timestamp = timestamp
        return self


class SleepSummaryBuilder:
    def build(self) -> SleepSummary:
        efficiency: Optional[float] = None
        is_main_sleep: Optional[bool] = True
        events_count: Optional[int] = None
        fall_asleep_seconds: Optional[int] = None
        after_wakeup_seconds: Optional[int] = None
        awake_seconds: Optional[int] = None
        asleep_seconds: Optional[int] = PrimitiveTypesGenerator.generate_random_int(min_value=11000, max_value=45000)
        in_bed_seconds: Optional[int] = None
        deep_seconds: Optional[int] = None
        light_seconds: Optional[int] = None
        rem_seconds: Optional[int] = None
        restless_seconds: Optional[int] = None
        return SleepSummary(
            efficiency=efficiency,
            is_main_sleep=is_main_sleep,
            events_count=events_count,
            fall_asleep_seconds=fall_asleep_seconds,
            after_wakeup_seconds=after_wakeup_seconds,
            awake_seconds=awake_seconds,
            asleep_seconds=asleep_seconds,
            in_bed_seconds=in_bed_seconds,
            deep_seconds=deep_seconds,
            light_seconds=light_seconds,
            rem_seconds=rem_seconds,
            restless_seconds=restless_seconds,
        )
