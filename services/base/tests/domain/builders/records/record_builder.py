import random
from datetime import datetime
from typing import Self, Sequence
from uuid import UUID, uuid4

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.metadata_v3 import Origin
from services.base.tests.domain.builders.builder_base import BuilderBase
from services.base.type_resolver import TypeResolver


class RecordBuilder(BuilderBase):
    def __init__(self):
        self._origin: Origin | None = None
        self._owner_id: UUID | None = None
        self._timestamp: datetime | None = None
        self._end_time: datetime | None = None

    def build(self) -> TypeResolver.RECORD_UNION:
        builder_type = random.choice(TypeResolver.RECORD_BUILDERS)
        return (
            builder_type()
            .with_timestamp(timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime())
            .with_end_time(end_time=self._end_time)
            .with_owner_id(owner_id=self._owner_id or uuid4())
            .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
            .build()
        )

    def build_n(self, n: int | None = None) -> Sequence[TypeResolver.RECORD_UNION]:
        return [self.build() for _ in range(n or PrimitiveTypesGenerator.generate_random_int(max_value=5, min_value=1))]

    def build_all(self, n: int | None = None) -> Sequence[TypeResolver.RECORD_UNION]:
        out = []
        for builder_type in TypeResolver.RECORD_BUILDERS:
            out.extend(
                [
                    builder_type()
                    .with_timestamp(
                        timestamp=self._timestamp or PrimitiveTypesGenerator.generate_random_aware_datetime()
                    )
                    .with_end_time(end_time=self._end_time)
                    .with_owner_id(owner_id=self._owner_id or uuid4())
                    .with_origin(origin=self._origin or PrimitiveTypesGenerator.generate_random_enum(enum_type=Origin))
                    .build()
                    for _ in range(n or PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=5))
                ]
            )
        return out

    def with_owner_id(self, owner_id: UUID) -> Self:
        self._owner_id = owner_id
        return self

    def with_origin(self, origin: Origin) -> Self:
        self._origin = origin
        return self

    def with_timestamp(self, timestamp: datetime) -> Self:
        self._timestamp = timestamp
        return self

    def with_end_time(self, end_time: datetime | None) -> Self:
        self._end_time = end_time
        return self
