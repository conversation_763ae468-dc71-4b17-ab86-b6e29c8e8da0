from datetime import datetime, timedelta, timezone
from zoneinfo import ZoneInfo

from services.base.domain.time import is_timezone_aware, parse_datetime_tz_fallback


def test_parse_datetime_tz_fallback_none_input():
    assert parse_datetime_tz_fallback(value=None) is None


def test_parse_datetime_tz_fallback_input_str_not_timezone_aware():
    results = parse_datetime_tz_fallback(value="2020-03-03 02:04:52")
    assert isinstance(results, datetime)
    assert is_timezone_aware(results)
    assert results.tzinfo == ZoneInfo("UTC")


def test_parse_datetime_tz_fallback_input_str_timezone_aware():
    input_str_offset = "+02:00"
    expected_timedelta_offset = timedelta(hours=2, minutes=0, seconds=0)
    results = parse_datetime_tz_fallback(value="2020-03-03 02:04:52" + input_str_offset)
    assert isinstance(results, datetime)
    assert is_timezone_aware(results)
    assert results.tzinfo.utcoffset(results) == expected_timedelta_offset  # pyright: ignore


def test_parse_datetime_tz_fallback_input_datetime_not_timezone_aware():
    results = parse_datetime_tz_fallback(value=datetime(2020, 3, 3, 2, 4, 52))
    assert isinstance(results, datetime)
    assert is_timezone_aware(results)
    assert results.tzinfo == ZoneInfo("UTC")


def test_parse_datetime_tz_fallback_non_utc_fallback():
    zone = ZoneInfo("US/Eastern")
    results = parse_datetime_tz_fallback(value=datetime(2020, 3, 3, 2, 4, 52), fallback_timezone=zone)
    assert isinstance(results, datetime)
    assert is_timezone_aware(results)
    assert results.tzinfo == zone


def test_parse_datetime_tz_fallback_input_datetime_timezone_aware_utc():
    results = parse_datetime_tz_fallback(value=datetime(2020, 3, 3, 2, 4, 52, tzinfo=timezone.utc))
    assert isinstance(results, datetime)
    assert is_timezone_aware(results)
    assert results.tzinfo == timezone.utc


def test_parse_datetime_tz_fallback_input_datetime_timezone_aware_non_utc_timezone():
    zone = ZoneInfo("US/Eastern")
    results = parse_datetime_tz_fallback(value=datetime(2020, 3, 3, 2, 4, 52, tzinfo=zone))
    assert isinstance(results, datetime)
    assert is_timezone_aware(results)
    assert results.tzinfo == zone
