from itertools import chain
from uuid import UUID, uuid4

from pydantic import Field
from pympler.asizeof import asizeof

from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.application.utils.size_splitter import SizeSplitter
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.document_base import Document


class TestSizeSplitter:
    class _ContentDocument(Document):
        @classmethod
        def type_id(cls) -> DataType:
            raise NotImplementedError()

        id: UUID = Field(default_factory=uuid4)
        content: str

    _SIZE_KB = 1024
    _BULK_SIZE = 5 * _SIZE_KB

    def test_size_splitter_passes(self):
        bulk_size = PrimitiveTypesGenerator.generate_random_int(min_value=5, max_value=10) * self._SIZE_KB
        doc_count = PrimitiveTypesGenerator.generate_random_int(min_value=1, max_value=50)
        docs = [self._ContentDocument(content="a" * self._SIZE_KB) for _ in range(doc_count)]

        chunks = list(SizeSplitter.split(docs, bulk_size=bulk_size))
        for chunk in chunks:
            expected_size = asizeof(chunk)
            assert expected_size <= bulk_size

        assert len(list(chain.from_iterable(chunks))) == doc_count
