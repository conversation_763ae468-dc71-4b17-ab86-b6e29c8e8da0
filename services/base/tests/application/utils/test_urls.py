from services.base.application.utils.urls import join_as_url


def test_join_as_url_no_input_params():
    expected_output = "base"
    output = join_as_url(base_url="base", query_params={})

    assert output == expected_output


def test_join_as_url_with_none_and_number():
    input_data = {
        "p1": "a",
        "p2": None,
        "p3": 1,
    }
    expected_output = "base?p1=a&p3=1"

    output = join_as_url(base_url="base", query_params=input_data)

    assert output == expected_output


def test_join_as_url_with_list_of_strings():
    input_data = {
        "p1": "a",
        "p2": ["b", "c", "d"],
    }
    expected_output = "base?p1=a&p2=b&p2=c&p2=d"

    output = join_as_url(base_url="base", query_params=input_data)

    assert output == expected_output
