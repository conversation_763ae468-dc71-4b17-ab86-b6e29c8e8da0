import time
from datetime import datetime, timedelta, timezone
from unittest.mock import AsyncMock, MagicMock
from uuid import UUID, uuid4

import pytest
from starlette import status
from starlette.responses import Response

from services.base.api.authentication.auth_guard import AuthGuard, get_current_uuid_or_none
from services.base.api.authentication.exceptions import (
    AuthorizationErrorMessages,
    ExpiredCredentialsException,
    InvalidCredentialsException,
    InvalidRefreshTokenException,
    MissingAccessTokenException,
    MissingRefreshTokenException,
)
from services.base.api.authentication.token_handling import (
    generate_access_token,
    generate_refresh_token,
    get_uuid_from_token,
)
from services.base.application.constants import UserTokenKeys
from services.base.application.exceptions import BadRequestException
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.infrastructure.database.sql_alchemy.repository.sql_alch_member_user_repository import (
    SqlAlchMemberUserRepository,
)


@pytest.fixture(scope="module")
def mock_orm():
    SqlAlchMemberUserRepository.get_by_uuid = AsyncMock()
    SqlAlchMemberUserRepository.insert_or_update = AsyncMock()


@pytest.fixture(scope="module")
def auth_guard() -> AuthGuard:
    return AuthGuard()


async def test_create_authorization_response_should_rotate(mock_orm, mock_demo1_member_user: MemberUser, auth_guard):
    # Arrange
    refresh_expiration = float((datetime.now(timezone.utc) + timedelta(hours=5)).timestamp())
    # Act - should rotate the refresh token
    response = await auth_guard.create_authorization_response(
        user=mock_demo1_member_user,
        refresh_expiration_timestamp=refresh_expiration,
        response=Response(),
    )
    # Assert
    assert response.headers[UserTokenKeys.API_ACCESS_TOKEN_HEADER] is not None
    assert response.headers["set-cookie"] is not None
    assert response.status_code == status.HTTP_200_OK


async def test_create_authorization_response_should_not_rotate(
    mock_orm, mock_demo1_member_user: MemberUser, auth_guard
):
    # Arrange
    refresh_expiration = float((datetime.now(timezone.utc) + timedelta(days=30)).timestamp())
    # Act - long living refresh token present, should not be rotated
    response = await auth_guard.create_authorization_response(
        user=mock_demo1_member_user,
        refresh_expiration_timestamp=refresh_expiration,
        response=Response(),
    )
    # Assert
    assert response.headers[UserTokenKeys.API_ACCESS_TOKEN_HEADER] is not None
    assert response.headers.get("set-cookie") is None


def test_validate_refresh_token_valid_token_should_pass(auth_guard):
    # Arrange
    user_uuid = uuid4()
    api_refresh_token = generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10))
    bearer_token = generate_access_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10))
    # Act
    now = datetime.now(timezone.utc).timestamp()
    token, token_from_bearer = auth_guard.validate_refresh_token(
        api_refresh_token=api_refresh_token
    ), auth_guard.validate_refresh_token(api_refresh_token=api_refresh_token, bearer_token=bearer_token)
    # Assert
    for token in (token, token_from_bearer):
        assert int(token[UserTokenKeys.EXPIRATION_TIME]) >= now
        assert int(token[UserTokenKeys.ISSUED_AT]) <= now
        assert get_uuid_from_token(token) == user_uuid


async def test_update_last_logged_at(mock_orm, mock_demo1_member_user: MemberUser, auth_guard):
    # Arrange
    # Act
    now = datetime.now(timezone.utc)
    await auth_guard.update_last_logged_at(user=mock_demo1_member_user)
    # Assert
    assert auth_guard._member_user_repo.insert_or_update.await_args.kwargs["user"].last_logged_at >= now


def test_validate_refresh_token_concatenated_valid_token_should_pass(auth_guard):
    """Might be a temporary issue, however at this point, iOS clients seems to sometimes concatenate refresh cookie
    in format of either "token,api_refresh_token=token" or "api_refresh_token=token,token".
    Auth guard should be able this cookie concatenation if the actual token is valid"""
    # Arrange
    user_uuid = uuid4()
    valid_refresh_token = generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10))
    # First possible format
    concatenated_refresh_token = f"{valid_refresh_token},{UserTokenKeys.API_REFRESH_TOKEN}={valid_refresh_token}"
    # Second possible format
    concatenated_refresh_token_reversed = (
        f"{UserTokenKeys.API_REFRESH_TOKEN}={valid_refresh_token},{valid_refresh_token}"
    )
    now = datetime.now(timezone.utc).timestamp()

    # Act & Assert that both tokens are parsed
    token = auth_guard.validate_refresh_token(api_refresh_token=concatenated_refresh_token)
    assert int(token[UserTokenKeys.EXPIRATION_TIME]) >= now
    assert int(token[UserTokenKeys.ISSUED_AT]) <= now
    assert get_uuid_from_token(token) == user_uuid

    token = auth_guard.validate_refresh_token(api_refresh_token=concatenated_refresh_token_reversed)
    assert int(token[UserTokenKeys.EXPIRATION_TIME]) >= now
    assert int(token[UserTokenKeys.ISSUED_AT]) <= now
    assert get_uuid_from_token(token) == user_uuid


def test_validate_refresh_token_expired_token_should_raise(auth_guard):
    # Arrange
    user_uuid = uuid4()
    expired_refresh_token = generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(minutes=-10))
    # Act & Assert
    with pytest.raises(ExpiredCredentialsException) as exception:
        auth_guard.validate_refresh_token(api_refresh_token=expired_refresh_token)
        assert AuthorizationErrorMessages.EXPIRED_CREDENTIALS in exception.value.message


async def test_validate_request_authorization_valid_tokens(auth_guard):
    # Arrange
    user_uuid = uuid4()
    valid_refresh_token = generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10))
    valid_access_token = generate_access_token(user_uuid=user_uuid, time_delta=timedelta(minutes=1))
    # Act & Assert
    user_uuid = await auth_guard.get_tenant_uuid_from_request(
        response=Response(),
        api_refresh_token=valid_refresh_token,
        bearer_token=valid_access_token,
    )
    assert user_uuid
    assert isinstance(user_uuid, UUID)


async def test_validate_request_authorization_missing_access_token_should_raise(auth_guard):
    # Arrange
    user_uuid = uuid4()
    valid_refresh_token = generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10))
    # Act & Assert
    with pytest.raises(MissingAccessTokenException) as exception:
        await auth_guard.get_tenant_uuid_from_request(
            response=Response(),
            api_refresh_token=valid_refresh_token,
            bearer_token=None,
        )
        assert AuthorizationErrorMessages.MISSING_ACCESS_TOKEN in exception.value.message


def test_validate_refresh_token_invalid_token_should_raise(auth_guard):
    # Arrange
    user_uuid = uuid4()
    invalid_refresh_token = generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10)) + "test"
    # Act & Assert
    with pytest.raises(InvalidRefreshTokenException) as exception:
        auth_guard.validate_refresh_token(api_refresh_token=invalid_refresh_token)

        assert AuthorizationErrorMessages.INVALID_REFRESH_TOKEN in exception.value.message


def test_validate_refresh_token_missing_token_should_raise(auth_guard):
    # Arrange
    missing_refresh_token = ""
    # Act & Assert
    with pytest.raises(MissingRefreshTokenException) as exception:
        auth_guard.validate_refresh_token(api_refresh_token=missing_refresh_token)

        assert AuthorizationErrorMessages.MISSING_REFRESH_TOKEN in exception.value.message


def test_validate_refresh_token_different_bearer_should_raise(auth_guard):
    # Arrange
    user_uuid = uuid4()
    valid_refresh_token = generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10))
    bearer_token = generate_access_token(user_uuid=uuid4(), time_delta=timedelta(minutes=10))
    # Act & Assert
    with pytest.raises(InvalidCredentialsException) as exception:
        auth_guard.validate_refresh_token(api_refresh_token=valid_refresh_token, bearer_token=bearer_token)
        assert AuthorizationErrorMessages.INVALID_ACCESS_TOKEN in exception.value.message


def test_validate_refresh_token_older_bearer_should_raise(auth_guard):
    # Arrange
    user_uuid = uuid4()
    bearer_token = generate_access_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10))
    time.sleep(1)
    valid_refresh_token = generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10))
    # Act & Assert
    with pytest.raises(InvalidCredentialsException) as exception:
        auth_guard.validate_refresh_token(api_refresh_token=valid_refresh_token, bearer_token=bearer_token)
        assert AuthorizationErrorMessages.INVALID_ACCESS_TOKEN in exception.value.message


@pytest.mark.parametrize(
    "headers",
    [
        {"Authorization": "Bearer "},
        {"Authorization": "null"},
        {"Authorization": ""},
        {"Authorization": None},
    ],
)
async def test_get_current_uuid_optional_user_not_signed_in(headers):
    request = MagicMock()
    request.cookies = {}
    request.headers = headers

    with pytest.raises(BadRequestException):
        await get_current_uuid_or_none(request=request, response=Response())


async def test_get_current_uuid_optional_returns_none():
    request = MagicMock()
    request.cookies = {}
    request.headers = {}
    assert await get_current_uuid_or_none(request=request, response=Response()) is None


async def test_get_current_uuid_optional_user_signed_in():
    # Arrange
    user_uuid = uuid4()
    request = MagicMock()
    valid_token = generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10))
    request.cookies = {UserTokenKeys.API_REFRESH_TOKEN: valid_token}
    bearer_token = generate_access_token(user_uuid=user_uuid, time_delta=timedelta(minutes=10))
    request.headers = {"Authorization": f"bearer {bearer_token}"}
    # Act
    uuid = await get_current_uuid_or_none(request=request, response=Response())
    # Assert
    assert isinstance(uuid, UUID)


async def test_get_current_uuid_optional_user_expired():
    # Arrange
    user_uuid = uuid4()
    request = MagicMock()
    expired_token = generate_refresh_token(user_uuid=user_uuid, time_delta=timedelta(minutes=-10))
    request.cookies = {UserTokenKeys.API_REFRESH_TOKEN: expired_token}
    # Act
    uuid = await get_current_uuid_or_none(request=request, response=Response())
    # Assert
    assert not uuid


async def test_validate_expired_datetime_expiration_not_set(mock_demo1_member_user: MemberUser, auth_guard):
    # Arrange
    token_timestamp = int(datetime.now(timezone.utc).timestamp())
    # Act # Assert
    auth_guard.validate_expired_datetime(user=mock_demo1_member_user, token_issued_at_timestamp=token_timestamp)


async def test_validate_expired_datetime_expiration_set_should_raise(mock_demo1_member_user: MemberUser, auth_guard):
    # Arrange
    token_timestamp = int(datetime.now(timezone.utc).timestamp())
    mock_demo1_member_user.expiration_datetime = datetime.now(timezone.utc)
    # Act & Assert
    with pytest.raises(ExpiredCredentialsException) as exception:
        auth_guard.validate_expired_datetime(user=mock_demo1_member_user, token_issued_at_timestamp=token_timestamp)
        assert AuthorizationErrorMessages.EXPIRED_CREDENTIALS in exception.value.message
