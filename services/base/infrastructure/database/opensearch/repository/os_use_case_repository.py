import logging
from datetime import datetime, timezone
from typing import Optional, Sequence
from uuid import UUID

from opensearchpy import Async<PERSON>penSearch
from pydantic import ValidationError

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.enums.bulk_operation import BulkOperation
from services.base.application.database.models.sorts import CommonSorts, Sort
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.repository.use_case_repository import UseCaseRepository
from services.base.domain.schemas.events.use_case import UseCase
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.infrastructure.database.opensearch.opensearch_index_constants import USE_CASE_INDEX
from services.base.infrastructure.database.opensearch.repository.os_response_parser import OSResponseParser


class OSUseCaseRepository(UseCaseRepository):

    def __init__(self, client: AsyncOpenSearch, search_service: DocumentSearchService):
        self._os_client: AsyncOpenSearch = client
        self._search_service: DocumentSearchService = search_service

    async def insert(self, use_cases: Sequence[UseCase], force_strong_consistency: bool = False) -> Sequence[UseCase]:
        insert_requests: list[dict] = []

        for use_case in use_cases:
            action = {BulkOperation.Create.value: {"_index": USE_CASE_INDEX, "_id": str(use_case.id)}}
            insert_requests.append(action)
            insert_requests.append(
                use_case.model_dump(exclude={"id"})
                | {DocumentLabels.TAGS: [{DocumentLabels.TAG: tag} for tag in use_case.tags]}
            )

        refresh = "wait_for" if force_strong_consistency else "false"
        bulk_response = await self._os_client.bulk(body=insert_requests, refresh=refresh)  # pyright: ignore
        if bulk_response["errors"]:
            logging.error(f"Error inserting use cases Response: {bulk_response}")
        ids = await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=bulk_response, action=BulkOperation.Create
        )

        return await self.search_by_id(ids=ids)

    async def update(self, use_cases: Sequence[UseCase]) -> Sequence[UseCase]:
        update_requests: list[dict] = []
        for use_case in use_cases:
            use_case.system_properties.updated_at = datetime.now(timezone.utc)
            action = {
                BulkOperation.Update.value: {"_index": USE_CASE_INDEX, "_id": str(use_case.id), "retry_on_conflict": 2}
            }
            request = {
                "doc": use_case.model_dump(by_alias=True, exclude={"id", "created_at"})
                | {DocumentLabels.TAGS: [{DocumentLabels.TAG: tag} for tag in use_case.tags]}
            }

            update_requests.append(action)
            update_requests.append(request)

        bulk_response = await self._os_client.bulk(body=update_requests)
        if bulk_response["errors"]:
            logging.error(f"Error updating use cases Response: {bulk_response}")
        ids = await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=bulk_response, action=BulkOperation.Update
        )
        return await self.search_by_id(ids=ids)

    async def search_by_id(self, ids: Sequence[UUID]) -> Sequence[UseCase]:
        response = await self._os_client.mget(body={"ids": ids}, index=USE_CASE_INDEX)
        result: list = []
        for doc in response["docs"]:
            if err := doc.get("error"):
                logging.error(f"Mget search error: {err["reason"]}, id: {doc["_id"]}")
            if doc["found"]:
                source = doc["_source"]
                tags = source[DocumentLabels.TAGS]
                try:
                    result.append(
                        UseCase(
                            **source | {DocumentLabels.TAGS: [tag[DocumentLabels.TAG] for tag in tags]}, id=doc["_id"]
                        )
                    )
                except ValidationError as err:
                    logging.error(
                        "failed to deserialize use case",
                        extra={
                            "document_id": doc["_id"],
                            "index": doc["_index"],
                            "source": source,
                            "error": str(err),
                        },
                    )

        return result

    async def delete_by_id(self, ids: Sequence[UUID]) -> Sequence[UUID]:
        delete_requests: list[dict] = []
        for doc_id in ids:
            request = {BulkOperation.Delete.value: {"_index": USE_CASE_INDEX, "_id": str(doc_id)}}
            delete_requests.append(request)

        bulk_response = await self._os_client.bulk(body=delete_requests)
        if bulk_response["errors"]:
            logging.error(f"There were errors during insertion of events. Response: {bulk_response}")
        return await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=bulk_response, action=BulkOperation.Delete
        )

    async def search_by_query(
        self,
        query: SingleDocumentTypeQuery[UseCase],
        size: int = 1000,
        sorts: Optional[Sequence[Sort]] = None,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[UseCase]:
        sorts = sorts if sorts else CommonSorts.created_at_and_internal_id()
        return await self._search_service.search_documents_by_single_query(
            query=query, sorts=sorts, size=size, continuation_token=continuation_token
        )
