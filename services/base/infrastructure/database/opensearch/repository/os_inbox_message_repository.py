from datetime import datetime, timezone
from typing import Optional, Sequence
from uuid import UUID

from opensearchpy import AsyncOpenSearch, OpenSearchException

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.enums.bulk_operation import BulkOperation
from services.base.application.database.models.sorts import CommonSorts, Sort
from services.base.application.retry import retry
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.inbox_message_repository import InboxMessageRepository
from services.base.domain.repository.models.search_results import SearchResults
from services.base.domain.schemas.inbox.inbox_message import InboxMessage
from services.base.domain.schemas.query.single_document_type_query import SingleDocumentTypeQuery
from services.base.infrastructure.database.opensearch.opensearch_index_constants import INBOX_MESSAGE_INDEX
from services.base.infrastructure.database.opensearch.repository.os_response_parser import OSResponseParser


class OSInboxMessageRepository(InboxMessageRepository):

    def __init__(self, client: AsyncOpenSearch, search_service: DocumentSearchService):
        self._os_client = client
        self._search_service: DocumentSearchService = search_service

    async def insert(
        self, messages: Sequence[InboxMessage], force_strong_consistency: bool = False
    ) -> Sequence[InboxMessage]:
        insert_requests: list[dict] = []

        for msg in messages:
            action = {BulkOperation.Create.value: {"_index": INBOX_MESSAGE_INDEX, "_id": str(msg.id)}}
            insert_requests.append(action)
            insert_requests.append(msg.model_dump(exclude={DocumentLabels.ID}))
        refresh = "wait_for" if force_strong_consistency else "false"
        bulk_response = await self._os_client.bulk(body=insert_requests, refresh=refresh)  # pyright: ignore
        # TODO: what if response contains errors?
        ids = await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=bulk_response, action=BulkOperation.Create
        )

        return await self.search_by_id(ids=ids)

    async def update(self, messages: Sequence[InboxMessage]) -> Sequence[InboxMessage]:
        update_requests: list[dict] = []
        for inbox_message in messages:
            inbox_message.system_properties.updated_at = datetime.now(timezone.utc)
            action = {
                BulkOperation.Update.value: {
                    "_index": INBOX_MESSAGE_INDEX,
                    "_id": str(inbox_message.id),
                    "retry_on_conflict": 2,
                }
            }
            request = {
                "doc": {
                    **inbox_message.model_dump(
                        by_alias=True,
                        exclude={
                            DocumentLabels.ID: True,
                            DocumentLabels.SYSTEM_PROPERTIES: {DocumentLabels.CREATED_AT, DocumentLabels.DELETED_AT},
                        },
                    )
                }
            }

            update_requests.append(action)
            update_requests.append(request)

        bulk_response = await self._os_client.bulk(body=update_requests)
        ids = await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=bulk_response, action=BulkOperation.Update
        )
        return await self.search_by_id(ids=ids)

    async def search_by_id(self, ids: Sequence[UUID]) -> Sequence[InboxMessage]:
        response = await self._os_client.mget(body={"ids": ids}, index=INBOX_MESSAGE_INDEX)
        return await OSResponseParser.to_domain_from_mget(data_schema=InboxMessage, response=response)

    async def delete_by_id(self, ids: Sequence[UUID]) -> Sequence[UUID]:
        delete_requests: list[dict] = []
        for message_uuid in ids:
            request = {BulkOperation.Delete.value: {"_index": INBOX_MESSAGE_INDEX, "_id": str(message_uuid)}}

            delete_requests.append(request)

        delete_result = await self._os_client.bulk(body=delete_requests)
        return await OSResponseParser.get_doc_ids_from_bulk_response(
            bulk_response=delete_result, action=BulkOperation.Delete
        )

    @retry(exceptions=OpenSearchException)
    async def search_by_query(
        self,
        size: int,
        query: SingleDocumentTypeQuery[InboxMessage],
        sorts: Optional[Sequence[Sort]] = None,
        continuation_token: Optional[str] = None,
    ) -> SearchResults[InboxMessage]:
        sorts = sorts if sorts else CommonSorts.created_at_and_internal_id()
        return await self._search_service.search_documents_by_single_query(
            query=query, size=size, continuation_token=continuation_token, sorts=sorts
        )
