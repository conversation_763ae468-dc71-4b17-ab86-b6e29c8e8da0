from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.query.boolean_query import BooleanQuery
from services.base.domain.schemas.query.leaf_query import <PERSON><PERSON><PERSON><PERSON>, PatternQuery, ValuesQuery


class QueryUtil:
    @staticmethod
    def is_must_clause_required(leaf_query: LeafQuery) -> bool:
        return isinstance(leaf_query, PatternQuery)

    @staticmethod
    def index_values_query(index_name: str) -> ValuesQuery:
        return ValuesQuery(field_name="_index", values=[index_name])

    @staticmethod
    def contains_match_query(boolean_query: BooleanQuery) -> bool:
        for query in boolean_query.queries:
            if isinstance(query, LeafQuery):
                if isinstance(query, PatternQuery):
                    return True
            elif isinstance(query, BooleanQuery):
                return QueryUtil.contains_match_query(boolean_query=query)
            else:
                raise ShouldNotReachHereException(f"Unexpected query type {type(query)}")

        return False
