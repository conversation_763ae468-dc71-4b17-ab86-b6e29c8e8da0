from typing import Sequence

from pydantic import Field

from services.base.domain.exceptions.should_not_reach_here_exception import ShouldNotReachHereException
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.boolean_query import BooleanQuery
from services.base.domain.schemas.query.leaf_query import Leaf<PERSON>uery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.shared import BaseDataModel
from services.base.infrastructure.database.opensearch.opensearch_mappings import (
    DataSchemaToIndexModelMapping,
)
from services.base.infrastructure.database.opensearch.query_translator.boolean_query_refiner import BooleanQueryRefiner
from services.base.infrastructure.database.opensearch.query_translator.boolean_query_translator import (
    BooleanQueryTranslator,
)
from services.base.infrastructure.database.opensearch.query_translator.leaf_query_refiner import LeafQueryRefiner
from services.base.infrastructure.database.opensearch.query_translator.leaf_query_translator import (
    LeafQueryTranslator,
)
from services.base.infrastructure.database.opensearch.query_translator.query_refiner import QueryRefiner
from services.base.infrastructure.database.opensearch.query_translator.query_util import QueryUtil


class QueryTranslationResult(BaseDataModel):
    indices: Sequence[str] = Field(..., min_length=1)
    query_as_dict: dict | None = None


class QueryTranslator:

    @staticmethod
    def translate(query: Query) -> QueryTranslationResult:
        parsed_query = QueryRefiner.refine(query=query)
        type_queries = parsed_query.type_queries
        indices = QueryTranslator._get_indices(parsed_query)

        # if all the type queries doesn't have query we filter only based on the data type
        if QueryTranslator._are_all_type_queries_empty(type_queries=type_queries):
            query_as_dict = None
        elif len(type_queries) == 1:
            query_as_dict = QueryTranslator._translate_single_type_query(
                query=type_queries[0].query, domain_type=type_queries[0].domain_types[0]
            )
        else:
            query_as_dict = QueryTranslator._translate_type_queries(type_queries)

        return QueryTranslationResult(indices=indices, query_as_dict=query_as_dict)

    @staticmethod
    def _get_indices(query: Query) -> Sequence[str]:
        indices: list[str] = []
        for type_query in query.type_queries:
            for domain_type in type_query.domain_types:
                idx = DataSchemaToIndexModelMapping[domain_type].name + "*"
                if idx not in indices:
                    indices.append(idx)
        return indices

    @staticmethod
    def _translate_type_queries(type_queries: Sequence[TypeQuery]) -> dict:
        result = {"query": {"bool": {"should": []}}}

        for type_query in type_queries:
            for domain_type in type_query.domain_types:
                if type_query.query is None:
                    value_index_query_dict = QueryTranslator._create_index_value_query_as_dict(domain_type=domain_type)
                    wrapped_query = {"bool": {"filter": [value_index_query_dict]}}
                    result["query"]["bool"]["should"].append(wrapped_query)
                else:
                    if isinstance(type_query.query, BooleanQuery):
                        boolean_query_as_dict = QueryTranslator._translate_boolean_query(
                            boolean_query=type_query.query, domain_type=domain_type
                        )
                        result["query"]["bool"]["should"].append(boolean_query_as_dict)
                    elif isinstance(type_query.query, LeafQuery):
                        leaf_query_as_dict = QueryTranslator._translate_leaf_query(
                            leaf_query=type_query.query, domain_type=domain_type
                        )
                        result["query"]["bool"]["should"].append(leaf_query_as_dict)
                    else:
                        raise ShouldNotReachHereException("Unexpected query type")

        return result

    @staticmethod
    def _are_all_type_queries_empty(type_queries: Sequence[TypeQuery]) -> bool:
        """
        Check if all type queries have query equals to None
        """
        for type_query in type_queries:
            if type_query.query is not None:
                return False
        return True

    @staticmethod
    def _translate_boolean_query(boolean_query: BooleanQuery, domain_type: type[Document]) -> dict:
        refined_boolean_query = BooleanQueryRefiner.refine(boolean_query=boolean_query, domain_type=domain_type)
        query_as_dict = BooleanQueryTranslator.translate(boolean_query=refined_boolean_query)
        QueryTranslator._add_index_value_to_query_dict(query_dict=query_as_dict, domain_type=domain_type)

        return query_as_dict

    @staticmethod
    def _translate_leaf_query(leaf_query: LeafQuery, domain_type: type[Document]) -> dict:
        refined_leaf_query = LeafQueryRefiner.refine(leaf_query=leaf_query, domain_type=domain_type)
        query_as_dict = LeafQueryTranslator.translate(leaf_query=refined_leaf_query)
        if QueryUtil.is_must_clause_required(refined_leaf_query):
            wrapped_query_as_dict = {"bool": {"must": [query_as_dict]}}
        else:
            wrapped_query_as_dict = {"bool": {"filter": [query_as_dict]}}

        QueryTranslator._add_index_value_to_query_dict(query_dict=wrapped_query_as_dict, domain_type=domain_type)

        return wrapped_query_as_dict

    @staticmethod
    def _add_index_value_to_query_dict(query_dict: dict, domain_type: type[Document]) -> None:
        index_value_query_as_dict = QueryTranslator._create_index_value_query_as_dict(domain_type=domain_type)
        if "filter" in query_dict["bool"]:
            query_dict["bool"]["filter"].append(index_value_query_as_dict)
        else:
            query_dict["bool"].update({"filter": [index_value_query_as_dict]})

    @staticmethod
    def _create_index_value_query_as_dict(domain_type: type[Document]) -> dict:
        index = DataSchemaToIndexModelMapping[domain_type].name + "*"
        index_values_query = QueryUtil.index_values_query(index_name=index)
        return LeafQueryTranslator.translate(leaf_query=index_values_query)

    @staticmethod
    def _translate_single_type_query(query: LeafQuery | BooleanQuery | None, domain_type: type[Document]) -> dict:
        if isinstance(query, LeafQuery):
            refined_leaf_query = LeafQueryRefiner.refine(leaf_query=query, domain_type=domain_type)
            query_as_dict = LeafQueryTranslator.translate(leaf_query=refined_leaf_query)
        elif isinstance(query, BooleanQuery):
            refined_bool_query = BooleanQueryRefiner.refine(boolean_query=query, domain_type=domain_type)
            query_as_dict = BooleanQueryTranslator.translate(boolean_query=refined_bool_query)
        else:
            raise ShouldNotReachHereException(f"Unexpected query type: {query}. Expected LeafQuery or BooleanQuery")

        return {"query": query_as_dict}
