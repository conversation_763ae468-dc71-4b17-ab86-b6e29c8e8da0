from __future__ import annotations

from datetime import datetime
from typing import Optional, Sequence
from zoneinfo import ZoneInfo

from services.base.application.database.models.sorts import Sort
from services.base.domain.schemas.query.aggregations import FieldAggregation
from services.base.infrastructure.database.opensearch.opensearch_utils import OpenSearchUtils
from services.base.infrastructure.database.opensearch.query_methods.utils import (
    get_str_timezone_offset,
)


class OpenSearchAggsBuilder:
    def __init__(self):
        self._aggs = {}

    def build(self):
        return self._aggs

    def with_unique_aggregation(
        self, field_name: str, size: int = 10000, sort: Optional[Sequence[Sort]] = None
    ) -> OpenSearchAggsBuilder:
        top_hits = {"_source": True, "size": 1}
        if sort:
            top_hits = top_hits | {
                "sort": [{sort.name: {"order": OpenSearchUtils.map_sort_order(sort.order)}} for sort in sort]
            }
        aggregation = {
            "aggregation": {
                "terms": {"field": field_name, "size": size},
                "aggs": {"sub_aggregation": {"top_hits": top_hits}},
            }
        }

        self._aggs = self._aggs | aggregation

        return self

    def with_frequency_distribution_agg(self, field_name: str, size: int = 1000) -> OpenSearchAggsBuilder:
        aggregation = {
            "aggregation": {
                "terms": {"field": field_name, "size": size},
            }
        }

        self._aggs = self._aggs | aggregation
        return self

    def with_cardinality_aggregation(self, field_name: str) -> OpenSearchAggsBuilder:
        aggregation = {
            "cardinality_aggregation": {
                "cardinality": {"field": field_name},
            }
        }

        self._aggs = self._aggs | aggregation
        return self

    def with_date_histogram_agg(
        self,
        field: str,
        histogram_field_aggregations: dict,
        interval: str,
        timezone: ZoneInfo,
    ) -> OpenSearchAggsBuilder:
        histogram_type = "calendar_interval" if int(interval[:-1]) == 1 else "fixed_interval"
        aggregation = {
            "requested_histogram": {
                "date_histogram": {
                    "field": field,
                    histogram_type: interval,
                    "time_zone": get_str_timezone_offset(dt=datetime.now(tz=timezone)),
                },
            }
        }
        if histogram_field_aggregations:
            aggregation["requested_histogram"]["aggs"] = histogram_field_aggregations

        self._aggs = self._aggs | aggregation
        return self

    def with_field_aggregations(self, field_aggs: Sequence[FieldAggregation]):
        for field_agg in field_aggs:
            method = field_agg.aggregation_method
            field = field_agg.field_name
            if method:
                agg = {f"{method}_{field}": {method: {"field": field}}}
                self._aggs = self._aggs | agg
        return self
