import asyncio
import logging

from opensearchpy import Async<PERSON>penSearch

from services.base.dependency_bootstrapper import bootstrapper
from services.base.infrastructure.database.opensearch.index_settings.extension import ExtensionResultsIndexModel
from services.base.infrastructure.database.opensearch.migrations.migration_wrapper import OSMigrationWrapper
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    OpenSearchIndex,
)

logger = logging.getLogger(__name__)
logger.setLevel(level=logging.INFO)


async def reindex_extension_results(client: AsyncOpenSearch, index_model: OpenSearchIndex, dry_run: bool):
    indices = await client.indices.get(index=index_model.name + "*")

    for index_name in indices:
        count = (await client.count(index=index_name + "*"))["count"]
        script = {
            "lang": "painless",
            "source": """
                if (ctx._source.type == 'run') {
                    ctx._source.type = 'extension_run';
                    ctx._source.join_field.name = 'extension_run';
                } else if (ctx._source.type == 'result') {
                    ctx._source.type = 'extension_result';
                    ctx._source.join_field.name = 'extension_result';
                }
            """,
        }

        logger.info(f"Reindexing {count} documents from {index_name}")

        if dry_run:
            dummy_index = f"dummy-{index_name}"
            await OSMigrationWrapper._re_create_index_with_mappings(
                index_model=index_model,
                target_index_name=dummy_index,
                client=client,
            )
            await OSMigrationWrapper.reindex_index_to(
                source=index_name,
                destination=dummy_index,
                client=client,
                script=script,
                requests_per_second=None,
                request_timeout=3600,
                pipeline=None,
            )

            new_count = (await client.count(index=dummy_index))["count"]

            logger.info(
                f"[DRY RUN] reindexed {new_count} documents from {index_name} to {dummy_index}\n"
                f"-------------\n"
                f"COUNT_DIFF: {count - new_count}"
            )

            result = await client.indices.delete(index=dummy_index)
            logger.info(f"[DRY RUN] removed index {dummy_index} with result: {result}")
        else:
            await OSMigrationWrapper.reindex_index_through_dummy_index(
                index_name=index_name,
                index_model=index_model,
                client=client,
                script=script,
                pipeline=None,
            )

            new_count = (await client.count(index=index_name))["count"]
            logger.info(f"reindexed {new_count} documents\n" f"-------------\n" f"COUNT_DIFF: {count - new_count}")


async def migrate(dry_run: bool):
    client = bootstrapper.get(interface=AsyncOpenSearch)
    try:
        await reindex_extension_results(client=client, index_model=ExtensionResultsIndexModel, dry_run=dry_run)
    finally:
        await client.close()


if __name__ == "__main__":
    asyncio.run(migrate(dry_run=True))
