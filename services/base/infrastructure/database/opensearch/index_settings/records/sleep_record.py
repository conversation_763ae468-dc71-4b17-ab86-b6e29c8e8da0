from typing import Any, Dict

from opensearchpy import Keyword

from services.base.domain.schemas.records.sleep_record import SleepRecordFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import (
    get_base_record_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    get_default_event_index_rollover_conditions,
    get_default_event_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    SLEEP_RECORD_INDEX,
    OpenSearchIndex,
)


def get_sleep_record_mapping() -> Dict[str, Any]:
    sleep_record_mapping = {
        SleepRecordFields.STAGE: Keyword(copy_to=OS_LABEL_CATCH_ALL),
    }
    return convert_dsl_mapping_to_dict(sleep_record_mapping | get_base_record_mapping(), strict_mapping=True)


def get_event_settings():
    return {
        "default_pipeline": None,
        **get_default_event_index_settings(),
        "plugins.index_state_management.rollover_alias": SLEEP_RECORD_INDEX,
    }


SleepRecordIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=SLEEP_RECORD_INDEX,
    mappings=get_sleep_record_mapping(),
    settings=get_event_settings(),
    rollover_conditions=get_default_event_index_rollover_conditions(),
    aliases=[SLEEP_RECORD_INDEX],
)
