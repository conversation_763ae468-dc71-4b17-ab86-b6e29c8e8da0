from opensearchpy import Date, Object

from services.base.domain.constants.document_labels import DocumentLabels


def get_system_properties_mapping():
    return {
        DocumentLabels.SYSTEM_PROPERTIES: Object(
            properties={
                DocumentLabels.CREATED_AT: Date(),
                DocumentLabels.UPDATED_AT: Date(),
                DocumentLabels.DELETED_AT: Date(),
            }
        )
    }
