from typing import Any, Dict

import opensearchpy


def convert_dsl_mapping_to_dict(custom_mapping: Dict[str, Any], strict_mapping: bool = False) -> dict:
    """Expects custom defined mapping - dictionary with string keys and elasticsearch-dsl type values.
    Returns back es compatible dict mapping."""
    mapping = opensearchpy.Mapping()
    for key, value in custom_mapping.items():
        mapping.field(key, value)
    if strict_mapping:
        mapping.meta(name="dynamic", params="strict")
    return mapping.to_dict()
