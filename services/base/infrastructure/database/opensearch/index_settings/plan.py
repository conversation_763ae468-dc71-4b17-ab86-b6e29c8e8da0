from typing import Any, Dict

from opensearchpy import <PERSON><PERSON><PERSON>, Date, Float, Integer, Keyword, Object, Text

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.schemas.plan.goal import GoalConditionFields, GoalFields
from services.base.domain.schemas.plan.plan import PlanFields
from services.base.domain.schemas.plan.plan_base import StreakFields
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_document_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    PLAN_INDEX,
    OpenSearchIndex,
)
from settings.app_config import settings


def get_plan_mapping() -> Dict[str, Any]:
    metadata_mapping = {
        DocumentLabels.METADATA: Object(
            properties={
                DocumentLabels.ORGANIZATION: Keyword(copy_to=OS_LABEL_CATCH_ALL),
            }
        ),
    }
    streak_mapping = {
        PlanFields.STREAK: Object(
            properties={
                StreakFields.STREAK: Integer(),
                StreakFields.LONGEST_STREAK: Integer(),
            }
        ),
    }
    plan_mapping = {
        PlanFields.TOTAL_COMPLETED_ON_TIME: Integer(),
        PlanFields.PRIORITY: Integer(),
        PlanFields.FIRST_COMPLETED_AT: Date(),
        PlanFields.PROMPT: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL),
        PlanFields.IS_URGENT: Boolean(),
        PlanFields.IS_CONFIRMATION_REQUIRED: Boolean(),
        PlanFields.IS_ABSOLUTE_SCHEDULE: Boolean(),
    }
    goal_mapping = {
        GoalFields.CURRENT_VALUE: Float(),
        GoalFields.IS_ACHIEVED: Boolean(),
        GoalFields.CONDITION: Object(
            properties={
                GoalConditionFields.GTE: Float(),
                GoalConditionFields.LTE: Float(),
                GoalConditionFields.FIELD_NAME: Text(),
                GoalConditionFields.AGG_METHOD: Text(),
                GoalConditionFields.QUERY: Text(),
            }
        ),
    }
    return convert_dsl_mapping_to_dict(
        {
            DocumentLabels.RBAC: Object(properties={DocumentLabels.OWNER_ID: Keyword()}),
            PlanFields.NAME: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL),
            PlanFields.RECURRENCE: Keyword(),
            PlanFields.NEXT_SCHEDULED_AT: Date(),
            PlanFields.TEMPLATE_ID: Keyword(),
            PlanFields.NOTE: Text(copy_to=OS_LABEL_CATCH_ALL),
            PlanFields.ARCHIVED_AT: Date(),
            PlanFields.MAX_COMPLETED: Integer(),
            PlanFields.CURRENT_COMPLETED: Integer(),
            PlanFields.TOTAL_CYCLES: Integer(),
            DocumentLabels.TAGS: Object(
                properties={
                    DocumentLabels.TAG: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL)
                }
            ),
        }
        | plan_mapping
        | goal_mapping
        | streak_mapping
        | metadata_mapping
        | get_document_mapping(),
        strict_mapping=True,
    )


def get_plan_settings():
    return {
        "default_pipeline": None,
        "number_of_shards": settings.OS_PRIMARY_SHARDS_COUNT,
        "number_of_replicas": settings.OS_REPLICA_SHARDS_COUNT,
    }


PlanIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=PLAN_INDEX, mappings=get_plan_mapping(), settings=get_plan_settings(), is_splittable=False
)
