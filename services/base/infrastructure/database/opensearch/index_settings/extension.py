from typing import Any, Dict

from opensearchpy import <PERSON><PERSON><PERSON>, Date, Join, Keyword, Object, Text

from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.extension_output import (
    AnalyticMetadataFields,
    AnalyticResultFields,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_common_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.default_index_settings import (
    depr_get_default_index_settings,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.system_properties import (
    get_system_properties_mapping,
)
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import (
    EXTENSION_RESULTS_INDEX,
    OpenSearchIndex,
)
from services.base.infrastructure.database.opensearch.opensearch_pipeline_constants import SPLIT_INDEX_PIPELINE


def get_extension_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            OS_LABEL_CATCH_ALL: Text(),
            # Common fields for both runs and outputs
            AnalyticResultFields.TYPE: Keyword(),
            AnalyticResultFields.TIMESTAMP: Date(),
            AnalyticResultFields.METADATA: Object(
                properties={
                    AnalyticMetadataFields.USER_UUID: Keyword(),
                    AnalyticMetadataFields.EXTENSION_ID: Keyword(),
                    AnalyticMetadataFields.PROVIDER_ID: Keyword(),
                    AnalyticMetadataFields.URGENT: Boolean(copy_to=OS_LABEL_CATCH_ALL),
                    AnalyticMetadataFields.IMPORTANT: Boolean(copy_to=OS_LABEL_CATCH_ALL),
                    AnalyticMetadataFields.FAVORITED_AT: Date(),
                }
            ),
            **get_system_properties_mapping(),
            **get_common_mapping(),
            # Run specific fields
            AnalyticResultFields.RUN_SUMMARY: Text(copy_to=OS_LABEL_CATCH_ALL),
            AnalyticResultFields.EXTENSION_INPUT: Object(),
            AnalyticResultFields.MESSAGE: Text(copy_to=OS_LABEL_CATCH_ALL),
            AnalyticResultFields.SUMMARY: Text(copy_to=OS_LABEL_CATCH_ALL),
            AnalyticResultFields.EXTENSION_STATUS: Keyword(),
            AnalyticResultFields.RESULT_STATUS: Boolean(),
            # Result specific Fields
            AnalyticResultFields.OUTPUT: Text(copy_to=OS_LABEL_CATCH_ALL),
            "join_field": Join(relations={DataType.ExtensionRun: DataType.ExtensionResult}),
        }
    )


def get_extension_settings():
    return {"default_pipeline": None, **depr_get_default_index_settings()}


ExtensionResultsIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=EXTENSION_RESULTS_INDEX,
    mappings=get_extension_mapping(),
    settings=get_extension_settings(),
    is_splittable=True,
    pipeline=SPLIT_INDEX_PIPELINE,
)
