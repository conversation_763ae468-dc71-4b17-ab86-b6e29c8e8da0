import json
import logging
from datetime import datetime
from typing import Optional, Sequence, Union
from uuid import UUID
from zoneinfo import ZoneInfo

from opensearchpy import OpenSearch

from services.base.application.utils.encoders import json_serializer
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.data_types import DataType
from services.base.domain.schemas.events.event import Event
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.time import parse_datetime_tz_fallback
from services.base.infrastructure.database.opensearch.opensearch_constants import DEFAULT_INTERVAL
from services.base.infrastructure.database.opensearch.query_methods.utils import (
    generate_sub_aggregation,
    get_str_timezone_offset,
)
from services.base.infrastructure.database.opensearch.query_translator.query_translator import QueryTranslator


class OpenSearchWrapper:

    @staticmethod
    def build_aggregation_query(
        requested_fields_and_agg: Sequence[tuple[str, str] | str] | None = None,
        time_gte: Union[datetime, str] | None = None,
        time_lte: Union[datetime, str] | None = None,
        interval: str = DEFAULT_INTERVAL,
        custom_sub_aggs: dict | None = None,
        time_field_name: str = DocumentLabels.TIMESTAMP,
        timezone: ZoneInfo | None = None,
        min_doc_count: int | None = None,
    ):
        if requested_fields_and_agg is None:
            requested_fields_and_agg = []

        if custom_sub_aggs:
            sub_aggregations = custom_sub_aggs
        else:
            sub_aggregations = generate_sub_aggregation(requested_fields_and_agg=requested_fields_and_agg)

        date_histogram = {
            "field": time_field_name,
            # Ensures that all buckets for requested calendar interval are returned, even with no values
            "extended_bounds": {"min": time_gte, "max": time_lte},
        }

        if min_doc_count is not None:
            date_histogram["min_doc_count"] = min_doc_count

        if int(interval[:-1]) == 1:
            date_histogram["calendar_interval"] = interval
        else:
            date_histogram["fixed_interval"] = interval

        # TODO(jaja): should be refactored with composite aggregation
        aggregations = {
            "requested_histogram": {
                "date_histogram": date_histogram,
                # aggs is short for aggregations
                "aggs": sub_aggregations,
            }
        }

        dt = datetime.now(
            tz=(
                timezone
                if timezone
                else time_lte.tzinfo if (time_lte and isinstance(time_lte, datetime)) else ZoneInfo("UTC")
            )
        )
        aggregations["requested_histogram"]["date_histogram"]["time_zone"] = get_str_timezone_offset(dt=dt)

        return aggregations


def get_fields(user_uuid: UUID, client: OpenSearch, data_type: DataType, size: int = 1000) -> dict:
    """Performs a query against opensearch in the given index, and returns
    the specified fields from each document in the results
    Args:
        user_uuid (str): user unique id
        data_type (DataType): specified DataType
        client (OpenSearch): client connection object to OpenSearch
        size (int): number of entries to return
    Returns:
        dict: {"Values": {field0: [value0, valueN], fieldN: [value0, valueN]}}
    """
    if issubclass(data_type.to_domain_model(), Event):
        # V3
        user_values_query = CommonLeafQueries.owner_id_value_query(user_uuid=user_uuid)
    else:
        # V2
        user_values_query = CommonLeafQueries.metadata_user_uuid_value_query(user_uuid=user_uuid)

    query = Query(type_queries=[TypeQuery(domain_types=[data_type.to_domain_model()], query=user_values_query)])
    query_translation_result = QueryTranslator.translate(query=query)

    sort = {"sort": [{DocumentLabels.TIMESTAMP: {"order": "desc"}}]}
    body = query_translation_result.query_as_dict or {}
    body = body | sort

    logging.info(f"Searching data types {data_type} with query {json.dumps(body, default=json_serializer)}")
    results = client.search(
        body=body,
        params={"size": size},
        index=query_translation_result.indices,
    )
    return results


def get_last_entry(user_uuid: UUID, client: OpenSearch, data_type: DataType):
    return get_fields(user_uuid=user_uuid, client=client, data_type=data_type, size=1)


def get_last_timestamp(
    user_uuid: UUID,
    client: OpenSearch,
    data_type: DataType,
) -> Optional[datetime]:
    """returns datetime.datetime object (with timezone) or None when timestamp is not in the last record"""

    last_entry = get_last_entry(user_uuid=user_uuid, client=client, data_type=data_type)
    try:
        return parse_datetime_tz_fallback(str(last_entry["hits"]["hits"][0]["_source"][DocumentLabels.TIMESTAMP]))

    except (KeyError, IndexError):
        logging.info(
            "Last entry of datatype %s does not have expected structure: %s - assuming no user data in index.",
            data_type,
            last_entry,
        )
        return None
