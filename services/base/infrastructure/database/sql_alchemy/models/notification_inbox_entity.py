from datetime import datetime
from uuid import UUI<PERSON>, uuid4

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Integer, String, Text, func
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import expression
from sqlalchemy_utils import UUIDType

from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity


class NotificationInboxEntity(BaseEntity):
    __tablename__ = "notification_inbox"

    notification_uuid: Mapped[UUID] = mapped_column(
        UUIDType(binary=False),
        index=True,
        primary_key=True,
        default=uuid4,
        nullable=False,
    )
    user_uuid: Mapped[UUID] = mapped_column(
        UUIDType(binary=False),
        ForeignKey("member_user.user_uuid", ondelete="CASCADE"),
        nullable=False,
    )

    timestamp: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    title: Mapped[str] = mapped_column(String(100), nullable=False)
    message: Mapped[str] = mapped_column(Text(), nullable=False)
    type: Mapped[str] = mapped_column(String(50), nullable=False)
    status: Mapped[str] = mapped_column(String(50), nullable=False)
    priority: Mapped[str] = mapped_column(String(50), nullable=False)
    urgent: Mapped[bool] = mapped_column(Boolean(), nullable=False, server_default=expression.false())
    important: Mapped[bool] = mapped_column(Boolean(), nullable=False, server_default=expression.false())

    end_time: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    duration: Mapped[int] = mapped_column(Integer(), nullable=True)
    description: Mapped[str] = mapped_column(Text(), nullable=True)
    image_url: Mapped[str] = mapped_column(Text(), nullable=True)
    action: Mapped[str] = mapped_column(Text(), nullable=True)
