import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Any, AsyncIterable, Callable, Optional, Sequence, Type

from asyncpg import PostgresError
from sqlalchemy import Table, func, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import Async<PERSON><PERSON><PERSON>, AsyncResult, AsyncSession
from sqlalchemy.sql import Select

from services.base.application.retry import retry
from services.base.domain.repository.wrappers import (
    Range,
    ReadFromDatabaseWrapper,
    SortOrder,
)
from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity


class SqlAlchemyAsyncCommons:
    @staticmethod
    @retry(exceptions=(SQLAlchemyError, PostgresError))
    async def commit_to_database(session_maker: Callable[..., AsyncSession], entities: Sequence[BaseEntity]):
        async with session_maker() as db_session:
            db_session.add(entities)
            await db_session.commit()
            return entities

    @staticmethod
    @retry(exceptions=(SQLAlchemyError, PostgresError))
    async def get_streaming_results[T: BaseEntity](
        engine: AsyncEngine, query: Select, entity_type: Type[T], size=1000
    ) -> AsyncIterable[Sequence[T]]:
        async with engine.connect() as connection:
            connection = await connection.execution_options(stream_results=True)
            result: AsyncResult = await connection.stream(query)
            while chunk := await result.mappings().fetchmany(size):
                yield [entity_type(**o) for o in chunk]

    @staticmethod
    def apply_search_keys(query: Select, entity_type: Type[BaseEntity], search_keys: dict[str, Any]) -> Select:
        for attr, value in search_keys.items():
            # Check if the value is a list or tuple for IN clause
            if isinstance(value, (list, tuple)):
                query = query.where(getattr(entity_type, attr).in_(value))
            else:
                query = query.where(getattr(entity_type, attr) == value)
        return query

    @staticmethod
    def apply_range_filter(query: Select, entity_type: Type[BaseEntity], range_filter: Optional[Range]) -> Select:
        if range_filter:
            query = query.filter(
                getattr(entity_type, range_filter.field_name).between(
                    (
                        range_filter.start_date
                        if range_filter.start_date
                        else datetime.now(timezone.utc) - timedelta(days=36500)
                    ),
                    range_filter.end_date if range_filter.end_date else datetime.now(timezone.utc),
                )
            )
        return query

    @staticmethod
    def apply_exists(query: Select, entity_type: Type[BaseEntity], exists: dict[str, bool] | None) -> Select:
        if exists:
            for attr, value in exists.items():
                if value:
                    query = query.filter(getattr(entity_type, attr).isnot(None))
                else:
                    query = query.filter(getattr(entity_type, attr).is_(None))
        return query

    @staticmethod
    def build_query_from_wrapper(entity_type: Type[BaseEntity], wrapper: ReadFromDatabaseWrapper) -> Select:
        if wrapper.count:
            if wrapper.offset is not None or wrapper.limit is not None or wrapper.sort is not None:
                raise ValueError("Offset, limit and sort can't be combined with count queries.")
            if wrapper.count_unique_field:
                query = select(func.count(func.distinct(getattr(entity_type, wrapper.count_unique_field)))).select_from(
                    entity_type
                )
            else:
                query = select(func.count()).select_from(entity_type)

            query = SqlAlchemyAsyncCommons.apply_search_keys(query, entity_type, wrapper.search_keys)
            query = SqlAlchemyAsyncCommons.apply_range_filter(query, entity_type, wrapper.range_filter)
            query = SqlAlchemyAsyncCommons.apply_exists(query, entity_type, wrapper.exists)

            return query
        else:
            query: Select = select(entity_type)
            query = SqlAlchemyAsyncCommons.apply_search_keys(query, entity_type, wrapper.search_keys)
            query = SqlAlchemyAsyncCommons.apply_range_filter(query, entity_type, wrapper.range_filter)
            query = SqlAlchemyAsyncCommons.apply_exists(query, entity_type, wrapper.exists)

            if wrapper.limit:
                query = query.limit(wrapper.limit)
            if wrapper.offset:
                query = query.offset(wrapper.offset)
            if wrapper.sort:
                sort_order = getattr(entity_type, wrapper.sort.name)
                query = query.order_by(sort_order.desc() if wrapper.sort.order == SortOrder.DESC else sort_order.asc())

            return query

    @staticmethod
    @retry(exceptions=(SQLAlchemyError, PostgresError))
    async def read_from_databases[T: BaseEntity](
        session_maker: Callable[..., AsyncSession], query: Select, entity_type: Type[T]
    ) -> Sequence[T]:
        async with session_maker() as db_session:
            result = await db_session.execute(query)
            results = result.scalars().all()
            return results

    @staticmethod
    @retry(exceptions=(SQLAlchemyError, PostgresError))
    async def get_by_primary_keys[T: BaseEntity](
        session_maker: Callable[..., AsyncSession],
        entity_type: Type[T],
        primary_keys: Sequence[str | dict],
    ) -> Sequence[T]:
        async with session_maker() as db_session:
            entities = await asyncio.gather(*(db_session.get(entity_type, key) for key in primary_keys))
            return [entity for entity in entities if entity is not None]

    @staticmethod
    @retry(exceptions=(SQLAlchemyError, PostgresError))
    async def delete_from_database(session_maker: Callable[..., AsyncSession], entities: Sequence[BaseEntity]):
        async with session_maker() as db_session:
            if not entities:
                return

            pks = []
            db_model: Type[BaseEntity] = entities[0].__class__
            table_object = db_model.__table__
            assert isinstance(table_object, Table), "Expected table_object to be of type Table"

            for o in entities:
                pk = {}
                for pk_column in table_object.primary_key.columns:
                    pk[pk_column.name] = getattr(o, str(pk_column.name))
                pks.append(pk)
            logging.info("Deleting entities %s with primary key: %s", db_model.__name__, pks)
            objects = await SqlAlchemyAsyncCommons.get_by_primary_keys(
                session_maker=session_maker, entity_type=db_model, primary_keys=pks
            )
            for val in objects:
                await db_session.delete(val)

            await db_session.commit()

    @staticmethod
    @retry(exceptions=(SQLAlchemyError, PostgresError))
    async def merge[T: BaseEntity](session_maker: Callable[..., AsyncSession], entities: Sequence[T]):
        async with session_maker() as db_session:
            output = [await db_session.merge(instance=instance) for instance in entities]
            await db_session.commit()
            return output
