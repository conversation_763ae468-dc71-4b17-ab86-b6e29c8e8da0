from typing import Async<PERSON><PERSON><PERSON>, Callable, Optional, Sequence
from uuid import UUID

from sqlalchemy.ext.asyncio import Async<PERSON>ng<PERSON>, AsyncSession

from services.base.domain.repository.member_user_settings_repository import MemberUserSettingsRepository
from services.base.domain.repository.wrappers import ReadFromDatabaseWrapper
from services.base.domain.schemas.member_user.member_user_settings import MemberUserSettings
from services.base.infrastructure.database.sql_alchemy.models.user_settings_entity import UserSettingsEntity
from services.base.infrastructure.database.sql_alchemy.repository.sql_repositories_helper import (
    SqlAlchRepositoriesHelper,
)
from services.base.infrastructure.database.sql_alchemy.sql_alch_async_commons import SqlAlchemyAsyncCommons


class MemberUserSettingsMapper:

    @staticmethod
    def to_domain(entities: Sequence[UserSettingsEntity]) -> Sequence[MemberUserSettings]:
        output = []
        for settings in entities:
            if settings:
                output.append(MemberUserSettings.model_validate(obj=settings))
        return output

    @staticmethod
    def to_entity(domain_models: Sequence[MemberUserSettings]) -> Sequence[UserSettingsEntity]:
        output = []
        for settings in domain_models:
            if settings:
                output_settings = UserSettingsEntity(user_uuid=settings.user_uuid)
                if settings.profile is not None:
                    output_settings.profile = settings.profile.model_dump_json(exclude_none=True)
                if settings.privacy is not None:
                    output_settings.privacy = settings.privacy.model_dump_json(exclude_none=True)
                if settings.general is not None:
                    output_settings.general = settings.general.model_dump_json(exclude_none=True)
                if settings.favorites is not None:
                    output_settings.favorites = settings.favorites.model_dump_json(exclude_none=True)

                output.append(output_settings)

        return output


class SqlAlchMemberUserSettingsRepository(MemberUserSettingsRepository):

    def __init__(self, session_maker: Callable[..., AsyncSession], engine: AsyncEngine):
        self._session_maker = session_maker
        self._engine = engine

    async def get_by_uuid(self, user_uuid: UUID) -> Optional[MemberUserSettings]:
        models = MemberUserSettingsMapper.to_domain(
            entities=await SqlAlchemyAsyncCommons.get_by_primary_keys(
                session_maker=self._session_maker, entity_type=UserSettingsEntity, primary_keys=[str(user_uuid)]
            )
        )
        return next(iter(models), None)

    async def insert_or_update(self, settings: MemberUserSettings) -> Optional[MemberUserSettings]:
        models = MemberUserSettingsMapper.to_domain(
            entities=await SqlAlchemyAsyncCommons.merge(
                session_maker=self._session_maker,
                entities=MemberUserSettingsMapper.to_entity(domain_models=[settings]),
            )
        )
        return next(iter(models), None)

    def yield_results(
        self, wrapper: ReadFromDatabaseWrapper, size: int = 1000
    ) -> AsyncIterator[Sequence[MemberUserSettings]]:
        return SqlAlchRepositoriesHelper.yield_results(
            engine=self._engine,
            wrapper=wrapper,
            domain_type=MemberUserSettings,
            entity_type=UserSettingsEntity,
            size=size,
        )
