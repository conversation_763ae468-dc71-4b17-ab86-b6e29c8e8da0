from enum import Enum
from typing import Sequence, Type

from automapper import mapper

from services.base.domain.schemas.shared import BaseDataModel
from services.base.infrastructure.database.sql_alchemy.models.base_entity import BaseEntity


class SqlAlchMapper:
    @staticmethod
    def to_domain[T: BaseDataModel](entities: Sequence[BaseEntity], domain_type: Type[T]) -> Sequence[T]:
        output = []
        for entity in entities:
            if entity:
                output.append(domain_type.model_validate(entity))
        return output

    @staticmethod
    def to_entity[T: BaseEntity](domain_models: Sequence[BaseDataModel], entity_type: Type[T]) -> Sequence[T]:
        output = []
        for model in domain_models:
            if model:
                entity = mapper.to(entity_type).map(model)
                for field in vars(entity):
                    if isinstance(enum := getattr(entity, field), Enum):
                        setattr(entity, field, enum.value)
                output.append(entity)
        return output
