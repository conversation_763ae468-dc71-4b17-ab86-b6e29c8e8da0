from contextlib import asynccontextmanager, contextmanager
from typing import Async<PERSON><PERSON><PERSON>, Iterator

from sqlalchemy import URL, Engine, create_engine
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import Session, sessionmaker

from settings.app_config import settings
from settings.app_secrets import secrets


class DbStateManager:
    _engine: Engine
    _async_engine: AsyncEngine
    _session_maker: sessionmaker
    _async_session_maker: async_sessionmaker

    def __init__(self, db_url: URL, async_db_url: URL):
        self.db_url = db_url
        self.async_db_url = async_db_url

        self._engine = create_engine(db_url)
        self._session_maker = sessionmaker(self.engine)

        self._async_engine = create_async_engine(async_db_url)
        self._async_session_maker = async_sessionmaker(
            bind=self.async_engine, expire_on_commit=False, class_=AsyncSession
        )

    @property
    def engine(self) -> Engine:
        return self._engine

    @property
    def async_engine(self) -> AsyncEngine:
        return self._async_engine

    # FOR DEPENDENCY/FastAPI endpoints, USE THE PREPARED DEPENDENCY GENERATOR BELOW!
    # DOCS: https://docs.sqlalchemy.org/en/14/orm/session_api.html#sqlalchemy.orm.sessionmaker
    # Session object may be closed explicitly via the .close() method.
    # Using a try:/finally: block is optional, however:
    # will ensure that the close takes place even if there are database errors:
    # - this is the way get_db_session Dependency generator below works

    @property
    def session_maker(self) -> sessionmaker:
        return self._session_maker

    @property
    def async_session_maker(self) -> async_sessionmaker:
        return self._async_session_maker


db_state_manager = DbStateManager(
    db_url=URL.create(
        drivername="postgresql",
        username=secrets.PG_USER,
        password=secrets.PG_PASSWORD,
        database=settings.PG_NAME,
        host=settings.PG_HOST,
        port=settings.PG_PORT,
    ),
    async_db_url=URL.create(
        drivername="postgresql+asyncpg",
        username=secrets.PG_USER,
        password=secrets.PG_PASSWORD,
        database=settings.PG_NAME,
        host=settings.PG_HOST,
        port=settings.PG_PORT,
    ),
)


# Example usage in FastAPI:
# def some_endpoint(db_session: Session = Depends(get_db_session))
@contextmanager
def get_db_session() -> Iterator[Session]:
    """!GENERATOR! - use with Depends() as an endpoint argument"""
    db_session: Session = db_state_manager.session_maker()
    try:
        yield db_session
    finally:
        # when used properly as generator, this part is called after the generation FINISHED
        db_session.close()  # pylint:disable=no-member


@asynccontextmanager
async def get_async_db_session() -> AsyncIterator[AsyncSession]:
    # Code to acquire resource, e.g.:
    session: AsyncSession = db_state_manager.async_session_maker()
    await session.begin()
    try:
        yield session
    finally:
        # Code to release resource, e.g.:
        await session.close()
