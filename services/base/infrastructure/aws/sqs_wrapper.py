import json
import logging
from typing import Callable, Sequence

from botocore.exceptions import ClientError
from mypy_boto3_sqs import SQSServiceResource
from mypy_boto3_sqs.service_resource import Queue
from pydantic import ValidationError

from services.base.application.message_queue_client import MessageQueueClient
from services.base.message_queue.message_wrapper import MessageWrapper


class SQSWrapper(MessageQueueClient):
    def __init__(self, sqs_service: SQSServiceResource):
        self._sqs_service = sqs_service

    def get_queue_identifier(self, queue_name: str) -> str:
        queue = self._get_queue_by_name(queue_name=queue_name)
        return queue.attributes["QueueArn"]

    def consume_messages_from_topics(
        self,
        queue_name: str,
        max_number_of_messages: int,
        polling_time: int,
        message_handler: Callable[[MessageWrapper], None],
        message_attributes: Sequence[str],
    ) -> None:
        """Consume specified amount of messages (max 10) over some long polling interval (max 20) on specified queue
        See SQS docs
        https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/sqs.html#SQS.Message.message_attributes
        """
        queue = self._get_queue_by_name(queue_name=queue_name)
        if message_attributes is None:
            # If no expected message attribute is provided, listen to any message
            message_attributes = ["All"]
        try:
            messages = queue.receive_messages(
                MaxNumberOfMessages=max_number_of_messages,
                WaitTimeSeconds=polling_time,
                MessageAttributeNames=message_attributes,
            )
            for sqs_message in messages:
                try:
                    # Get message body out of sqs_message
                    message_dict = json.loads(sqs_message.body)
                    # Message Attributes are actually stored in message body
                    inner_message_attributes = message_dict.get("MessageAttributes", {})
                    # Attributes as well
                    attributes = message_dict.get("Attributes", {})
                    # The actual message content
                    message = json.loads(message_dict.get("Message", {}))

                    message_wrapper: MessageWrapper = MessageWrapper(
                        message_id=sqs_message.message_id,
                        queue_url=sqs_message.queue_url,
                        attributes=attributes,
                        message_body=message,
                        message_attributes=inner_message_attributes,
                    )
                    message_handler(message_wrapper)
                    sqs_message.delete()
                except (TypeError, ValidationError) as err:
                    logging.exception("Could not parse sqs_message to MessageWrapper object, %s.", repr(err))
                except Exception as err:
                    logging.exception("Unhandled error has occurred, %s.", repr(err))

        except ClientError as error:
            logging.exception("Couldn't receive messages from queue: %s, %s", queue_name, repr(error))
            raise error

    def publish_to_queue(self, queue_name: str, message_body: dict, message_attributes: dict | None = None):
        """
        Send a message to an Amazon SQS queue.

        :param queue_name: The queue that receives the message.
        :param message_body: The body text of the message.
        :param message_attributes: Custom attributes of the message with specific format:
            https://boto3.amazonaws.com/v1/documentation/api/latest/guide/sqs.html
        :return: The response from SQS that contains the assigned message ID.
        """
        if message_attributes is None:
            message_attributes = {}
        queue = self._get_queue_by_name(queue_name=queue_name)
        logging.info(
            "Publishing message %s with attributes %s to queue %s", message_body, message_attributes, queue_name
        )
        try:
            queue.send_message(MessageBody=json.dumps(message_body), MessageAttributes=message_attributes)
        except ClientError as error:
            logging.exception("Sending message failed: %s", message_body)
            raise error

    def upsert_queue_access_policy(self, queue_name: str, resource_identifier: str):
        queue = self._get_queue_by_name(queue_name=queue_name)
        queue_attributes = queue.attributes
        queue_arn = queue_attributes["QueueArn"]
        queue_policy: dict = json.loads(queue_attributes.get("Policy", "{}"))
        new_access_policy = self._get_queue_access_policy(queue_arn=queue_arn, arn=resource_identifier)

        if not queue_policy:
            queue_policy = {
                "Version": "2008-10-17",
                "Id": "__default_policy_ID",
                "Statement": [new_access_policy],
            }
        else:
            if new_access_policy not in queue_policy["Statement"]:
                queue_policy["Statement"].append(new_access_policy)

        queue_policy = {"Policy": json.dumps(queue_policy)}
        queue.set_attributes(Attributes=queue_policy)

    def _get_queue_access_policy(self, queue_arn: str, arn: str) -> dict:
        return {
            "Sid": f"topic-subscription-{queue_arn}",
            "Effect": "Allow",
            "Principal": {"AWS": "*"},
            "Action": "SQS:SendMessage",
            "Resource": queue_arn,
            "Condition": {"ArnLike": {"aws:SourceArn": arn}},
        }

    def _get_queue_by_name(self, queue_name: str) -> Queue:
        return self._sqs_service.get_queue_by_name(QueueName=queue_name)
