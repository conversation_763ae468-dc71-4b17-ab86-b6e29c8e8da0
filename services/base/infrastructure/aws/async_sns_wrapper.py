import logging

from aioboto3 import Session
from types_aiobotocore_sns import SNSClient, SNSServiceResource
from types_aiobotocore_sns.service_resource import Topic

from services.base.application.async_message_broker_client import AsyncMessageBrokerClient
from services.base.application.retry import retry
from services.base.infrastructure.aws.service_provider import AWSServiceProvider


class AsyncSNSWrapper(AsyncMessageBrokerClient):
    def __init__(self, session: Session):
        self._session = session

    async def get_topic_identifier(self, topic_name: str) -> str:
        topic: Topic = await self.create_or_get_topic(topic_name=topic_name)
        return topic.arn

    async def publish_topic(self, topic_name: str, message_body: str, message_attributes: dict | None = None):
        if message_attributes is None:
            message_attributes = {}
        topic = await self.create_or_get_topic(topic_name=topic_name)
        sns_client: SNSClient
        async with self._session.client("sns", **AWSServiceProvider.get_common_args()) as sns_client:
            await sns_client.publish(TopicArn=topic.arn, Message=message_body, MessageAttributes=message_attributes)
            logging.info(
                "Published message %s with attributes %s to topic %s", message_body, message_attributes, topic_name
            )

    @retry()
    async def create_or_get_topic(self, topic_name: str) -> Topic:
        logging.info("Creating topic: %s", topic_name)
        sns_resource: SNSServiceResource
        async with self._session.resource("sns", **AWSServiceProvider.get_common_args()) as sns_resource:
            return await sns_resource.create_topic(Name=topic_name)
